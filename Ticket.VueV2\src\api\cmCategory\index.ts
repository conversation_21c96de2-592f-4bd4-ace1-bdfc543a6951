﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class cmCategoryApi extends BaseApi {
	GetDetailByIdAndCompanyId(id: any, companyId: any) {
		return request({
			url: this.baseurl + 'GetDetailByIdAndCompanyId/' + id + '/' + companyId,
			method: 'get',
			params: {},
		});
	}

	GetDetailByIdAndCompanyIdV2(id: any, companyId: any, cmCategoryTalbeRelationId: any) {
		return request({
			url: this.baseurl + 'GetDetailByIdAndCompanyId',
			method: 'post',
			data: {
				id,
				companyId,
				cmCategoryTalbeRelationId,
			},
		});
	}

	GetListByUserId(data: any) {
		return request({
			url: this.baseurl + 'GetCategoryByUserId',
			method: 'post',
			data,
		});
	}

	// 删除指定数据
	DeleteCategoryByKey(data: any) {
		return request({
			url: this.baseurl + 'DeleteCategory',
			method: 'post',
			data,
		});
	};

	UpdateCmCategoryCode(data?: any) {
		return request({
			url: this.baseurl + 'UpdateCmCategoryCode',
			method: 'post',
			data,
		});
	}

	QueryForAddCompany(data: any) {
		return request({
			url: this.baseurl + 'QueryForAddCompany',
			method: 'post',
			data,
		});
	}
}

export default new cmCategoryApi('/api/cmCategory/', 'id');
