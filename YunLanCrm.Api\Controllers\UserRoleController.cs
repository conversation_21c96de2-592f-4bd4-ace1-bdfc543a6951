﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.UserRole;
using YunLanCrm.Common.Helper;
using YunLanCrm.Dto.User;
using YunLanCrm.Dto.CmTickets;
using YunLanCrm.Services;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class UserRoleController : ControllerBase
    {
        private readonly ILogger<UserRoleInfo> _logger;
        private readonly IUserService _userService;
        private readonly IUserRoleService _userRoleService;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="userRoleService"></param>
        /// <param name="logger"></param>
        public UserRoleController(IUserRoleService userRoleService, IUserService userService, ILogger<UserRoleInfo> logger)
        {
            this._userRoleService = userRoleService;
            this._logger = logger;
            _userService = userService;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        [NonAction]
        public async Task<long> Add(UserRoleAddDto req)
        {
            return await _userRoleService.AddIdentity(req.Adapt<UserRoleInfo>());
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        [NonAction]
        public async Task<bool> Update(UserRoleEditDto req)
        {
            return await _userRoleService.Update(req.Adapt<UserRoleInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        [NonAction]
        public async Task<bool> Delete(int id)
        {
            int result = await _userRoleService.Delete(a => a.Id == id);

            return result > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        [NonAction]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _userRoleService.DeleteByIds(items);

            return result;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        [NonAction]
        public async Task<UserRoleDto> Get(int id)
        {
            return await _userRoleService.QueryInfo<UserRoleDto>(a => a.Id == id);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        [NonAction]
        public async Task<UserRoleDetailDto> Detail(int id)
        {
            var obj = await _userRoleService.QueryInfo(a => a.Id == id);

            if (obj != null)
            {
                return _userRoleService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        [NonAction]
        public async Task<List<UserRoleListDto>> List([FromQuery] UserRoleListQueryDto req)
        {
            var list = new List<UserRoleListDto>();
            var where = PredicateBuilder.New<UserRoleInfo>(true);
            var orderBy = new OrderBy<UserRoleInfo>(req.order, req.sort);
            var data = await _userRoleService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _userRoleService.Join(item);
                list.Add(detail.Adapt<UserRoleListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        [NonAction]
        public async Task<PageQueryResult<UserRoleListDto>> Query([FromQuery] UserRolePageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        [NonAction]
        public async Task<PageQueryResult<UserRoleListDto>> QueryPost(UserRolePageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<UserRoleListDto>> PageQuery([FromQuery] UserRolePageQueryDto req)
        {
            var list = new List<UserRoleListDto>();
            var where = PredicateBuilder.New<UserRoleInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            if (req.roleId.HasValue)
            {
                where.And(a => a.RoleId == req.roleId.Value);
            }

            if (req.userId.HasValue)
            {
                where.And(a => a.UserId == req.userId.Value);
            }
            //用于导出
            if (req.ids != null && req.ids.Count > 0)
            {
                where.And(a => req.ids.Contains(a.UserId.ToString()));
            }
            var totalCount = await _userRoleService.CountAsync(where);
            var orderBy = new OrderBy<UserRoleInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _userRoleService.QueryList(where, orderBy, paging);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _userRoleService.Join(item);
                list.Add(detail.Adapt<UserRoleListDto>());
            }
            #endregion

            return new PageQueryResult<UserRoleListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }


        /// <summary>
        /// 获取角色成员
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("GetRoleUsers")]
        public async Task<List<Dto.User.UserDetailDto>> GetRoleUsers([FromQuery] UserRolePageQueryDto req)
        {
            List<Dto.User.UserDetailDto> list = new List<Dto.User.UserDetailDto>();
            var rs = await PageQuery(req);
            var users = rs.Data?.Select(a => a.UserId).ToList();
            var userList = await _userService.QueryList(a => users.Contains(a.Id) && a.IsDeleted == false);

            foreach (var item in userList)
            {
                list.Add(_userService.Join(item));
            }

            return list;
        }

        /// <summary>
        /// 解除角色用户
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("RemoveRoleUser")]
        public async Task<bool> RemoveRoleUser(RemoveRoleUserDto req)
        {
            var result = await _userRoleService.Delete(a => a.RoleId == req.RoleId && a.UserId == req.UserId);

            return result > 0;
        }

        [HttpPost("Export")]
        public async Task<IActionResult> Export(UserRolePageQueryDto req)
        {
            var isAllCheck = req.ischeckType == 0 ? true : false;
            return await ExcelHelper.Export<Task<List<Dto.User.UserDetailDto>>>(this, typeof(IUserRoleService).Name, "GetRoleUsers", this, isAllCheck, req);
        }


    }
}