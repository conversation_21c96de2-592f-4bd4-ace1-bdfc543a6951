﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class userDelegate<PERSON>pi extends BaseApi {
    GetSelectUserDelegate(params?: any) {
        return request({
            url: this.baseurl + 'GetSelectUserDelegate',
            method: 'get',
            params,
        });
    };
	UpdateIsApprove(data: any) {
		return request({
			url: this.baseurl + 'UpdateIsApprove',
			method: 'post',
			data,
		});
	}

        UpdateIsApproveBatch(data: any) {
        return request({
            url: this.baseurl + 'UpdateIsApproveBatch',
            method: 'post',
            data,
        });
    }

}

export default new userDelegateApi('/api/userDelegate/','id');




                        
        
        