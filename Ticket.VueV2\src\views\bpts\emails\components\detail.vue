﻿<template>
    <el-main style="padding:0 20px;">
        <el-descriptions :column="1" border size="small">
           
 <el-descriptions-item label="id">{{ info.id }}</el-descriptions-item>
 <el-descriptions-item label="subject">{{ info.subject }}</el-descriptions-item>
 <el-descriptions-item label="body">{{ info.body }}</el-descriptions-item>
 <el-descriptions-item label="fromAddr">{{ info.fromAddr }}</el-descriptions-item>
 <el-descriptions-item label="fromName">{{ info.fromName }}</el-descriptions-item>
 <el-descriptions-item label="toAddrs">{{ info.toAddrs }}</el-descriptions-item>
 <el-descriptions-item label="ccAddrs">{{ info.ccAddrs }}</el-descriptions-item>
 <el-descriptions-item label="dataType">{{ info.dataType }}</el-descriptions-item>
 <el-descriptions-item label="status">{{ info.status }}</el-descriptions-item>
 <el-descriptions-item label="isDeleted">{{ info.isDeleted }}</el-descriptions-item>
 <el-descriptions-item label="createById">{{ info.createById }}</el-descriptions-item>
 <el-descriptions-item label="createTime">{{ info.createTime }}</el-descriptions-item>
 <el-descriptions-item label="modifiedById">{{ info.modifiedById }}</el-descriptions-item>
 <el-descriptions-item label="modifiedTime">{{ info.modifiedTime }}</el-descriptions-item>
 <el-descriptions-item label="description">{{ info.description }}</el-descriptions-item>
        </el-descriptions>
    </el-main>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent } from 'vue';
import { formatStrDate } from '/@/utils/formatTime';

export default defineComponent({
    name: 'apiDetail',
    props: {
        info: Object
    },
    setup(props) {
        const state = reactive({
            isShowDialog: false,
            obj: {},
        });
        // 页面加载时
        onMounted(() => {
           window.console.log("props", props.info)
        });
        return {
            ...toRefs(state),
        };
    },
});
</script>


                        
        
        