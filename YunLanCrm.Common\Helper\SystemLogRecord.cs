﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace YunLanCrm.Common.Helper
{
    public class SystemLogRecord
    {
        //读写锁，当资源处于写入模式时，其他线程写入需要等待本次写入结束之后才能继续写入
        private static ReaderWriterLockSlim LogWriteLock = new ReaderWriterLockSlim();
        public static void createLog(string content, string logTxtName, string directoryName, bool folderWithDateTime, bool contentWithDateTime)
        {
            try
            {
                string txtPath = AppDomain.CurrentDomain.BaseDirectory + string.Format("{0}\\", directoryName);

                if (folderWithDateTime)
                {
                    txtPath += DateTime.Now.ToString("yyyy-MM-dd");
                }

                if (!Directory.Exists(txtPath))
                {
                    Directory.CreateDirectory(txtPath);
                }

                //todo:自动创建文件（但不能创建文件夹），并设置文件内容追加模式，使用using会自动释放FileSteam资源
                LogWriteLock.EnterWriteLock();

                txtPath = txtPath + "\\" + logTxtName;

                if (!File.Exists(txtPath))
                {
                    File.Create(txtPath).Close();
                }

                using (FileStream stream = new FileStream(txtPath, FileMode.Append))
                {
                    StreamWriter write = new StreamWriter(stream);

                    if (contentWithDateTime)
                    {
                        content = content + " " + DateTime.Now;
                    }

                    write.WriteLine(content);

                    //关闭并销毁流写入文件
                    write.Close();
                    write.Dispose();
                }
            }
            catch (Exception)
            {

            }
            finally
            {
                LogWriteLock.ExitWriteLock();
            }
        }
    }
}
