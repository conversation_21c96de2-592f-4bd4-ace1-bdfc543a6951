﻿<template>
	<div class="role-tree-container role-index-tree">
		<div class="role-tree-left">
			<el-card shadow="never" class="h100">
				<template #header>
					<div class="card-header">
						<h3>Roles</h3>
						<el-button v-auth="'systemRole.Create'" type="success" round size="small" @click="onAdd">
							{{ $t('message.page.buttonCreate') }}
						</el-button>
					</div>
				</template>
				<el-menu :default-openeds="[0, 1, 2, 3]" default-active="0-0">
					<el-sub-menu v-for="(item, index) in roleGroupData" :key="item.groupId" :index="index">
						<template #title>
							<div class="group-title">{{ $t(item.groupName) }}</div>
						</template>
						<el-menu-item-group>
							<el-menu-item class="tab-item" v-for="(role, i) in item.roles" :key="role.id"
								:index="index + '-' + i" @click="onTreeClick(role)">
								<span>
									<i class="bi bi-person-vcard-fill"></i>
									{{ role.name }}
								</span>
								<el-dropdown v-auths="['systemRole.Edit', 'systemRole.Delete']">
									<span class="el-dropdown-link" stye="color:#409eff;height:1px !important;">
										<el-icon><ele-Edit /></el-icon>
										<el-icon class="el-icon--right"><arrow-down /></el-icon>
									</span>
									<template #dropdown>
										<el-dropdown-menu>
											<Auth :value="'systemRole.Edit'">
												<el-dropdown-item @click="onEdit(role)">
													{{ $t('message.page.actionsEdit') }}
												</el-dropdown-item>
											</Auth>

											<Auth :value="'systemRole.Delete'">
												<el-dropdown-item style="color: red" @click="onDelete(role)">
													{{ $t('message.page.actionsDelete') }}
												</el-dropdown-item>
											</Auth>
										</el-dropdown-menu>
									</template>
								</el-dropdown>
							</el-menu-item>
						</el-menu-item-group>
					</el-sub-menu>
				</el-menu>
			</el-card>
		</div>
		<div class="role-tree-right">
			<el-card shadow="never" class="h100">
				<template #header>
					<div class="card-header">
						<div class="left-panel">
							<span class="role-index-header-title">{{ currentObj.name }}</span>
						</div>
						<div class="right-panel">
							<Auth :value="activeName == 'RolePermission' ? 'systemRole.Edit' : 'systemRole.Export'">
								<el-button @click="onRolePermission" :loading="savePerLoading"
									v-if="activeName == 'RolePermission'" type="primary" size="small">
									{{ $t('message.page.buttonSave') }}
								</el-button>

								<el-dropdown v-else>
									<el-button type="primary" size="small" class="ml10">
										<el-icon>
											<ele-ArrowDownBold />
										</el-icon>
										{{ $t('message.page.buttonExport') }}
									</el-button>
									<template #dropdown>
										<el-dropdown-menu>
											<el-dropdown-item @click="onExportAllRecord(0)">{{
												$t('message.page.buttonExportEntireList') }}</el-dropdown-item>
											<el-dropdown-item @click="onExportAllRecord(1)">{{
												$t('message.page.buttonExportSelectedRecords') }}</el-dropdown-item>
										</el-dropdown-menu>
									</template>
								</el-dropdown>
							</Auth>
						</div>
					</div>
				</template>
				<el-tabs v-model="activeName" class="demo-tabs" @tab-change="onTabChange">
					<el-tab-pane :label="$t('message.roleFields.privilegeGroupMember')" name="RoleUser">
						<RoleUsers ref="roleUsersRef" />
					</el-tab-pane>
					<el-tab-pane :label="$t('message.roleFields.PrivilegeConfiguration')" name="RolePermission">
						<PermissionTable ref="permissionTableRef" />
					</el-tab-pane>
				</el-tabs>
			</el-card>
		</div>

		<CreateOrEdit ref="createOrEditRef" @fetchData="onInit"></CreateOrEdit>
	</div>
</template>

<script lang="ts">
//import { useRouter } from 'vue-router';
import { toRefs, reactive, onMounted, ref, defineComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate } from '/@/utils/formatTime';

import roleApi from '/@/api/role/index';
import RoleUsers from './components/roleUsers.vue';
import PermissionTable from './components/permissionTable.vue';
import CreateOrEdit from './components/createOrEdit.vue';
import { useI18n } from 'vue-i18n';
import Auth from '/@/components/auth/auth.vue';

export default defineComponent({
	name: 'systemRole',
	components: { RoleUsers, PermissionTable, CreateOrEdit, Auth },
	setup() {
		const { t } = useI18n();

		//const router = useRouter();
		const roleUsersRef = ref();
		const permissionTableRef = ref();
		const createOrEditRef = ref();
		const state = reactive({
			roleGroupData: [], //角色组与权限
			currentObj: {}, //当前选择的角色
			activeName: 'RoleUser',
			savePerLoading: false,
			roleId: '',
		});

		//初始化
		const onInit = (roleId: any) => {
			roleApi.GetRoleGroups().then((rs) => {
				state.roleGroupData = rs.data;
				state.currentObj = rs.data[0]?.roles[0];

				if (roleId) {
					rs.data.map(function (value: any, index: any) {
						const selectRoles = value.roles.filter((a: any) => {
							return a.id == roleId;
						});

						if (selectRoles.length > 0) {
							state.currentObj = selectRoles[0];
						}
					});
				}
				setSelectData(state.currentObj);
			});
		};
		// 添加
		const onAdd = () => {
			createOrEditRef.value.openDialog({ action: 'Create' });
			//router.push('/role/Create');
		};
		// 修改
		const onEdit = (row: Object) => {
			createOrEditRef.value.openDialog({ action: 'Edit', id: row.id });
			//router.push('/role/edit/' + row.id);
		};
		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal: false,
			}).then(() => {
				roleApi
					.DeleteByKey(row.id)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((rs) => { });
			});
		};
		const onTreeClick = (node: any) => {
			setSelectData(node);
		};
		const setSelectData = (node: any) => {
			state.currentObj = node;
			permissionTableRef.value.refreshData(node);
			roleUsersRef.value.onInit(node.id);
		};

		const onRolePermission = () => {
			state.savePerLoading = true;
			permissionTableRef.value.onSave();

			const timeInterval = setInterval(function () {
				state.savePerLoading = false;
				//记得销毁定时器
				clearInterval(timeInterval);
			}, 3000);
		};
		const onExportAllRecord = (selectAll: number) => {
			roleUsersRef.value.onExportAllRecord(selectAll);
		};
		// 页面加载时
		onMounted(() => {
			onInit();
		});
		return {
			onAdd,
			onEdit,
			onDelete,
			onTreeClick,
			formatStrDate,
			onRolePermission,
			onExportAllRecord,
			roleUsersRef,
			permissionTableRef,
			createOrEditRef,
			onInit,
			...toRefs(state),
		};
	},
});
</script>

<style scoped lang="scss">
.role-tree-container {
	display: flex;
	height: 100%;
	padding: 10px;

	.role-tree-left,
	.role-tree-right {
		height: 100%;
	}

	.role-tree-right {
		flex: 1;
		margin-left: 10px;
	}

	.role-tree-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10px;
		/* 可根据需求调整 */
	}

	.role-tree-header-title {
		font-size: 16px;
		/* 可根据需求调整 */
		font-weight: bold;
		/* 可根据需求调整 */
	}
}

@media (max-width: 768px) {
	.role-tree-container {
		flex-direction: column;

		.el-menu {
			width: 100% !important;
		}

		.role-tree-left {
			width: 100%;
			/* 在小屏幕上，左边部分占据整个宽度 */
			margin-bottom: 10px;
		}

		.role-tree-right {
			margin-left: 0;
			width: 100%;
			/* 在小屏幕上，右边部分占据整个宽度 */
		}
	}
}
</style>
