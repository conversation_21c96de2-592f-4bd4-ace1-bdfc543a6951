﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class organizationApi extends BaseApi {
	Tree(params?: any) {
		return request({
			url: this.baseurl + 'Tree',
			method: 'get',
			params,
		});
	}

	TreePost(data?: any) {
		return request({
			url: this.baseurl + 'TreePost',
			method: 'POST',
			data,
		});
	}

	UserTree(params?: any) {
		return request({
			url: this.baseurl + 'UserTree',
			method: 'get',
			params,
		});
	}

	GetEndUserOrganization(params?: any) {
		return request({
			url: this.baseurl + 'GetEndUserOrganization',
			method: 'get',
			params,
		});
	}

	GetEndUserOrganizationByUserId(params?: any) {
		return request({
			url: this.baseurl + 'GetEndUserOrganizationByUserId',
			method: 'get',
			params,
		});
	}

	GetCompanyProjectTree(data?: any) {
		return request({
			url: this.baseurl + 'GetCompanyProjectTreeV2',
			method: 'post',
			data,
		});
	}

	GetCompanyProjectTreeV3(data?: any) {
		return request({
			url: this.baseurl + 'GetCompanyProjectTreeV3',
			method: 'post',
			data,
		});
	}

	GetCompanyProjectTreeByUserId(data?: any) {
		return request({
			url: this.baseurl + 'GetCompanyProjectTreeByUserIdV2',
			method: 'post',
			data,
		});
	}

	GetCompanyProjectTreeByUserIdV3(data?: any) {
		return request({
			url: this.baseurl + 'GetCompanyProjectTreeByUserIdV3',
			method: 'post',
			data,
		});
	}

	GetCompanyProjectTreeByUserIdForListSearch(data?: any) {
		return request({
			url: this.baseurl + 'GetCompanyProjectTreeByUserIdForListSearch',
			method: 'post',
			data,
		});
	}
}

export default new organizationApi('/api/organization/', 'orgId');
