﻿using YunLanCrm.Common;
using YunLanCrm.Common.Helper;
using YunLanCrm.IServices;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using YunLanCrm.Model;
using YunLanCrm.Common.Extensions;


namespace YunLanCrm.AuthHelper
{
    /// <summary>
    /// 权限授权处理器
    /// </summary>
    public class PermissionHandler : AuthorizationHandler<PermissionRequirement>
    {
        /// <summary>
        /// 验证方案提供对象
        /// </summary>
        public IAuthenticationSchemeProvider Schemes { get; set; }
        private readonly IRoleModulePermissionServices _roleModulePermissionServices;
        private readonly IHttpContextAccessor _accessor;
        private readonly ILogger<PermissionHandler> _logger;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="schemes"></param>
        /// <param name="roleModulePermissionServices"></param>
        /// <param name="accessor"></param>
        public PermissionHandler(IAuthenticationSchemeProvider schemes, IRoleModulePermissionServices roleModulePermissionServices,
            IHttpContextAccessor accessor,
            ILogger<PermissionHandler> logger)
        {
            _accessor = accessor;
            Schemes = schemes;
            _roleModulePermissionServices = roleModulePermissionServices;
            _logger = logger;
        }

        // 重写异步处理程序
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, PermissionRequirement requirement)
        {
            var httpContext = _accessor.HttpContext;

            try
            {
                // 获取系统中所有的角色和菜单的关系集合
                if (!requirement.Permissions.Any())
                {
                    var data = await _roleModulePermissionServices.RoleApis();
                    var list = new List<PermissionItem>();
                    // ids4和jwt切换
                    // ids4
                    if (Permissions.IsUseIds4)
                    {
                        list = (from item in data
                                select new PermissionItem
                                {
                                    Url = item.LinkUrl,
                                    Role = item.RoleId.ObjToString(),
                                }).ToList();
                    }
                    // jwt
                    else
                    {
                        //查询角色对应的 url [ {RoleId,uRL} ]
                        list = (from item in data
                                select new PermissionItem
                                {
                                    Url = item.LinkUrl,
                                    Role = item.RoleId.ObjToString(),
                                }).ToList();

                        //api 地址为空的 过滤掉
                        list = list.Where(a => a.Url != null).ToList();
                    }
                    requirement.Permissions = list;
                }

                if (httpContext != null)
                {
                    bool isSingle = false;
                    var singleUrl = string.Empty;
                    var pattern = string.Empty;
                    //  var input = string.Empty;

                    var questUrl = httpContext.Request.Path.Value.ToLower();

                    // 匹配 api/user/get/100 api/user/info/100 api/user/detail/100
                    //Regex r = new Regex(@"api\/[a-z]+\/(get|detail|info|delete|getdetailbyidandcompanyid)\/\d+(?:\/\d+)?");
                    Regex r = new Regex(@"api\/[a-zA-Z]+\/[a-zA-Z]+\/\w+(?:\/\w+)?", RegexOptions.IgnoreCase);

                    if (r.IsMatch(questUrl))
                    {
                        isSingle = true;
                        //questUrl = questUrl.Substring(0, questUrl.LastIndexOf("/") + 1);
                        //pattern = questUrl.Replace("//", "\\/") + "{[a-z]+}";

                        // 找到方法名后的第一个'/'位置
                        string[] parts = questUrl.Split('/');
                        if (parts.Length >= 3)
                        {
                            // 重新组合到方法名为止：api/controller/method
                            questUrl = string.Join("/", parts.Take(4));
                            pattern = questUrl.Replace("/", "\\/") + "(?:\\/\\{[a-z]+\\}){1,2}";
                        }
                    }


                    // 整体结构类似认证中间件UseAuthentication的逻辑，具体查看开源地址
                    // https://github.com/dotnet/aspnetcore/blob/master/src/Security/Authentication/Core/src/AuthenticationMiddleware.cs
                    httpContext.Features.Set<IAuthenticationFeature>(new AuthenticationFeature
                    {
                        OriginalPath = httpContext.Request.Path,
                        OriginalPathBase = httpContext.Request.PathBase
                    });

                    // Give any IAuthenticationRequestHandler schemes a chance to handle the request
                    // 主要作用是: 判断当前是否需要进行远程验证，如果是就进行远程验证
                    var handlers = httpContext.RequestServices.GetRequiredService<IAuthenticationHandlerProvider>();
                    foreach (var scheme in await Schemes.GetRequestHandlerSchemesAsync())
                    {
                        if (await handlers.GetHandlerAsync(httpContext, scheme.Name) is IAuthenticationRequestHandler handler && await handler.HandleRequestAsync())
                        {
                            context.Fail();
                            return;
                        }
                    }
                    //判断请求是否拥有凭据，即有没有登录
                    // 首先检查是否是API Key认证
                    var apiKeyResult = await httpContext.AuthenticateAsync("ApiKey");
                    if (apiKeyResult?.Principal != null)
                    {
                        // API Key认证成功，但需要检查是否是ApiKeyPolicy策略
                        httpContext.User = apiKeyResult.Principal;

                        // 检查当前endpoint是否明确要求ApiKeyPolicy
                        var endpoint = httpContext.GetEndpoint();
                        var authorizeData = endpoint?.Metadata?.GetOrderedMetadata<IAuthorizeData>();
                        var hasApiKeyPolicy = authorizeData?.Any(a => a.Policy == "ApiKeyPolicy") == true;

                        if (hasApiKeyPolicy)
                        {
                            // 明确标记为ApiKeyPolicy的接口，API Key可以访问
                            context.Succeed(requirement);
                            return;
                        }
                        else
                        {
                            // 没有明确标记ApiKeyPolicy的接口，API Key不能访问
                            context.Fail();
                            return;
                        }
                    }

                    var defaultAuthenticate = await Schemes.GetDefaultAuthenticateSchemeAsync();
                    if (defaultAuthenticate != null)
                    {
                        var result = await httpContext.AuthenticateAsync(defaultAuthenticate.Name);

                        // 是否开启测试环境
                        var isTestCurrent = Appsettings.app(new string[] { "AppSettings", "UseLoadTest" }).ObjToBool();

                        //result?.Principal不为空即登录成功
                        if (result?.Principal != null || isTestCurrent)
                        {

                            if (!isTestCurrent) httpContext.User = result.Principal;

                            // 获取当前用户的角色信息
                            var currentUserRoles = new List<string>();
                            // ids4和jwt切换
                            // ids4
                            if (Permissions.IsUseIds4)
                            {
                                currentUserRoles = (from item in httpContext.User.Claims
                                                    where item.Type == "role"
                                                    select item.Value).ToList();
                            }
                            else
                            {
                                // jwt
                                currentUserRoles = (from item in httpContext.User.Claims
                                                    where item.Type == requirement.ClaimType
                                                    select item.Value).ToList();
                            }

                            if (currentUserRoles.Contains(Roles.SuperAdminId.ToEnumInt().ToString()))
                            {
                                //超级管理员可以直接通过
                                //context.Succeed(requirement);
                                //return;
                            }


                            var isMatchRole = false;
                            var permisssionRoles = requirement.Permissions.Where(w => currentUserRoles.Contains(w.Role));
                            foreach (var item in permisssionRoles)
                            {
                                try
                                {

                                    if (isSingle)
                                    {
                                        //角色的 api Url 如果匹配当前的请求名称 则表示权限认证通过
                                        if (Regex.IsMatch(item.Url?.ObjToString().ToLower(), pattern))
                                        {
                                            isMatchRole = true;
                                            break;
                                        }
                                    }

                                    if (!isSingle)
                                    {
                                        //角色的 api Url 如果匹配当前的请求名称 则表示权限认证通过
                                        if (Regex.Match(questUrl, item.Url?.ObjToString().ToLower())?.Value == questUrl)
                                        {
                                            isMatchRole = true;
                                            break;
                                        }
                                    }

                                }
                                catch (Exception)
                                {
                                    // ignored
                                }
                            }

                            //验证权限
                            if (currentUserRoles.Count <= 0 || !isMatchRole)
                            {
                                context.Fail();
                                return;
                            }
                            else
                            {
                                context.Succeed(requirement);
                                return;
                            }
                        }
                    }
                    //判断没有登录时，是否访问登录的url,并且是Post请求，并且是form表单提交类型，否则为失败
                    if (!(questUrl.Equals(requirement.LoginPath.ToLower(), StringComparison.Ordinal) && (!httpContext.Request.Method.Equals("POST") || !httpContext.Request.HasFormContentType)))
                    {
                        context.Fail();
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                //var hd = new UserAuthorizationHandler();
                //context.Fail(new AuthorizationFailureReason(hd, "exx") { });
                _logger.LogError(ex.Message);
            }
            //context.Succeed(requirement);
        }
    }
}
