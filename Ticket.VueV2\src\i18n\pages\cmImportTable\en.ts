﻿export default {
	cmImportTableSearch: {
		//查询区域
		searchKeyPlaceholder: 'Please input',
	},
	cmImportTableButtons: {
		//非按钮
	},
	cmImportTableFields: {
		//table 列名
		id: 'Id',
		isDeleted: 'IsDeleted',
		createdById: 'CreatedById',
		createdAt: 'CreatedAt',
		modifiedById: 'ModifiedById',
		modifiedAt: 'ModifiedAt',
		importName: 'Import Name',
		importDescription: 'ImportDescription',
		importStatus: 'ImportStatus',
		tableModel: 'Table Model',
		selectImportType: 'What type of data are you importing?',
		importConfiguration: 'Configuration',
		SelecttheSource: 'Select the Source',
		haveHeader: 'Have Header?',
		batchSize: 'Batch Size',
		OnlyUpdate: 'Only Update',
		OnlyInsert: 'Only Insert',
		UpdateAndInsert: 'Update and Insert',
		OperationType: 'Operation Type',
		UpdateOrInsertMatchWhere: 'Data Match Conditions',
		pleaseSelectTheSource: 'Please select the source',
		uploadExcel: 'Please upload excel file',
	},
};
