﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmTicketUsersGrant;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmTicketUsersGrantController : ControllerBase
    {
        private readonly ILogger<CmTicketUsersGrantInfo> _logger;
        private readonly ICmTicketUsersGrantService _cmTicketUsersGrantService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmTicketUsersGrantService"></param>
        /// <param name="logger"></param>
        public CmTicketUsersGrantController(ICmTicketUsersGrantService cmTicketUsersGrantService, ILogger<CmTicketUsersGrantInfo> logger)
        {
            _logger = logger;
            _cmTicketUsersGrantService = cmTicketUsersGrantService;
            
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CmTicketUsersGrantAddOrUpdateDto req)
        {
            return await _cmTicketUsersGrantService.AddCmTicketUsersGrant(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmTicketUsersGrantAddOrUpdateDto req)
        {
            return await _cmTicketUsersGrantService.UpdateCmTicketUsersGrant(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _cmTicketUsersGrantService.Delete(a => a.Id == id);

            return result > 0;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _cmTicketUsersGrantService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmTicketUsersGrantDto> Get(long id)
        {
            return await _cmTicketUsersGrantService.QueryInfo<CmTicketUsersGrantDto>(a => a.Id == id);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmTicketUsersGrantDto>> GetList([FromQuery] CmTicketUsersGrantListQueryDto req)
        {
            return await _cmTicketUsersGrantService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<CmTicketUsersGrantDetailDto> Detail(long id)
        {
            return await _cmTicketUsersGrantService.Detail(id);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmTicketUsersGrantDetailDto>> DetailList([FromQuery] CmTicketUsersGrantListQueryDto req)
        {
            return await _cmTicketUsersGrantService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmTicketUsersGrantDetailDto>> Query([FromQuery] CmTicketUsersGrantPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmTicketUsersGrantDetailDto>> QueryPost(CmTicketUsersGrantPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmTicketUsersGrantDetailDto>> PageQuery([FromQuery] CmTicketUsersGrantPageQueryDto req)
        {
            return await _cmTicketUsersGrantService.PageQueryView(req);
        }

    }
}