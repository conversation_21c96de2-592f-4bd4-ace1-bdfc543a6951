﻿<template>
	<div class="permission-table-container">
		<div class="foot-btns">
			<el-button v-auth="'systemRole.CreatePermission'" @click="onOpenMenuButtons" type="primary" size="small">
				{{ $t('message.menuCommonFields.createPermission') }}
			</el-button>

			<el-button v-auth="'system.SavePermissions'" @click="onSave" type="primary" size="small"
				:loading="saveLoading">
				{{ $t('message.page.buttonSavePermissions') }}
			</el-button>
		</div>
		<el-table ref="tableRef" :data="treePermissionData" v-loading="tableLoading" row-key="id"
			height="calc(100vh - 380px)" :default-expand-all="true" :tree-props="{ children: 'children' }"
			@select="onSelect" @select-all="onSelectAll" :row-style="{ height: '40px' }"
			:cell-style="{ padding: '0px' }">
			<template #empty>
				<el-empty :description="$t('message.page.emptyDescription')" :image-size="100"></el-empty>
			</template>

			<el-table-column type="selection"></el-table-column>

			<el-table-column :label="$t('message.permissionTableFields.name')" width="200" show-overflow-tooltip>
				<template #default="{ row }">
					<!-- <el-checkbox v-model="row.isCheck" :label="true" class="col-name" @change="(e) => checkAll(e, row)"></el-checkbox> -->
					{{ $t(row.title) }}
				</template>
			</el-table-column>

			<el-table-column :label="$t('message.permissionTableFields.permissions')">
				<template #default="{ row }">
					<el-checkbox-group v-model="checkList">
						<el-checkbox v-for="item in row.permissions" :disabled="disableCheckBox(row.title)"
							:key="item.id" :label="item.id" @change="(e) => checkOne(e, item.id)">
							<el-dropdown @command="handleCommand($event, item)">
								<span class="el-dropdown-link">
									{{ $t(item.title) }}
								</span>
								<template #dropdown>
									<Auth :value="'systemRole.EditPermission'">
										<el-dropdown-menu>
											<el-dropdown-item command="editPermissions">
												{{ $t('message.menuCommonFields.editPermission') }}
											</el-dropdown-item>
										</el-dropdown-menu>
									</Auth>
								</template>
							</el-dropdown>
						</el-checkbox>
					</el-checkbox-group>
				</template>
			</el-table-column>
		</el-table>

		<MenuButtons ref="menuButtonsRef" @fetchData="refreshData(roleObj)" />
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, ref, defineComponent } from 'vue';
import { ElMessage } from 'element-plus';
import MenuButtons from '/@/views/bpts/menu/component/buttonCreateOrEdit.vue';
import roleModulePermissionApi from '/@/api/roleModulePermission';
import menuApi from '/@/api/menu/list';
import { Consts } from '/@/constants';
import Auth from '/@/components/auth/auth.vue';

export default defineComponent({
	name: 'roleModulePermissionCreateOrEdit',
	components: { MenuButtons, Auth },
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const menuButtonsRef = ref();
		const tableRef = ref();
		const state = reactive({
			title: 'Role Permission',
			isShowDialog: false,
			tableLoading: false,
			saveLoading: false,
			roleObj: { id: 0 }, //角色信息
			treePermissionData: [],
			treeCheckData: [], //已勾选
			checkList: [],
		});

		// 打开弹窗
		const refreshData = (parmas: any) => {
			state.roleObj = parmas;
			state.checkList = [];

			//获取table 数据
			state.tableLoading = true;
			menuApi
				.PermissionTree()
				.then((rs) => {
					state.treePermissionData = rs.data;
				})
				.finally(() => {
					state.tableLoading = false;
				});

			//获取角色对应的权限id
			roleModulePermissionApi.GetPermissionList(state.roleObj.id, 3).then((rs) => {
				state.checkList = rs.data;
			});
		};

		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			state.treeCheckData = [];
			state.treePermissionData = [];
		};

		// 保存
		const onSave = async (pl) => {
			if (state.checkList.length == 0) {
				ElMessage.error('至少选择一项');
				return;
			}
			state.saveLoading = true;
			roleModulePermissionApi
				.Assign({ menus: state.checkList, roleId: state.roleObj.id })
				.then(() => {
					ElMessage.success('Succeed');
				})
				.finally(() => {
					state.saveLoading = false;
					context.emit('fetchData');
				});
		};

		const onSelect = (selection, row) => {
			const isSelect = selection.some((el) => {
				return row.id === el.id;
			});
			setChildren(row.children, isSelect);
			refreshCheckList(row, isSelect);
		};

		const onSelectAll = (selection) => {
			const isSelect = selection.some((el) => {
				const tableDataIds = state.treePermissionData.map((j) => j.id);
				return tableDataIds.includes(el.id);
			});

			state.treePermissionData.map((el) => {
				setChildren(el.children, isSelect);
				refreshCheckList(el, isSelect);
			});
		};

		const onOpenMenuButtons = () => {
			menuButtonsRef.value.openDialog(null);
		};

		const handleCommand = (command: string, row: any) => {
			if (command === 'editPermissions') {
				menuButtonsRef.value.openDialog(row, Consts.Action_GetById);
			}
		};

		const setChildren = (children, type) => {
			if (!children) return;

			// 编辑多个子层级
			children.map((j) => {
				toggleSelection(j, type);
			});
		};

		const toggleSelection = (row, select) => {
			tableRef.value.toggleRowSelection(row, select);

			refreshCheckList(row, select);
		};

		const refreshCheckList = (row, isCheckAll) => {
			const pers = row.permissions?.map((a) => a.id);

			pers.forEach((item) => {
				if (isCheckAll) {
					if (state.checkList.indexOf(item) == -1) {
						state.checkList.push(item);
					}
				} else {
					if (row.id != 112 && row.id != 957 && row.id != 975) {
						state.checkList.splice(state.checkList.indexOf(item), 1);
					}
				}
			});
		};

		const checkOne = (isCheck, id) => { };

		const disableCheckBox = (title) => {
			if (title == 'message.router.dashboard' || title == 'message.router.personal' || title == 'message.router.changepassword') {
				return true;
			}

			return false;
		};

		return {
			menuButtonsRef,
			handleCommand,
			onOpenMenuButtons,
			onSelect,
			onSave,
			refreshData,
			onInitForm,
			tableRef,
			onSelectAll,
			checkOne,
			...toRefs(state),
			disableCheckBox,
		};
	},
});
</script>
<style scoped>
.treeMain {
	height: 280px;
	overflow: auto;
	border: 1px solid #dcdfe6;
	margin-bottom: 10px;
	width: 500px;
}

.col-name {
	font-weight: bold;
}

.foot-btns {
	background-color: #ffffff;
	padding: 0 0 10px;
	height: 40px;
	display: flex;
	justify-content: flex-end;
	border-bottom: 1px solid #e6e6e6;
}

.save-btn {}
</style>
