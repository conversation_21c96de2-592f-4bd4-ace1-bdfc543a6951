﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.MenuApi;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class MenuApiController : ControllerBase
    {
        private readonly ILogger<MenuApiInfo> _logger;

        private readonly IMenuApiService _menuApiService;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="menuApiService"></param>
        /// <param name="logger"></param>
        public MenuApiController(IMenuApiService menuApiService, ILogger<MenuApiInfo> logger)
        {
            this._menuApiService = menuApiService;
            this._logger = logger;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        [NonAction]
        public async Task<long> Add(MenuApiAddDto req)
        {
            long menuId= await _menuApiService.AddIdentity(req.Adapt<MenuApiInfo>());

            

            return menuId;
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        [NonAction]
        public async Task<bool> Update(MenuApiEditDto req)
        {
            return await _menuApiService.Update(req.Adapt<MenuApiInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="apiId">apiId</param>
        /// <returns></returns>
        [HttpPost("Delete/{apiId}")]
        [NonAction]
        public async Task<bool> Delete(long apiId)
        {
            int result = await _menuApiService.Delete(a => a.ApiId == apiId);

            return result > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        [NonAction]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _menuApiService.DeleteByIds(items);

            return result;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="apiId"></param>
        /// <returns></returns>
        [HttpGet("Get/{apiId}")]
        [NonAction]
        public async Task<MenuApiDto> Get(long apiId)
        {
            return await _menuApiService.QueryInfo<MenuApiDto>(a => a.ApiId == apiId);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="apiId"></param>
        /// <returns></returns>
        [HttpGet("Detail/{apiId}")]
        [NonAction]
        public async Task<MenuApiDetailDto> Detail(long apiId)
        {
            var obj = await _menuApiService.QueryInfo(a => a.ApiId == apiId);

            if (obj != null)
            {
                return  _menuApiService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<MenuApiListDto>> List([FromQuery] MenuApiListQueryDto req)
        {
            var list = new List<MenuApiListDto>();
            var where = PredicateBuilder.New<MenuApiInfo>(true);
            if (req.menuId.HasValue)
            {
                where.And(a => a.MenuId == req.menuId.Value);
            }
            if (req.apiId.HasValue)
            {
                where.And(a => a.ApiId == req.menuId.Value);
            }
            var orderBy = new OrderBy<MenuApiInfo>(req.order, req.sort);
            var data = await _menuApiService.QueryList(where, orderBy);
            
            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _menuApiService.Join(item);
                list.Add(detail.Adapt<MenuApiListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        [NonAction]
        public async Task<PageQueryResult<MenuApiListDto>> Query([FromQuery] MenuApiPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        [NonAction]
        public async Task<PageQueryResult<MenuApiListDto>> QueryPost(MenuApiPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<MenuApiListDto>> PageQuery([FromQuery] MenuApiPageQueryDto req)
        {
            var list = new List<MenuApiListDto>();
            var where = PredicateBuilder.New<MenuApiInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            var totalCount = await _menuApiService.CountAsync(where);
            var orderBy = new OrderBy<MenuApiInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _menuApiService.QueryList(where, orderBy, paging);
            
            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _menuApiService.Join(item);
                list.Add(detail.Adapt<MenuApiListDto>());
            }
            #endregion

            return new PageQueryResult<MenuApiListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

    }
}