﻿using YunLanCrm.Common.Helper;
using YunLanCrm.Common.LogHelper;
using YunLanCrm.Hubs;
using YunLanCrm.Model;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using StackExchange.Profiling;
using System;
using YunLanCrm.Model.Api;
using Newtonsoft.Json;
using YunLanCrm.Extensions.Exceptions;
using YunLanCrm.Common.HttpContextUser;

namespace YunLanCrm.Filter
{
    /// <summary>
    /// 全局异常错误日志
    /// </summary>
    public class GlobalExceptionsFilter : IExceptionFilter
    {
        private readonly IWebHostEnvironment _env;
        private readonly IHubContext<ChatHub> _hubContext;
        private readonly ILogger<GlobalExceptionsFilter> _loggerHelper;

        private readonly ILogger<GlobalExceptionsFilter> _logger;
        private readonly IUser _user;

        public GlobalExceptionsFilter(IWebHostEnvironment env, ILogger<GlobalExceptionsFilter> loggerHelper, IHubContext<ChatHub> hubContext, ILogger<GlobalExceptionsFilter> logger, IUser user)
        {
            _env = env;
            _loggerHelper = loggerHelper;
            _hubContext = hubContext;
            _logger = logger;
            _user = user;
        }

        public void OnException(ExceptionContext context)
        {
            var json = new ApiResult<string>();

            // 检查异常是否是CrmException的实例
            if (context.Exception is CrmException appException)
            {
                json.ResultCode = appException.StatusCode; // 使用自定义的状态码
                json.ResultMsg = appException.Message;
            }
            else
            {
                json.ResultCode = 500;
                json.ResultMsg = context.Exception.Message;
            }

            var errorAudit = "Unable to resolve service for";

            if (!string.IsNullOrEmpty(json.ResultMsg) && json.ResultMsg.Contains(errorAudit))
            {
                json.ResultMsg = json.ResultMsg.Replace(errorAudit, $"（若新添加服务，需要重新编译项目）{errorAudit}");
            }

            if (_env.EnvironmentName.ObjToString().Equals("Development"))
            {
                //json.ResultMsg += $"[{context.Exception.StackTrace}]";//堆栈信息
            }

            var setting = new JsonSerializerSettings
            {
                ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
            };

            var res = new ContentResult();

            res.Content = JsonConvert.SerializeObject(json, setting);

            context.Result = res;

            MiniProfiler.Current.CustomTiming("Errors：", json.ResultMsg);

            //采用log4net 进行错误日志记录
            //_loggerHelper.LogError(json.ResultMsg + WriteLog(json.ResultMsg, context.Exception));

            HttpRequest request = context.HttpContext.Request;

            string content = string.Empty;

            content += $"【用户名】=> {_user.Name}\r\n";
            content += "【访问的Url】=> " + Convert.ToString(request.Headers["AccessUrl"]) + Environment.NewLine;
            content += $"【异常信息】=> \r\n{context.Exception.ObjToString()}\r\n";
            content += $"【堆栈调用】=> \r\n{context.Exception.StackTrace.ObjToString()}\r\n";
            content += $"【Content】=> \r\n{res.Content}\r\n";
            ;

            _logger.LogToFile(LogLevel.Error, content);

            //_hubContext.Clients.All.SendAsync("ReceiveUpdate", LogLock.GetLogData()).Wait();
        }

        /// <summary>
        /// 自定义返回格式
        /// </summary>
        /// <param name="throwMsg"></param>
        /// <param name="ex"></param>
        /// <returns></returns>
        public string WriteLog(string throwMsg, Exception ex)
        {
            return string.Format("\r\n【自定义错误】：{0} \r\n【异常类型】：{1} \r\n【异常信息】：{2} \r\n【堆栈调用】：{3}", new object[] { throwMsg,
                ex.GetType().Name, ex.Message, ex.StackTrace });
        }
    }

    public class InternalServerErrorObjectResult : ObjectResult
    {
        public InternalServerErrorObjectResult(object value) : base(value)
        {
            StatusCode = StatusCodes.Status500InternalServerError;
        }
    }

    //返回错误信息
    public class JsonErrorResponse
    {
        /// <summary>
        /// 生产环境的消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 开发环境的消息
        /// </summary>
        public string DevelopmentMessage { get; set; }
    }

}
