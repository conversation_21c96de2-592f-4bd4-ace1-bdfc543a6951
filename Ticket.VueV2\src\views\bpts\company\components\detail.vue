﻿<template>
	<el-main style="padding: 0 20px">
		<el-descriptions :column="1" border size="small" v-if="info.orgType === 1">
			<el-descriptions-item label="公司名称">{{ obj.name }}</el-descriptions-item>
			<el-descriptions-item label="公司编码">{{ obj.code }}</el-descriptions-item>
			<el-descriptions-item label="简称">{{ obj.shortName }}</el-descriptions-item>
			<el-descriptions-item label="地址">{{ obj.address }}</el-descriptions-item>
			<el-descriptions-item label="法人">{{ obj.legalPerson }}</el-descriptions-item>
			<el-descriptions-item label="规模">{{ obj.scale }}</el-descriptions-item>
			<el-descriptions-item label="联系人">{{ obj.linkMan }}</el-descriptions-item>
			<el-descriptions-item label="电话号码">{{ obj.phone }}</el-descriptions-item>
			<el-descriptions-item label="联系Email">{{ obj.email }}</el-descriptions-item>
			<el-descriptions-item label="传真">{{ obj.fax }}</el-descriptions-item>
			<el-descriptions-item label="联系号码">{{ obj.linkTel }}</el-descriptions-item>
			<el-descriptions-item label="创建时间">{{ obj.createAt }}</el-descriptions-item>
			<el-descriptions-item label="所属行业">{{ obj.industryName }}</el-descriptions-item>
			<el-descriptions-item label="状态">
				<el-tag type="danger" v-if="obj.status == '1'">禁用</el-tag>
				<el-tag type="success" v-if="obj.status == '0'">正常</el-tag>
			</el-descriptions-item>
			<el-descriptions-item label="备注信息">{{ obj.description }}</el-descriptions-item>
		</el-descriptions>

		<el-descriptions v-if="info.orgType === 2" :column="1" border size="small">
			<el-descriptions-item label="部门名称">{{ obj.name }}</el-descriptions-item>
			<el-descriptions-item label="部门主管">{{ obj.leaderName }}</el-descriptions-item>
			<el-descriptions-item label="状态">
				<el-tag type="danger" v-if="obj.status == '1'">禁用</el-tag>
				<el-tag type="success" v-if="obj.status == '0'">正常</el-tag>
			</el-descriptions-item>
			<el-descriptions-item label="创建时间">{{ obj.createTime }}</el-descriptions-item>
		</el-descriptions>
	</el-main>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent } from 'vue';
import { formatStrDate } from '/@/utils/formatTime';
import companyApi from '/@/api/company';
import departmentApi from '/@/api/department';

export default defineComponent({
	name: 'apiDetail',
	props: {
		info: Object,
	},
	setup(props) {
		const state = reactive({
			isShowDialog: false,
			obj: {},
		});
		// 页面加载时
		onMounted(() => {
			console.log('props.info', props.info);
			if (props.info.orgType == 1) {
				companyApi.DetailByOrgId(props.info.orgId).then((rs) => {
					state.obj = rs.data;
				});
			}
			if (props.info.orgType == 2) {
				departmentApi.DetailByOrgId(props.info.orgId).then((rs) => {
					state.obj = rs.data;
				});
			}
		});
		return {
			formatStrDate,
			...toRefs(state),
		};
	},
});
</script>


                        
        
        