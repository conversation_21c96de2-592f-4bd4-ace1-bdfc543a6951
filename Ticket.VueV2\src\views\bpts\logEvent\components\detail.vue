﻿<template>
	<el-main class="log-detail" style="padding: 0 20px">
		<el-descriptions v-if="logType === 'System'" :column="1" border size="small" class="mb10">
			<el-descriptions-item label="Level">{{ info.level }}</el-descriptions-item>
			<el-descriptions-item label="TimeStamp">{{ info.timeStamp }}</el-descriptions-item>
			<el-descriptions-item label="Log Type">{{ info.logType }}</el-descriptions-item>
			<el-descriptions-item label="Data Key">{{ info.dataKey }}</el-descriptions-item>
		</el-descriptions>
		<el-descriptions v-if="logType === 'Login'" :column="1" border size="small" class="mb10">
			<el-descriptions-item label="Username">{{ info.UserName }}</el-descriptions-item>
			<el-descriptions-item label="Client IP">{{ info.ClientIP.replace('::ffff:', '') }}</el-descriptions-item>
			<el-descriptions-item label="Login Time">{{ info.LoginTime }}</el-descriptions-item>
			
			<el-descriptions-item label="Logout Time">{{ formatStrDate(info.LogoutTime)==''?'':info.LogoutTime }}</el-descriptions-item>
			<el-descriptions-item label="Device">{{ info.Device }}</el-descriptions-item>
			<el-descriptions-item label="Login Status">{{ info.LogStatus }}</el-descriptions-item>
		</el-descriptions>

		<el-descriptions v-if="logType === 'Request'" :column="1" border size="small" class="mb10">
			<el-descriptions-item label="User">{{ info.User }}</el-descriptions-item>
			<el-descriptions-item label="IP">{{ info.IP }}</el-descriptions-item>
			<el-descriptions-item label="API">{{ info.API }}</el-descriptions-item>
			<el-descriptions-item label="BeginTime">{{ info.BeginTime }}</el-descriptions-item>
			<el-descriptions-item label="OPTime">{{ info.OPTime }}</el-descriptions-item>
			<el-descriptions-item label="RequestMethod">{{ info.RequestMethod }}</el-descriptions-item>
			<el-descriptions-item label="Agent">{{ info.Agent }}</el-descriptions-item>
		</el-descriptions>

		<el-descriptions v-if="logType === 'Sql'" :column="1" border size="small" class="mb10">
			<el-descriptions-item label="Params">{{ info.Params }}</el-descriptions-item>
			<el-descriptions-item label="Sql">{{ info.Sql }}</el-descriptions-item>
		</el-descriptions>

		<el-card shadow="hover">
			<template #header>
				<div class="card-header">
					<span>Message</span>
				</div>
			</template>
			<pre class="per microlight">{{ msg }}</pre>
		</el-card>
	</el-main>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent } from 'vue';
import { formatStrDate } from '/@/utils/formatTime';

export default defineComponent({
	name: 'apiDetail',
	setup() {
		const state = reactive({
			isShowDialog: false,
			logType: '',
			info: {} as any,
			msg: {},
		});
		const onShow = (obj: any, logType: string) => {
			state.info = obj;
			state.msg = obj;
			state.logType = logType;
			if (logType == 'Sql') {
				console.log(logType);
				var jsonObj = JSON.parse(obj.message);
				state.info = jsonObj;
				state.msg = jsonObj;
			}
		};
		// 页面加载时
		onMounted(() => { });
		return {
			onShow,
			formatStrDate,
			...toRefs(state),
		};
	},
});
</script>

<style>
pre.microlight {
	word-wrap: break-word;
	background: #333;

	white-space: pre-wrap;
	word-break: break-all;
	word-break: break-word;
}

.per {
	display: block;
	overflow-x: auto;
	padding: 0.5em;
	background: rgb(51, 51, 51);
	color: white;
}

.log-detail .el-descriptions__label {
	min-width: 150px;
}

.log-detail .el-descriptions__content {
	min-width: 400px;
}
</style>
