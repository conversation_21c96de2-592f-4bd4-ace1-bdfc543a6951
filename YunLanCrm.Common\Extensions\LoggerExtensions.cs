﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Linq;
using System.Threading;
using Microsoft.Extensions.Logging;

namespace YunLanCrm
{
    /// <summary>
    /// 日志拓展
    /// </summary>
    public static class LoggerExtensions
    {
        static ReaderWriterLockSlim LogWriteLock = new ReaderWriterLockSlim();

        public static void LogToDatabase(this ILogger logger, LogLevel logLevel, string message, object entity = null, params object[] args)
        {
            using (logger.BeginScope(new[] { new KeyValuePair<string, object>("WriteToType", "Database") }))
            {


                if (entity != null)
                {
                    logger.Log(logLevel, message, new[] { entity }.Concat(args).ToArray());
                }
                else
                {
                    logger.Log(logLevel, message, args);
                }
            }
        }

        public static void LogToDatabase(this ILogger logger, LogLevel logLevel, string logType, string message, string dataKey, string exception, object entity = null, params object[] args)
        {
            var properties = new List<KeyValuePair<string, object>> { new KeyValuePair<string, object>("WriteToType", "Database") };

            if (logType != null)
            {
                properties.Add(new KeyValuePair<string, object>("LogType", logType));
            }

            if (!string.IsNullOrEmpty(dataKey))
            {
                properties.Add(new KeyValuePair<string, object>("DataKey", dataKey));
            }

            if (!string.IsNullOrEmpty(exception))
            {
                properties.Add(new KeyValuePair<string, object>("DataMessage", exception));
            }

            using (logger.BeginScope(properties))
            {
                if (entity != null)
                {
                    logger.Log(logLevel, message, new[] { entity }.Concat(args).ToArray());
                }
                else
                {
                    logger.Log(logLevel, message, args);
                }
            }
        }

        public static void LogToFile(this ILogger logger, LogLevel logLevel, string message, string fileName = null, object entity = null, params object[] args)
        {
            try
            {
                LogWriteLock.EnterWriteLock();

                var properties = new List<KeyValuePair<string, object>> { new KeyValuePair<string, object>("WriteToType", "File") };

                if (fileName != null)
                {
                    properties.Add(new KeyValuePair<string, object>("FileName", fileName));
                }

                using (logger.BeginScope(properties))
                {
                    if (entity != null)
                    {
                        logger.Log(logLevel, message, new[] { entity }.Concat(args).ToArray());
                    }
                    else
                    {
                        logger.Log(logLevel, message, args);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.Write(ex.Message);
            }
            finally
            {
                LogWriteLock.ExitWriteLock();
            }
        }
    }
}
