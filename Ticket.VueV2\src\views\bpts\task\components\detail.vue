﻿<template>
    <el-main style="padding:0 20px;">
        <el-descriptions :column="1" border size="small">
           
 <el-descriptions-item label="id">{{ info.id }}</el-descriptions-item>
 <el-descriptions-item label="name">{{ info.name }}</el-descriptions-item>
 <el-descriptions-item label="jobGroup">{{ info.jobGroup }}</el-descriptions-item>
 <el-descriptions-item label="cron">{{ info.cron }}</el-descriptions-item>
 <el-descriptions-item label="assemblyName">{{ info.assemblyName }}</el-descriptions-item>
 <el-descriptions-item label="className">{{ info.className }}</el-descriptions-item>
 <el-descriptions-item label="remark">{{ info.remark }}</el-descriptions-item>
 <el-descriptions-item label="runTimes">{{ info.runTimes }}</el-descriptions-item>
 <el-descriptions-item label="beginTime">{{ info.beginTime }}</el-descriptions-item>
 <el-descriptions-item label="endTime">{{ info.endTime }}</el-descriptions-item>
 <el-descriptions-item label="triggerType">{{ info.triggerType }}</el-descriptions-item>
 <el-descriptions-item label="intervalSecond">{{ info.intervalSecond }}</el-descriptions-item>
 <el-descriptions-item label="cycleRunTimes">{{ info.cycleRunTimes }}</el-descriptions-item>
 <el-descriptions-item label="isStart">{{ info.isStart }}</el-descriptions-item>
 <el-descriptions-item label="jobParams">{{ info.jobParams }}</el-descriptions-item>
 <el-descriptions-item label="isDeleted">{{ info.isDeleted }}</el-descriptions-item>
 <el-descriptions-item label="createTime">{{ info.createTime }}</el-descriptions-item>
        </el-descriptions>
    </el-main>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent } from 'vue';
import { formatStrDate } from '/@/utils/formatTime';

export default defineComponent({
    name: 'apiDetail',
    props: {
        info: Object
    },
    setup(props) {
        const state = reactive({
            isShowDialog: false,
            obj: {},
        });
        // 页面加载时
        onMounted(() => {
           window.console.log("props", props.info)
        });
        return {
            ...toRefs(state),
        };
    },
});
</script>


                        
        
        