﻿using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Serialization;
using Elsa.Services.Models;
using Elsa.Services.WorkflowStorage;
using YunLanCrm.Dto.WorkflowExecutionLogRecords;

namespace YunLanCrm.Api.ElsaWorkflows.Activities
{
    [Action(Category = "基础节点", DisplayName = "选择分支", Description = "选择分支（Fork）")]
    public class ForkCrm : Elsa.Activities.ControlFlow.Fork
    {
        IWorkflowStorageService _workflowStorageService;
        public ForkCrm(IContentSerializer serializer, IWorkflowStorageService workflowStorageService)
        {
            _workflowStorageService = workflowStorageService;
        }

        [ActivityInput(Hint = "Config", SupportedSyntaxes = new[] { SyntaxNames.Literal, SyntaxNames.JavaScript, SyntaxNames.Liquid })]
        public string Config { get; set; } = default;

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            if (context.Input is WorkflowExecutionDto)
            {
                var input = context.GetInput<WorkflowExecutionDto>();
                if (!string.IsNullOrWhiteSpace(input.Branch))
                {
                    return Outcome(input.Branch, input);
                }
            }

            return await base.OnExecuteAsync(context);
        }
    }
}
