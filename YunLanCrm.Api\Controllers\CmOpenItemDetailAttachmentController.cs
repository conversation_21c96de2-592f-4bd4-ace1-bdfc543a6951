﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmOpenItemDetailAttachment;
using YunLanCrm.Dto.Files;
using YunLanCrm.Services;
using SkyWalking.NetworkProtocol.V3;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmOpenItemDetailAttachmentController : ControllerBase
    {
        private readonly ILogger<CmOpenItemDetailAttachmentInfo> _logger;
        private readonly ICmOpenItemDetailAttachmentService _cmOpenItemDetailAttachmentService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmOpenItemDetailAttachmentService"></param>
        /// <param name="logger"></param>
        public CmOpenItemDetailAttachmentController(ICmOpenItemDetailAttachmentService cmOpenItemDetailAttachmentService, ILogger<CmOpenItemDetailAttachmentInfo> logger)
        {
            _logger = logger;
            _cmOpenItemDetailAttachmentService = cmOpenItemDetailAttachmentService;
            
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CmOpenItemDetailAttachmentAddOrUpdateDto req)
        {
            return await _cmOpenItemDetailAttachmentService.AddCmOpenItemDetailAttachment(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmOpenItemDetailAttachmentAddOrUpdateDto req)
        {
            return await _cmOpenItemDetailAttachmentService.UpdateCmOpenItemDetailAttachment(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _cmOpenItemDetailAttachmentService.Delete(a => a.OpenItemDetailId == id);

            return result > 0;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _cmOpenItemDetailAttachmentService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmOpenItemDetailAttachmentDto> Get(long id)
        {
            return await _cmOpenItemDetailAttachmentService.QueryInfo<CmOpenItemDetailAttachmentDto>(a => a.Id == id);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmOpenItemDetailAttachmentDto>> GetList([FromQuery] CmOpenItemDetailAttachmentListQueryDto req)
        {
            return await _cmOpenItemDetailAttachmentService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<CmOpenItemDetailAttachmentDto> Detail(long id)
        {
            return await _cmOpenItemDetailAttachmentService.Detail(id);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmOpenItemDetailAttachmentDto>> DetailList([FromQuery] CmOpenItemDetailAttachmentListQueryDto req)
        {
            return await _cmOpenItemDetailAttachmentService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmOpenItemDetailAttachmentDto>> Query([FromQuery] CmOpenItemDetailAttachmentPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmOpenItemDetailAttachmentDto>> QueryPost(CmOpenItemDetailAttachmentPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmOpenItemDetailAttachmentDto>> PageQuery([FromQuery] CmOpenItemDetailAttachmentPageQueryDto req)
        {
            return await _cmOpenItemDetailAttachmentService.PageQueryView(req);
        }

        /// <summary>
        /// 文件上传
        /// </summary>
        /// <param name="files"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        [HttpPost("UploadV2")]
        public async Task<CmOpenItemDetailAttachmentDto> UpLoadFileV2(IFormCollection formData)
        {

            if (formData == null || formData.Files.Count == 0)
            {
                throw new ArgumentNullException(nameof(formData));
            }

            // 获取额外的参数
            var openItemDetailId = Convert.ToInt64(formData["OpenItemDetailId"]);
            if (openItemDetailId <= 0)
            {
                throw new ArgumentException("参数 'OpenItemDetailId' 不能为空");
            }

            List<IFormFile> formFiles = (List<IFormFile>)formData.Files;
            //每次上传新的附件先删除旧的
            await Delete(openItemDetailId);

            CmOpenItemDetailAttachmentDto data = await _cmOpenItemDetailAttachmentService.UpLoadFile(formFiles, openItemDetailId);
            data.Url = data.FilePath;

            return data;
        }

    }
}