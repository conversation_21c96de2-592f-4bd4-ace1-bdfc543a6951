import request from '/@/utils/request';

export default class BaseApi {
    baseurl: string;
    key: string;
    constructor(baseurl: string, key: string) {
        this.baseurl = baseurl;
        this.key = key;
    }

    // 获取指定的单个记录
    Get(params: any) {
        return request({
            url: this.baseurl + 'Get',
            method: 'get',
            params,
        });
    }

    GetByKey(key: any) {
        return request({
            url: this.baseurl + 'Get/' + key,
            method: 'get',
            params: {},
        });
    }

    Detail(key: any) {
        return request({
            url: this.baseurl + 'Detail/' + key,
            method: 'get',
            params: {},
        });
    }

    // 根据条件获取所有记录
    GetList(params?: any) {
        return request({
            url: this.baseurl + 'Get',
            method: 'get',
            params,
        });
    }

    Query(data: any) {
        return request({
            url: this.baseurl + 'Query',
            method: 'post',
            data,
        });
    }

    // 创建记录
    Create(data: any) {
        return request({
            url: this.baseurl + 'Add',
            method: 'post',
            data,
        });
    }

    // 更新记录
    Update(data: any) {
        return request({
            url: this.baseurl + 'Update',
            method: 'post',
            data,
        });
    }

    //Insert Or Update
    Save(data: any) {
        if (!data[this.key]) {
            return this.Create(data);
        }

        return this.Update(data);
    }

    // 删除指定数据
    DeleteMany(data: any) {
        return request({
            url: this.baseurl + 'Delete',
            method: 'post',
            data,
        });
    }

    // 删除指定数据
    DeleteByKey(id: any) {
        return request({
            url: this.baseurl + 'Delete/' + id,
            method: 'post',
            params: {},
        });
    }

    // 获取条件记录数量
    Count(params: any) {
        return request({
            url: this.baseurl,
            method: 'get',
            params,
        });
    }
    Export(data?: any){
        return request({
            url: this.baseurl + 'Export',
            method: 'post',
            data,
            responseType: 'blob'
        });
    }
}
