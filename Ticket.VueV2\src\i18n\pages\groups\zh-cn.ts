export default {
	groupSearch: {
		//查询区域
		searchKeyPlaceholder: '请输入',
	},
	groupButtons: {
		//非按钮
		createGroup: '创建员工组',
	},
	groupFields: {
		//table 列名
		id: 'Id',
		parentId: 'ParentId',
		path: 'Route Path',
		name: '员工组',
		description: '描述',
		status: '状态',
		users: '分配员工',
		Default: '设置组默认值',
		ShowDefault: '默认组',
		titleCreate: '创建员工组',
		titleEdit: '编辑员工组',
		DefaultText:'当团队成员添加到BPTS时，他们将自动分配到此组。只能有一个默认组。'
	},
	groupEditFields:{
		parentMenu:'Parent Menu',
		routerName:'Router Name',
	},
	groupCommonFields:{
		PrivilegeBelongsTo:'Privilege Belongs To',
		PermissionName:'Permission Name',
		DisplayName:'Display Name',
		createPermission:'Create Permission',
		editPermission:'Edit Permission',
	}
};
