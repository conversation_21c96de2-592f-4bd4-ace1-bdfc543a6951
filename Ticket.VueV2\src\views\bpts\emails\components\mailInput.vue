<template>
    <el-row @click="showInput" style="border:1px solid;width:100%;">
        <el-col>
            <template v-if="selectedIndex === -1">
                
                <el-tag style="margin-right: 5px;" v-for="tag in dynamicTags" :key="tag"
                    :type="tag.status === 'err' ? 'danger' : ''" class="mx-1" closable :disable-transitions="false"
                    @close="handleClose(tag)" @dblclick="editTag(tag)" >
                    
                    {{ tag.content }}
                     
                </el-tag>
                 
            </template>
            <!-- <el-input style="display: inline;" ref="InputRef" v-model="inputValue" size="small"
                @keyup.enter="handleInputEnter" @blur="handleInputConfirm" /> -->
            <div style="display: inline;">
            <el-autocomplete
                        ref="InputRef"
                        v-model="inputValue"
                        :fetch-suggestions="querySearch"
                        :trigger-on-focus="false"
                        value-key="name"
                        class="inline-input w-50"
                        placeholder="Please Input"
                        @select="handleSelect"
                        style="width:240px;"
                        size="small"
                        @keyup="handleInputEnter" 
                        @blur="inputSuccess"
                        @change="testChange"
                        />
                    </div>
        </el-col>
    </el-row>
</template>
    
<script lang="ts" setup>
import { nextTick, ref, defineProps, onMounted, getCurrentInstance } from 'vue'
import { ElInput } from 'element-plus'
import emailsApi from '/@/api/emails/index';

const inputValue = ref('')
const inputVisible = ref(false)
const InputRef = ref<InstanceType<typeof ElInput>>()

const selectedIndex = ref(-1);

const props = defineProps(['list']);
const dynamicTags = ref(props.list);
const allAddrData = ref([])
const { proxy } = <any>getCurrentInstance();

const tempQueryString = ref('')

const handleClose = (tag: string) => {
    dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1)
}

const showInput = () => {
    inputVisible.value = true
    nextTick(() => {
        //InputRef.value!.input!.focus()
        InputRef.value.focus()
    })
}

const handleInputEnter = (e) => {
    if(e.keyCode == 59){
        inputSuccess();
    }
    
}

const inputSuccess = () => {

    console.log('inputSuccess', inputValue.value);

    if (inputValue.value) {
            if (selectedIndex.value > -1) {
                dynamicTags.value[selectedIndex.value] = getEmailData(formatLabel(inputValue.value));

            } else {
                dynamicTags.value.push(getEmailData(formatLabel(inputValue.value)));
            }
        }
        inputVisible.value = false
        inputValue.value = ''
        selectedIndex.value = -1;
}

const editTag = (tag) => {

    selectedIndex.value = dynamicTags.value.indexOf(tag);

    inputValue.value = tag.email;
    nextTick(() => {
        showInput();
    });
}



const formatLabel = (str) => {
    return str.replace(/</g, '<').replace(/>/g, '>')
}

const getEmailData = (value) => {
    
    value = value.replace(';', '');
    // debugger
    const reg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/
    let data = null

    // 如果数据为空则邮件无信息，做删除处理
    if (value && value !== '' && value.trim() !== '') {
        const inputData = value.trim()
        let name = null
        let email = null

        if (!reg.test(inputData)) {
            // 如果有 < 且有 > 则处理数据，根据结果再判断是否满足邮件格式
            const stNum = inputData.split('<').length - 1
            const etNum = inputData.split('>').length - 1
            const stIndex = inputData.indexOf('<')
            const etIndex = inputData.indexOf('>')
            // < 或者 > 只存在一个且 < 的位置小于 > 的位置
            if (stNum === 1 && etNum === 1 && stIndex < etIndex) {
                const inputName = inputData.substr(0, stIndex)
                const inputEmail = inputData.substr(stIndex + 1).replaceAll('>', '')
                if (reg.test(inputEmail)) {
                    name = inputName
                    email = inputEmail
                }
            } else {
                email = inputData
            }
        } else {
            email = inputData
            name = email.split('@')[0]
        }
        if (name === '' && email !== '') name = email.split('@')[0]
        data = {
            content: name && email ? `${name}<${email}>` : inputData,
            name: name ? name.trim() : null,
            email: email ? email.trim() : null,
            status: name && email ? 'normal' : 'err'
        }
    }

    return data
}


const querySearch = (queryString, cb) => {
    tempQueryString.value = queryString

    const results = queryString
        ? allAddrData.value.filter(createFilter(queryString))
        : allAddrData.value
    cb(results)
}

const createFilter = (queryString: string) => {
    return (data) => {
        
        return (
            data.email.toLowerCase().indexOf(queryString.toLowerCase()) > -1
        )
    }
}

const handleSelect = (item) => {

    dynamicTags.value.forEach((element, index) => {
        if(tempQueryString.value == element.content){
            dynamicTags.value.splice(index, 1);
        }
    })

    inputValue.value = item.name ;
    inputSuccess();
}

const testChange = (value) => {
    inputValue.value = value;
    inputSuccess();
    
}

onMounted(async () => {
    emailsApi.GetEmails().then((rs) => {
        allAddrData.value = rs.data;
    });
});

</script>
<style scoped>
:deep(.el-input) {
    --el-input-focus-border: #fff;
    --el-input-transparent-border: 0 0 0 0px;
    --el-input-border-color: #fff;
    --el-input-hover-border: 0px !important;
    --el-input-hover-border-color: #fff;
    --el-input-focus-border-color: #fff;
    --el-input-clear-hover-color: #fff;
    box-shadow: 0 0 0 0px !important;
    --el-input-border: 0px;
}

:deep(.el-select .el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 0px !important;
}

:deep(.el-select .el-input.is-focus .el-input__wrapper) {
    box-shadow: 0 0 0 0px !important;
}

:deep(.el-select) {
    --el-select-border-color-hover: #fff;
}
</style>

