{"$id": "1", "activities": [{"$id": "2", "activityId": "a9a3058c-e6b7-4ffe-9957-1e718f7cda78", "type": "SignalReceived", "name": "TestSignalStart1", "displayName": "触发流程", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "3", "name": "Signal", "expressions": {"$id": "4", "Literal": "TestSignalStart1"}}], "propertyStorageProviders": {}}, {"$id": "5", "activityId": "27405adc-b421-42e9-814d-c3ef1b9df2b8", "type": "InitWorkflow", "name": "发起人", "displayName": "发起人", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "6", "name": "Content", "expressions": {"$id": "7", "Literal": "InitWorkflow"}}], "propertyStorageProviders": {}}, {"$id": "8", "activityId": "42b9ae86-54a6-43c5-ad78-ca9de002c203", "type": "Fork", "name": "选择分支", "displayName": "选择分支", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "9", "name": "Branches", "expressions": {"$id": "10", "Json": "[\"app1\",\"app2\"]"}}], "propertyStorageProviders": {}}, {"$id": "11", "activityId": "903f8a0e-f642-41bd-a5da-b7f1b6051bcb", "type": "SignalReceived", "name": "app1_sig", "displayName": "app1_sig", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "12", "name": "Signal", "expressions": {"$id": "13", "Literal": "app1_sig"}}], "propertyStorageProviders": {}}, {"$id": "14", "activityId": "46dc57b9-b9a7-4066-8642-2e9196ecf02b", "type": "Approved", "name": "app1", "displayName": "审核人", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "15", "name": "Content", "expressions": {"$id": "16", "Literal": "Approved"}}], "propertyStorageProviders": {}}, {"$id": "17", "activityId": "ba495901-0564-4121-a93a-ec5d0b3bacdf", "type": "SignalReceived", "name": "app2_sig", "displayName": "app2_sig", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "18", "name": "Signal", "expressions": {"$id": "19", "Literal": "app2_sig"}}], "propertyStorageProviders": {}}, {"$id": "20", "activityId": "deef7d58-9aab-4022-9b02-0a7c3e24bc7a", "type": "Approved", "name": "app2", "displayName": "审核人", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "21", "name": "Content", "expressions": {"$id": "22", "Literal": "Approved"}}], "propertyStorageProviders": {}}], "connections": [{"$id": "23", "sourceActivityId": "a9a3058c-e6b7-4ffe-9957-1e718f7cda78", "targetActivityId": "27405adc-b421-42e9-814d-c3ef1b9df2b8", "outcome": "Done"}, {"$id": "24", "sourceActivityId": "27405adc-b421-42e9-814d-c3ef1b9df2b8", "targetActivityId": "42b9ae86-54a6-43c5-ad78-ca9de002c203", "outcome": "Done"}, {"$id": "25", "sourceActivityId": "42b9ae86-54a6-43c5-ad78-ca9de002c203", "targetActivityId": "903f8a0e-f642-41bd-a5da-b7f1b6051bcb", "outcome": "app1"}, {"$id": "26", "sourceActivityId": "903f8a0e-f642-41bd-a5da-b7f1b6051bcb", "targetActivityId": "46dc57b9-b9a7-4066-8642-2e9196ecf02b", "outcome": "app1"}, {"$id": "27", "sourceActivityId": "42b9ae86-54a6-43c5-ad78-ca9de002c203", "targetActivityId": "ba495901-0564-4121-a93a-ec5d0b3bacdf", "outcome": "app2"}, {"$id": "28", "sourceActivityId": "ba495901-0564-4121-a93a-ec5d0b3bacdf", "targetActivityId": "deef7d58-9aab-4022-9b02-0a7c3e24bc7a", "outcome": "app2"}], "variables": {"$id": "29", "data": {}}, "contextOptions": {"$id": "30", "contextType": "System.Object, mscorlib", "contextFidelity": "<PERSON><PERSON><PERSON>"}, "customAttributes": {"$id": "31", "data": {}}, "channel": "Channelb5f2d4ce-6f52-4087-96c2-1344d4e4aaaf"}