/* 初始化样式
------------------------------- */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
	outline: none !important;
}

:root {
	--next-color-white: #ffffff;
	--next-bg-main-color: #f8f8f8;
	--next-bg-color: #f5f5ff;
	--next-border-color-light: #f1f2f3;
	--next-color-primary-lighter: #ecf5ff;
	--next-color-success-lighter: #f0f9eb;
	--next-color-warning-lighter: #fdf6ec;
	--next-color-danger-lighter: #fef0f0;
	--next-color-dark-hover: #0000001a;
	--next-color-menu-hover: rgba(0, 0, 0, 0.2);
	--next-color-user-hover: rgba(0, 0, 0, 0.04);
	--next-color-seting-main: #e9eef3;
	--next-color-seting-aside: #d3dce6;
	--next-color-seting-header: #b3c0d1;
}

html,
body,
#app {
	margin: 0;
	padding: 0;
	width: 100%;
	height: 100%;
	font-family:
		Helvetica Neue,
		Helvetica,
		PingFang SC,
		Hiragino Sans GB,
		Microsoft YaHei,
		SimSun,
		sans-serif;
	font-weight: 400;
	-webkit-font-smoothing: antialiased;
	-webkit-tap-highlight-color: transparent;
	background-color: var(--next-bg-main-color);
	font-size: 12px !important;
	overflow: hidden;
	position: relative;
}

/* 主布局样式
------------------------------- */
.layout-container {
	width: 100%;
	height: 100%;

	.layout-pd {
		padding: 15px !important;
	}

	.layout-flex {
		display: flex;
		flex-direction: column;
		flex: 1;
	}

	.layout-aside {
		background: var(--next-bg-menuBar);
		box-shadow: 2px 0 6px rgb(0 21 41 / 1%);
		height: inherit;
		position: relative;
		z-index: 1;
		display: flex;
		flex-direction: column;
		overflow-x: hidden !important;

		.el-scrollbar__view {
			// overflow: hidden;
		}
	}

	.layout-header {
		padding: 0 !important;
		height: auto !important;
	}

	.layout-main {
		padding: 0 !important;
		overflow: hidden;
		width: 100%;
		background-color: var(--next-bg-main-color);
		display: flex;
		flex-direction: column;

		// 内层 el-scrollbar样式，用于界面高度自适应（main.vue）
		.layout-main-scroll {
			@extend .layout-flex;

			.layout-parent {
				@extend .layout-flex;
				position: relative;
				overflow: hidden;
			}
		}
	}

	.scrolly {
		overflow-y: auto !important;
		overflow-x: hidden;
	}

	@media screen and (max-width: 1024px) {
		.layout-parent {
			overflow: auto !important;
		}
	}

	// 用于界面高度自适应
	.layout-padding {
		@extend .layout-pd;
		position: absolute;
		left: 0;
		top: 0;
		height: 100%;
		overflow: hidden;
		@extend .layout-flex;

		&-auto {
			height: inherit;
			@extend .layout-flex;
		}

		&-view {
			background: var(--el-color-white);
			width: 100%;
			height: 100%;
			border-radius: 4px;
			border: 1px solid var(--el-border-color-light, #ebeef5);
			overflow: hidden;
		}
	}

	// 用于界面高度自适应，主视图区 main 的内边距，用于 iframe
	.layout-padding-unset {
		padding: 0 !important;

		&-view {
			border-radius: 0 !important;
			border: none !important;
		}
	}

	// 用于设置 iframe loading 时的高度（loading 垂直居中显示）
	.layout-iframe {
		.el-loading-parent--relative {
			height: 100%;
		}
	}

	.el-scrollbar {
		width: 100%;
	}

	.layout-el-aside-br-color {
		border-right: 1px solid var(--el-border-color-light, #ebeef5);
	}

	.sidebar-close {
		width: 0px;
	}

	// pc端左侧导航样式
	.layout-aside-pc-220 {
		width: 220px !important;
		transition: width 0.3s ease;
	}

	.layout-aside-pc-64 {
		width: 64px !important;
		transition: width 0.3s ease;
	}

	.layout-aside-pc-1 {
		width: 1px !important;
		transition: width 0.3s ease;
		position: relative;
		left: -1px;
	}

	// 手机端左侧导航样式
	.layout-aside-mobile {
		position: fixed;
		top: 0;
		left: -220px;
		width: 220px;
		z-index: 9999999;
	}

	.layout-aside-mobile-close {
		left: -220px;
		transition: all 0.3s cubic-bezier(0.39, 0.58, 0.57, 1);
	}

	.layout-aside-mobile-open {
		left: 0;
		transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
	}

	.layout-aside-mobile-mode {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 9999998;
		animation: error-img 0.3s;
	}

	.layout-mian-height-50 {
		height: calc(100vh - 50px);
	}

	.layout-columns-warp {
		flex: 1;
		display: flex;
		overflow: hidden;
	}

	.layout-hide {
		display: none;
	}
}

/* element plus 全局样式
------------------------------- */
.layout-breadcrumb-seting {
	.el-divider {
		background-color: rgb(230, 230, 230);
	}
}

/* nprogress 进度条跟随主题颜色
------------------------------- */
#nprogress {
	.bar {
		background: var(--el-color-primary) !important;
		z-index: 9999999 !important;
	}
}

/* flex 弹性布局
------------------------------- */
.flex {
	display: flex;
}

.flex-auto {
	flex: 1;
	overflow: hidden;
}

.flex-center {
	@extend .flex;
	flex-direction: column;
	width: 100%;
	overflow: hidden;
}

.flex-margin {
	margin: auto;
}

.flex-warp {
	display: flex;
	flex-wrap: wrap;
	align-content: flex-start;
	margin: 0 -5px;

	.flex-warp-item {
		padding: 5px;

		.flex-warp-item-box {
			width: 100%;
			height: 100%;
		}
	}
}

/* cursor 鼠标形状
------------------------------- */
// 默认
.cursor-default {
	cursor: default !important;
}

// 帮助
.cursor-help {
	cursor: help !important;
}

// 手指
.cursor-pointer {
	cursor: pointer !important;
}

// 移动
.cursor-move {
	cursor: move !important;
}

/* 宽高 100%
------------------------------- */
.w100 {
	width: 100% !important;
}

.h100 {
	height: 100% !important;
}

.vh100 {
	height: 100vh !important;
}

.max100vh {
	max-height: 100vh !important;
}

.min100vh {
	min-height: 100vh !important;
}

/* 颜色值
------------------------------- */
.color-primary {
	color: var(--el-color-primary);
}

.color-success {
	color: var(--el-color-success);
}

.color-warning {
	color: var(--el-color-warning);
}

.color-danger {
	color: var(--el-color-danger);
}

.color-info {
	color: var(--el-color-info);
}

/* 字体大小全局样式
------------------------------- */
@for $i from 10 through 32 {
	.font#{$i} {
		font-size: #{$i}px !important;
	}
}

/* 外边距、内边距全局样式
------------------------------- */
@for $i from 1 through 35 {
	.mt#{$i} {
		margin-top: #{$i}px !important;
	}

	.mr#{$i} {
		margin-right: #{$i}px !important;
	}

	.mb#{$i} {
		margin-bottom: #{$i}px !important;
	}

	.ml#{$i} {
		margin-left: #{$i}px !important;
	}

	.pt#{$i} {
		padding-top: #{$i}px !important;
	}

	.pr#{$i} {
		padding-right: #{$i}px !important;
	}

	.pb#{$i} {
		padding-bottom: #{$i}px !important;
	}

	.pl#{$i} {
		padding-left: #{$i}px !important;
	}
}

/*发票录入*/
.drawerBG {
	background: #f6f8f9;
}

/*树形*/

.ti {
	margin-right: 5px;
}

.el-tree-node {
	padding-top: 10px;
}

.tablelist {
	padding: 5px;
	height: 100%;
	overflow: hidden;
}

.tablelist .el-tabs--border-card>.el-tabs__content {
	padding: 0px;
}

.el-menu--horizontal>.el-sub-menu .el-sub-menu__title {
	color: rgba(255, 255, 255, 0.6);
}

.inv .el-card__header {
	padding: 5px 20px;
}

.inv .el-main {
	padding: 0px;
}

.aside {
	margin: 10px;
	background-color: #fff;
	margin-top: 20px;
}

.role-index-tree .el-menu {
	background-color: #f5f5ff;
}

.role-index-tree .el-menu-item {
	color: black !important;
}

.role-index-tree .el-menu-item .do {
	color: #ffffff;
}

/*角色首页*/
.role-index-tree .el-menu-item.is-active {
	background-color: #ecf5ff !important;
	color: #409eff;
}

.role-index-tree .el-menu-item.is-active span {
	color: #409eff;
}

.role-index-tree .el-menu-item.is-active .el-dropdown {
	color: #409eff;
}

.role-index-tree .el-menu-item:hover {
	background-color: #f5f7fa !important;
}

.role-index-tree .tab-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 10px 0 20px;
	height: 38px;
	border-radius: 2px;
	cursor: pointer;
	transition:
		color 0.1s ease-in-out,
		background-color 0.1s ease-in-out;
}

.role-index-tree .role-index-header-title {
	font-size: 18px;
	font-weight: 500;
	color: #303133;
}

.role-index-tree .el-sub-menu__icon-arrow {
	display: none;
}

.role-index-tree .el-dropdown {
	// color: #d3d4d6;
}

.group-title {
	padding: 20px 20px 10px;
	font-size: 15px;
	font-weight: 700;
	color: #727f8f;
}

.workflow-info-container .el-tabs__content {
	height: 100%;
}

.w-30 {
	width: 30.5rem;
}

.w-20 {
	width: 18.5rem;
}

.w-15 {
	width: 15.5rem;
}

.w-50 {
	width: 12.5rem;
}

.m-2,
[m~='\32 '] {
	margin: 0.5rem;
}

.list-search-lable {
	--un-text-opacity: 1;
	color: rgba(75, 85, 99, var(--un-text-opacity));
	align-items: center;
	display: inline-flex;
}

.list-search-card {}

.pdfView-card .el-card__body {
	height: 100%;
	overflow: hidden;
}

/*工作流*/
.approval-setting .el-card__header {
	padding: 0px 20px;
	background-color: #f5f7fa;
}

.approval-setting .el-card__body {
	padding: 10px 5px;
}

/*工单旧的*/
.ticket_list-container .el-tabs__header {
	margin: 0px;
	background-color: #fff;
	border-width: 0px;
	margin-left: 10px;
}

.ticket_list_c .el-card__body {
	padding: 12px 0px 0px 0px;
}

.ticket_list_c .el-card__header {
	padding: 5px 20px;
}

.tick_top .flex-auto {
	text-align: center;
}

.tick_top .block:not(:last-child) {
	border-right: 1px solid var(--el-border-color);
}

.tk_c-item {
	text-align: center;
}

.tk-aside .el-card__header {
	padding: 20px 20px;
}

.tk-aside .el-card__body {
	padding: 5px;
}

.tk-aside .el-descriptions--large .el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell {
	padding: 8px 11px;
}

/*工单list*/
.list-page-layout {
	height: 100%;
	padding: 5px 8px;
	background-color: #ebeef5;
	overflow: hidden;
}

.list-page-layout .el-header,
.list-page-layout .el-footer,
.list-page-layout .el-main,
.list-page-layout .el-aside {
	display: block;
	padding: 0px;
}

.list-page-layout .el-header,
.list-page-layout .el-footer {
	background-color: #ebeef5;
}

.list-page-layout .el-aside {
	background-color: var(--el-color-primary-light-8);
	color: var(--el-text-color-primary);
	text-align: center;
}

.list-page-layout .el-main {
	background-color: #ffffff;
	color: var(--el-text-color-primary);
	// text-align: center;
}

.list-page-layout .el-header.table_header {
	background-color: #ffffff;
	padding: 0px 10px;
	height: 40px;
}

.el-header.table_header {
	background-color: #ffffff;
	padding: 0px 10px;
	height: 40px;
	border-bottom: 1px solid #e6e6e6;
}

.list-page-layout .el-header .el-card__body {
	padding-bottom: 5px;
}

.list-page-layout .el-card {
	float: left;
}

.list-page-layout .el-card.ticket_list_c {
	margin-right: 10px;
	padding: 0px 20px;
}

.list-page-layout .tk_c-item:first-child {
	border-right: 1px solid #d3dce6;
	padding: 0px 15px 0px 10px;
}

.list-page-layout .tk_c-item {
	display: inline-block;
	padding: 0px 15px 0px 10px;
	margin-bottom: 10px;
}

.list-page-layout .tk_c-item span {
	padding: 3px 0px;
	display: block;
}

.list-page-layout .tk_c-item:last-child {
	border-right: none;
}

.ticket_list_c .card-header {
	padding: 5px;
}

/*工单info*/
.ticket-info_layout {
	height: 100%;
	// padding: 10px;
	padding: 5px 5px 0px 5px;

	.ticket-info-left {
		.el-card__body {
			height: 90%;
			overflow-y: auto;
			overflow-x: hidden;
			padding-bottom: 45px;
		}
	}

	.ticket-info-right {
		.list-card {
			.el-card__body {
				height: 90%;
				overflow-y: auto;
				overflow-x: hidden;
			}
		}
	}
}

.ticket-info_layout .tk-aside {
	width: 500px;
	// margin-right: 10px;
	margin-right: 2px;
}

.ticket-info_layout .tk-container {
	min-width: 600px;
	overflow: hidden;
}

.ticket-info_layout .tk-header {
	display: block;
}

.ticket-info_layout .tk-footer {
	height: 280px;
	padding: 0px;
}

.ticket-info_layout .tk-footer.full-height {
	height: auto;
}

.ticket-info_layout .tk-aside,
.ticket-info_layout .tk-header,
.ticket-info_layout .tk-main,
.ticket-info_layout .tk-footer {
	/* border: 1px solid red; */
	// margin-bottom: 10px;
	margin-bottom: 2px;
	background-color: #fff;
}

.tk-header .button-group {
	//width: 600px;
	// float: right;
	text-align: right;
	float: right;
	display: flex;
}

.form-item-border {
	border: 1px solid #dcdfe6;
	padding: 10px;
}

.tl-item {
	display: flex;
}

.timeline-avatar {
	flex-shrink: 0;
}

.timeline-content {
	flex-grow: 1;
	margin-left: 15px;
}

.timeline-content h5 {
	display: inline-block;
}

.timeline-content div {
	margin-bottom: 5px;
}

.timeline-content_to {
	margin-right: 3px;
}

.timeline-time-description {
	border: 1px solid red;
}

.timeline-conte_type {
	margin-left: 10px;
}

.elicon {
	margin-top: 5px;
}

.link_text {
	margin-left: 5px;
}

.ele-icon {
	vertical-align: middle;
	margin-right: 5px;
}

.descriptions-item::before {
	content: '*';
	color: var(--el-color-danger);
	margin-right: 4px;
}

.head-count {
	cursor: pointer;
	margin-top: 10px;
}

.head-count:hover {
	text-decoration: underline;
	color: blue;
}

.ticket-info_layout .el-upload-list {
	margin: 0px;
}

.ticket-info_layout .el-upload-list__item {
	margin-bottom: 0px;
	height: 35px;
}

.el-upload-list__item .el-icon--close {
	color: red;
}

.internal-content {
	border: 1px solid #fed9ae;
	background-color: #fff7ed;
	padding: 20px;
	border-radius: 7px;
}

.ticketTable {
	font-size: 12px;
}

.user-dropdown {
	li {
		font-size: 12px;
	}
}

.el-select-dropdown__item {
	font-size: 12px;
}

.el-tree {
	width: auto !important;
	font-size: 12px !important;
}

// .el-input{
// 	height: 30px !important;
// }

ul li {
	font-size: 12px !important;
}

ul li span {
	font-size: 12px !important;
}

// h5,p,table,label,input{
h5,
p,
table,
label,
input,
.scTable-table span,
.el-checkbox span,
.el-select span,
.el-radio span {
	font-size: 12px !important;
}

.el-dialog__title {
	font-size: 14px !important;
	font-weight: bold;
}

.el-form-item__error {
	display: none;
}

.el-form-item__label {
	font-size: 12px !important;
}