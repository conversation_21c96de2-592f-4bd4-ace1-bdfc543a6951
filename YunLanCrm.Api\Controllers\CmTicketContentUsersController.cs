﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmTicketContentUsers;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmTicketContentUsersController : ControllerBase
    {
        private readonly ILogger<CmTicketContentUsersInfo> _logger;
        private readonly ICmTicketContentUsersService _cmTicketContentUsersService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmTicketContentUsersService"></param>
        /// <param name="logger"></param>
        public CmTicketContentUsersController(ICmTicketContentUsersService cmTicketContentUsersService, ILogger<CmTicketContentUsersInfo> logger)
        {
            _logger = logger;
            _cmTicketContentUsersService = cmTicketContentUsersService;
            
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CmTicketContentUsersAddOrUpdateDto req)
        {
            return await _cmTicketContentUsersService.AddCmTicketContentUsers(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmTicketContentUsersAddOrUpdateDto req)
        {
            return await _cmTicketContentUsersService.UpdateCmTicketContentUsers(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _cmTicketContentUsersService.Delete(a => a.Id == id);

            return result > 0;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _cmTicketContentUsersService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmTicketContentUsersDto> Get(long id)
        {
            return await _cmTicketContentUsersService.QueryInfo<CmTicketContentUsersDto>(a => a.Id == id);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmTicketContentUsersDto>> GetList([FromQuery] CmTicketContentUsersListQueryDto req)
        {
            return await _cmTicketContentUsersService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<CmTicketContentUsersDetailDto> Detail(long id)
        {
            return await _cmTicketContentUsersService.Detail(id);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmTicketContentUsersDetailDto>> DetailList([FromQuery] CmTicketContentUsersListQueryDto req)
        {
            return await _cmTicketContentUsersService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmTicketContentUsersDetailDto>> Query([FromQuery] CmTicketContentUsersPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmTicketContentUsersDetailDto>> QueryPost(CmTicketContentUsersPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmTicketContentUsersDetailDto>> PageQuery([FromQuery] CmTicketContentUsersPageQueryDto req)
        {
            return await _cmTicketContentUsersService.PageQueryView(req);
        }

    }
}