﻿using Elsa;
using Elsa.Activities.UserTask.Activities;
using Elsa.Activities.UserTask.Models;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Models;
using Elsa.Serialization;
using Elsa.Services.Models;
using Elsa.Services.WorkflowStorage;
using YunLanCrm.Dto.WorkflowExecutionLogRecords;

namespace YunLanCrm.Api.ElsaWorkflows.Activities
{
    [Action(Category = "基础节点", DisplayName = "用户交互", Description = "用户交互（UserTask）", Outcomes = new[] { OutcomeNames.Done })]
    public class UserTaskCrm : UserTask
    {
        IWorkflowStorageService _workflowStorageService;
        public UserTaskCrm(IContentSerializer serializer, IWorkflowStorageService workflowStorageService) : base(serializer)
        {
            _workflowStorageService = workflowStorageService;
        }

        [ActivityInput(Hint = "Config", SupportedSyntaxes = new[] { SyntaxNames.Literal, SyntaxNames.JavaScript, SyntaxNames.Liquid })]
        public string Config { get; set; } = default;

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            //var workflowInstance = context.WorkflowInstance;
            //await _workflowStorageService.UpdateInputAsync(workflowInstance, new WorkflowInput("Open"));

            if (context.Input is WorkflowExecutionDto)
            {
                var input = context.GetInput<WorkflowExecutionDto>();
                if (!string.IsNullOrWhiteSpace(input.Action))
                {
                    return Outcome(input.Action, input);
                }
                else if (!string.IsNullOrWhiteSpace(input.Branch))
                {
                    return Outcome(input.Branch, input);
                }
            }

            return await base.OnExecuteAsync(context);
        }
    }
}
