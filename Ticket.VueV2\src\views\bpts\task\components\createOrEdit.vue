﻿<template>
	<div class="taskQueue-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="769px" draggable>
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="120px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="任务名称" prop="name">
							<el-input v-model="ruleForm.name" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="任务组">
							<el-select v-model="ruleForm.jobGroup" placeholder="请选择" clearable>
								<el-option v-for="item in taskGroupData" :label="item.name" :value="item.itemId" :key="item.itemId"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="运行状态">
							<el-select v-model="ruleForm.isStart" placeholder="请选择" clearable>
								<el-option label="运行" :value="true"></el-option>
								<el-option label="停止" :value="false"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="程序集" prop="assemblyName">
							<el-select v-model="ruleForm.assemblyName">
								<el-option value="YunLanCrm.Tasks">YunLanCrm.Tasks</el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="执行类" prop="className">
							<el-input v-model="ruleForm.className" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="任务模式" prop="cron">
							<el-radio-group v-model="ruleForm.triggerType">
								<el-radio-button :label="0">Simple模式</el-radio-button>
								<el-radio-button :label="1">Cron模式</el-radio-button>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="cron表达式" prop="cron">
							<sc-cron v-model="ruleForm.cron" placeholder="请输入Cron定时规则" :disabled="ruleForm.triggerType == 0" clearable> </sc-cron>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="开始时间" prop="beginTime">
							<el-date-picker v-model="ruleForm.beginTime" type="date" placeholder="Pick a day" class="w100"> </el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="结束时间" prop="endTime">
							<el-date-picker v-model="ruleForm.endTime" type="date" placeholder="Pick a day" class="w100"> </el-date-picker>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="循环周期(秒)">
							<el-input-number v-model="ruleForm.intervalSecond" :min="0" class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="循环次数(次)">
							<el-input-number v-model="ruleForm.cycleRunTimes" :min="0" class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="参数">
							<el-input v-model="ruleForm.jobParams" type="textarea" placeholder="Please input " />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="备注">
							<el-input v-model="ruleForm.remark" type="textarea" placeholder="请输入备注" maxlength="150"> </el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button v-if="dialogParams.action === 'Edit'" @click="onDelete" type="danger" size="small"> Delete</el-button>
					<el-button :loading="saveLoading" type="primary" @click="onSubmit" size="small">Save</el-button>
					<el-button v-if="dialogParams.action === 'Create'" @click="onInitForm" size="small" type="danger"> Reset</el-button>
					<el-button @click="onCancel" size="small">Cannel</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent, watch } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';

import dictItemApi from '/@/api/dictItem';
import taskQueueApi from '/@/api/taskQueue';

interface DialogParams {
	action: string;
	id: number;
}

export default defineComponent({
	name: 'taskQueueCreateOrEdit',
	components: {},
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			title: 'Create Task',
			isShowDialog: false,
			saveLoading: false,
			deleteLoading: false,
			dialogParams: {
				action: '',
				id: -1,
			},
			ruleForm: {
				id: 0, //
				name: '', //
				jobGroup: 'Logs', //
				cron: '0 0 0 * * ?', //
				assemblyName: 'YunLanCrm.Tasks', //
				className: '', //
				remark: '', //
				runTimes: 0, //
				beginTime: new Date(), //
				endTime: new Date(), //
				triggerType: 0, //
				intervalSecond: 0, //
				cycleRunTimes: 0, //
				isStart: false, //
				jobParams: '', //
			},
			rules: {
				name: [{ required: true, message: 'Please input', trigger: 'blur' }],
				assemblyName: [{ required: true, message: 'Please input', trigger: 'blur' }],
				beginTime: [{ type: 'date', required: true, message: 'Pick date', trigger: 'change' }],
				endTime: [{ type: 'date', required: true, message: 'Pick date', trigger: 'change' }],
				triggerType: [{ required: true, message: 'Please input', trigger: 'blur' }],
				intervalSecond: [{ required: true, message: 'Please input', trigger: 'blur' }],
				cycleRunTimes: [{ required: true, message: 'Please input', trigger: 'blur' }],
				cron: [{ required: true, message: 'Please input', trigger: 'blur' }],
				className: [{ required: true, message: 'Please input', trigger: 'blur' }],

				//type1: [{ type: 'array', required: true, message: '', trigger: 'change' }],
			},
			taskGroupData: [],
			shortcuts: [
				{
					text: '每天8点和12点 (自定义追加)',
					value: '0 0 8,12 * * ?',
				},
			],
		});
		// 打开弹窗
		const openDialog = (params: DialogParams) => {
			state.dialogParams = params;

			onInitForm();

			if (params.action == 'Create') {
				state.title = 'Create task';
			} else if (params.action == 'Edit') {
				state.title = 'Edit task';
				getData(params.id);
			} else {
				ElMessage.error('Parameter action cannot be empty.');
			}

			//获取task group
			dictItemApi.GetList({ dictId: 8 }).then((rs) => {
				state.taskGroupData = rs.data;
			});
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}
				var obj = state.ruleForm;
				state.saveLoading = true;
				taskQueueApi
					.Save(obj)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};
		//根据id获取完整的信息
		const getData = (id: any) => {
			taskQueueApi.GetByKey(id).then((rs) => {
				state.ruleForm = Object.assign({}, rs.data);
			});
		};
		//删除纪录
		const onDelete = () => {
			ElMessageBox.confirm(`Are you sure to delete this?`, 'Tips', {
				confirmButtonText: 'OK',
				cancelButtonText: 'No,Thanks',
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				state.deleteLoading = true;
				taskQueueApi
					.DeleteByKey(state.ruleForm.id)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.deleteLoading = false;
					});
			});
		};
		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.ruleForm = {
				id: 0, //
				name: '', //
				jobGroup: 'Logs', //
				cron: '0 0 0 * * ?', //
				assemblyName: 'YunLanCrm.Tasks', //
				className: '', //
				remark: '', //
				runTimes: 0, //
				beginTime: new Date(), //
				endTime: new Date(), //
				triggerType: 0, //
				intervalSecond: 0, //
				cycleRunTimes: 0, //
				isStart: false, //
				jobParams: '', //
			};
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			...toRefs(state),
		};
	},
});
</script>
