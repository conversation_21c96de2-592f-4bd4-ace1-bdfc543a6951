﻿using System;
using System.Text;

namespace YunLanCrm.Common.Helper
{
    public static class PasswordGenerator
    {
        private static readonly string UppercaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        private static readonly string LowercaseLetters = "abcdefghijklmnopqrstuvwxyz";
        private static readonly string Numbers = "0123456789";
        private static readonly string SpecialCharacters = "~!@#$%^&*()_+";

        public static string GeneratePassword()
        {
            Random random = new Random();
            StringBuilder password = new StringBuilder();

            // 保证包含至少一个大写字母
            password.Append(UppercaseLetters[random.Next(UppercaseLetters.Length)]);
            // 保证包含至少一个小写字母
            password.Append(LowercaseLetters[random.Next(LowercaseLetters.Length)]);
            // 保证包含至少一个数字
            password.Append(Numbers[random.Next(Numbers.Length)]);
            // 保证包含至少一个特殊字符
            password.Append(SpecialCharacters[random.Next(SpecialCharacters.Length)]);

            // 随机填充剩余的字符，直到密码长度达到8个字符或更多
            string allCharacters = UppercaseLetters + LowercaseLetters + Numbers + SpecialCharacters;
            while (password.Length < 8)
            {
                password.Append(allCharacters[random.Next(allCharacters.Length)]);
            }

            // 打乱字符顺序
            char[] passwordArray = password.ToString().ToCharArray();
            for (int i = passwordArray.Length - 1; i > 0; i--)
            {
                int j = random.Next(i + 1);
                char temp = passwordArray[i];
                passwordArray[i] = passwordArray[j];
                passwordArray[j] = temp;
            }

            return new string(passwordArray);
        }
    }

}
