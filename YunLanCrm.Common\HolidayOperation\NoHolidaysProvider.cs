﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hczb.Tools.HolidayOperation
{
    /// <summary>
    /// NoHolidaysProvider
    /// </summary>
    public class NoHolidaysProvider : IPublicHolidayProvider
    {
        private static readonly Lazy<IPublicHolidayProvider> _instance =
            new Lazy<IPublicHolidayProvider>(() => new NoHolidaysProvider());

        /// <summary>
        /// Gets the singleton instance of <see cref="NoHolidaysProvider"/>.
        /// </summary>
        public static IPublicHolidayProvider Instance
        {
            get { return _instance.Value; }
        }

        ///<inheritdoc/>
        public IEnumerable<PublicHoliday> Get(int year)
        {
            return Enumerable.Empty<PublicHoliday>();
        }
    }
}
