﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmOpenItemDetailRelatedTicket;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmOpenItemDetailRelatedTicketController : ControllerBase
    {
        private readonly ILogger<CmOpenItemDetailRelatedTicketInfo> _logger;
        private readonly ICmOpenItemDetailRelatedTicketService _cmOpenItemDetailRelatedTicketService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmOpenItemDetailRelatedTicketService"></param>
        /// <param name="logger"></param>
        public CmOpenItemDetailRelatedTicketController(ICmOpenItemDetailRelatedTicketService cmOpenItemDetailRelatedTicketService, ILogger<CmOpenItemDetailRelatedTicketInfo> logger)
        {
            _logger = logger;
            _cmOpenItemDetailRelatedTicketService = cmOpenItemDetailRelatedTicketService;
            
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CmOpenItemDetailRelatedTicketAddOrUpdateDto req)
        {
            return await _cmOpenItemDetailRelatedTicketService.AddCmOpenItemDetailRelatedTicket(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmOpenItemDetailRelatedTicketAddOrUpdateDto req)
        {
            return await _cmOpenItemDetailRelatedTicketService.UpdateCmOpenItemDetailRelatedTicket(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _cmOpenItemDetailRelatedTicketService.Delete(a => a.Id == id);

            return result > 0;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _cmOpenItemDetailRelatedTicketService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmOpenItemDetailRelatedTicketDto> Get(long id)
        {
            return await _cmOpenItemDetailRelatedTicketService.QueryInfo<CmOpenItemDetailRelatedTicketDto>(a => a.Id == id);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmOpenItemDetailRelatedTicketDto>> GetList([FromQuery] CmOpenItemDetailRelatedTicketListQueryDto req)
        {
            return await _cmOpenItemDetailRelatedTicketService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<CmOpenItemDetailRelatedTicketDto> Detail(long id)
        {
            return await _cmOpenItemDetailRelatedTicketService.Detail(id);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmOpenItemDetailRelatedTicketDto>> DetailList([FromQuery] CmOpenItemDetailRelatedTicketListQueryDto req)
        {
            return await _cmOpenItemDetailRelatedTicketService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmOpenItemDetailRelatedTicketDto>> Query([FromQuery] CmOpenItemDetailRelatedTicketPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmOpenItemDetailRelatedTicketDto>> QueryPost(CmOpenItemDetailRelatedTicketPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmOpenItemDetailRelatedTicketDto>> PageQuery([FromQuery] CmOpenItemDetailRelatedTicketPageQueryDto req)
        {
            return await _cmOpenItemDetailRelatedTicketService.PageQueryView(req);
        }

    }
}