// versionUtils.ts
import fs from 'fs';
import path from 'path';

// 递增版本号的方法
export const incrementVersion = (version: string): string => {
  const versionParts = version.split('.').map(Number);

  if (versionParts.length === 3) {
    versionParts[2] += 1;
    if (versionParts[2] >= 10) {
      versionParts[2] = 0;
      versionParts[1] += 1;
      if (versionParts[1] >= 10) {
        versionParts[1] = 0;
        versionParts[0] += 1;
      }
    }
  }

  return versionParts.join('.');
};

// 更新版本号的方法
export const updateVersionJson = (publicPath: string, distPath: string) => {
  const filePath = path.resolve(publicPath, 'version.json');
  if (fs.existsSync(filePath)) {
    const existingData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
    let version = existingData.version;
    if (version) {
      version = incrementVersion(version);
    }
    existingData.version = version;

    // 更新 public 文件夹中的版本号
    fs.writeFileSync(filePath, JSON.stringify(existingData, null, 2));
    console.log('version.json file in public folder updated successfully!');

    // 同步更新 dist 文件夹中的版本号
    const distFilePath = path.resolve(distPath, 'version.json');
    fs.writeFileSync(distFilePath, JSON.stringify(existingData, null, 2));
    console.log('version.json file in dist folder updated successfully!');
  } else {
    console.log('version.json file does not exist in public folder.');
  }
};
