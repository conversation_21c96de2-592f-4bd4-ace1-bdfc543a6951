﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmTicketContents;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmTicketContentsController : ControllerBase
    {
        private readonly ILogger<CmTicketContentsInfo> _logger;
        private readonly ICmTicketContentsService _cmTicketContentsService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmTicketContentsService"></param>
        /// <param name="logger"></param>
        public CmTicketContentsController(ICmTicketContentsService cmTicketContentsService, ILogger<CmTicketContentsInfo> logger)
        {
            _logger = logger;
            _cmTicketContentsService = cmTicketContentsService;
            
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CmTicketContentsAddOrUpdateDto req)
        {
            return await _cmTicketContentsService.AddCmTicketContents(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmTicketContentsAddOrUpdateDto req)
        {
            return await _cmTicketContentsService.UpdateCmTicketContents(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _cmTicketContentsService.Delete(a => a.Id == id);

            return result > 0;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _cmTicketContentsService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmTicketContentsDto> Get(long id)
        {
            return await _cmTicketContentsService.QueryInfo<CmTicketContentsDto>(a => a.Id == id);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmTicketContentsDto>> GetList([FromQuery] CmTicketContentsListQueryDto req)
        {
            return await _cmTicketContentsService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<CmTicketContentsDetailDto> Detail(long id)
        {
            return await _cmTicketContentsService.Detail(id);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmTicketContentsDetailDto>> DetailList([FromQuery] CmTicketContentsListQueryDto req)
        {
            return await _cmTicketContentsService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmTicketContentsDetailDto>> Query([FromQuery] CmTicketContentsPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmTicketContentsDetailDto>> QueryPost(CmTicketContentsPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmTicketContentsDetailDto>> PageQuery([FromQuery] CmTicketContentsPageQueryDto req)
        {
            return await _cmTicketContentsService.PageQueryView(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("QueryCard")]
        public async Task<PageQueryResult<CmTicketContentsV2Dto>> QueryCard([FromQuery] CmTicketContentsPageQueryDto req)
        {
            return await PageQueryCard(req);
        }
        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmTicketContentsV2Dto>> PageQueryCard([FromQuery] CmTicketContentsPageQueryDto req)
        {
            return await _cmTicketContentsService.PageQueryCardView(req);
        }
        [HttpGet("IsReadMsg")]
        public async Task<bool> IsReadMsg([FromQuery] CmTicketContentsPageQueryDto req)
        {
            return await _cmTicketContentsService.IsReadMsg(req);
        }
        [HttpPost("UpdateIsRead/{id}")]
        public async Task UpdateIsRead(long Id)
        {
             await _cmTicketContentsService.UpdateIsRead(Id);
        }
    }
}