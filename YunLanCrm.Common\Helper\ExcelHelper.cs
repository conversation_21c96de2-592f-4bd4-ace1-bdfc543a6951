﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Reflection;
using Newtonsoft.Json.Linq;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.HSSF.UserModel;
using NetTaste;
using Microsoft.AspNetCore.StaticFiles;
using YunLanCrm.Dto.CmTickets;
using NPOI.Util.Collections;
using System.Text.RegularExpressions;
using NPOI.POIFS.Crypt.Dsig;
using Ocelot.Responses;
using System.Data;
using YunLanCrm.Model.Api;
using SqlSugar;
using Microsoft.AspNetCore.Components.Web;
using Nacos.V2.Utils;
using YunLanCrm.Model.Dto.CmOpenItemDetail;
using System.ComponentModel.DataAnnotations.Schema;

namespace YunLanCrm.Common.Helper
{
    public class ExcelHelper
    {
        private static List<Dictionary<string, object>> _displayConvertPair;

        static ExcelHelper()
        {
            try
            {
                _displayConvertPair = new List<Dictionary<string, object>>();
                string setting = File.ReadAllText(AppDomain.CurrentDomain.BaseDirectory + "\\export_setting.json");
                _displayConvertPair = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(setting);
            }
            catch (Exception ex)
            {
                SystemLogRecord.createLog("ExportSetting:" + ex.Message, "ExportExcel.txt", "TxtLog", true, true);
            }
        }

        private static void AddCol(IRow row, int index, JToken val, string drValue, XSSFWorkbook workbook, bool isHeader, string propName)
        {
            var newCell = row.CreateCell(index);

            if (isHeader)
            {
                var headFont = workbook.CreateFont();
                headFont.IsBold = true;
                // CreateCellStyle：创建单元格样式
                var headStyle = workbook.CreateCellStyle();
                headStyle.Alignment = HorizontalAlignment.Center;
                headStyle.SetFont(headFont);
                newCell.SetCellValue(drValue);
                newCell.CellStyle = headStyle; //格式化显示
                return;
            }
            //内容
            var textStyle = workbook.CreateCellStyle();
            textStyle.WrapText = true; // 设置自动换行
            textStyle.Alignment = HorizontalAlignment.Center;
            textStyle.VerticalAlignment = VerticalAlignment.Center;


            // 左对齐内容
            var leftTextStyle = workbook.CreateCellStyle();
            leftTextStyle.WrapText = true; // 设置自动换行
            leftTextStyle.Alignment = HorizontalAlignment.Left; // 设置水平左对齐
            leftTextStyle.VerticalAlignment = VerticalAlignment.Top; // 设置置顶

            var format = workbook.CreateDataFormat();
            //日期格式样式
            var dateStyle = workbook.CreateCellStyle();
            dateStyle.DataFormat = format.GetFormat("yyyy-MM-dd");
            dateStyle.Alignment = HorizontalAlignment.Center;
            dateStyle.VerticalAlignment = VerticalAlignment.Center;
            //单元格格式 数值
            var cStyleNumber = workbook.CreateCellStyle();
            cStyleNumber.DataFormat = format.GetFormat("0.00_ ");
            cStyleNumber.Alignment = HorizontalAlignment.Center;
            cStyleNumber.VerticalAlignment = VerticalAlignment.Center;


            var cStyleInt = workbook.CreateCellStyle();
            cStyleInt.Alignment = HorizontalAlignment.Center;
            cStyleInt.VerticalAlignment = VerticalAlignment.Center;

            var objType = val.Type;
            var name = objType.ToString();
            if (propName == "Comment"|| propName == "Description")
            {
                newCell.SetCellValue(drValue);
                newCell.CellStyle = leftTextStyle;
            }
            else
            {
                if (propName == "Ticket_Number"|| propName == "TicketNumber")
                {
                    if (!string.IsNullOrWhiteSpace(drValue))
                    {
                        name = "Int64";
                    }
                }
                if (propName == "Status" || propName == "Category_Status_Value" || propName == "Project_Status_Value" || propName == "Group_Status")
                {
                    name = "String";
                }
                switch (name)
                {
                    case "String":
                        newCell.SetCellValue(drValue);
                        newCell.CellStyle = textStyle;
                        break;

                    case "Date":
                        if (!string.IsNullOrEmpty(drValue))
                        {
                            if (drValue.IndexOf("1969") > -1)
                            {
                                newCell.SetCellValue("");
                            }
                            else
                            {
                                newCell.SetCellValue(drValue.ParseToDateTime().ToString("MM/dd/yyyy"));
                                newCell.CellStyle = dateStyle; //格式化显示
                            }                                                      
                        }
                        break;
                    case "Boolean":
                        newCell.SetCellValue(drValue);
                        break;
                    case "Int16":
                        newCell.SetCellValue(drValue.ParseToInt());
                        newCell.CellStyle = cStyleInt; //格式化显示
                        break;
                    case "Int32":
                    case "Integer":
                        newCell.SetCellValue(drValue.ParseToInt());
                        newCell.CellStyle = cStyleInt; //格式化显示
                        break;
                    case "Int64":
                        newCell.SetCellValue(drValue.ParseToDouble());
                        newCell.CellStyle = cStyleInt; //格式化显示
                        break;
                    case "Double":
                        newCell.SetCellValue(drValue.ParseToDouble());
                        newCell.CellStyle = cStyleNumber; //格式化显示
                        break;

                    case "Single":
                        newCell.SetCellValue(drValue.ParseToDouble());
                        newCell.CellStyle = cStyleNumber; //格式化显示
                        break;

                    case "Decimal":
                        newCell.SetCellValue(drValue.ParseToDouble());
                        newCell.CellStyle = cStyleNumber; //格式化显示
                        break;

                    case "Float":
                        newCell.SetCellValue(drValue.ParseToDouble());
                        newCell.CellStyle = cStyleNumber; //格式化显示
                        break;

                    case "DBNull":
                        newCell.SetCellValue(string.Empty);
                        break;

                    default:
                        newCell.SetCellValue(string.Empty);
                        break;
                }
            }
        }

        public static async Task<IActionResult> Export<T>(object target, string targetName, string method, ControllerBase baseController, bool isAllCheck, params object[] methodParams)
        {
            //SystemLogRecord.createLog("Export11111:" + targetName, "ExportExcel.txt", "TxtLog", true, true);

            byte[] bs;

            try
            {
                //SystemLogRecord.createLog("Export:" + targetName, "ExportExcel.txt", "TxtLog", true, true);

                var task = target.GetType().InvokeMember(method, BindingFlags.InvokeMethod, null, target, methodParams) as Task;
                if (task != null) await task;
                var returnObj = task?.GetType().GetProperty("Result")?.GetValue(task);
                string json = JsonConvert.SerializeObject(returnObj);
                JArray jsonVals = null;
                object testVals = JsonConvert.DeserializeObject(json);

                if (testVals is JObject)
                {
                    JObject root = (JObject)testVals;
                    if (root.ContainsKey("Data"))
                    {
                        jsonVals = (JArray)root["Data"];
                    }
                }
                else if (testVals is JArray)
                {
                    jsonVals = (JArray)testVals;
                }
                string flag = targetName + "." + method;
                Dictionary<string, object> targetJson = null;

                // 创建工作簿
                var workbook = new XSSFWorkbook();
                // 创建表
                var sheet = workbook.CreateSheet("Sheet1");
                // CreateFont：创建字体样式
                var headFont = workbook.CreateFont();
                headFont.IsBold = true;


                // CreateRow：操作指定表的指定行。
                var rowIndex = 0;
                var row = sheet.CreateRow(rowIndex);

                foreach (Dictionary<string, object> tJson in _displayConvertPair)
                {
                    if (tJson["name"].ToString().Equals(flag))
                    {
                        targetJson = tJson;
                        break;
                    }
                }

                var index = 0;

                if (targetJson == null)
                {
                    JObject sourceRoot = (JObject)((JObject)testVals)["Data"][0];

                    dynamic root = new JObject();
                    root.name = flag;
                    root.cols = new JArray() as dynamic;

                    foreach (var key in sourceRoot.Properties().Select(p => p.Name).ToList())
                    {
                        dynamic col = new JObject();
                        col.display = key;
                        col.name = key;

                        root.cols.Add(col);
                    }

                    string rootJsonStr = ((JObject)root).ToJsonString();

                    targetJson = JsonConvert.DeserializeObject<Dictionary<string, object>>(rootJsonStr);

                }

                foreach (JObject jobject in targetJson["cols"] as JArray)
                {
                    if (jobject.ContainsKey("display"))
                    {

                        // 获取属性信息
                        PropertyInfo propertyInfo = typeof(CmOpenItemExportDto).GetProperty(jobject["name"].ToString());
                        if (propertyInfo != null)
                        {
                            // 获取 Column 属性
                            var columnAttribute = propertyInfo.GetCustomAttribute<ColumnAttribute>();
                            if (columnAttribute != null)
                            {
                                // 修改 display 字段
                                jobject["display"] = columnAttribute.Name;
                            }
                        }
                        // 检查当前的字段名称
                        //if (jobject["name"].ToString() == "SortId")
                        //{
                        //    // 修改字段名称
                        //    jobject["display"] = "Item#"; // 修改显示名称（如果需要）
                        //}
                        //// 检查当前的字段名称
                        //if (jobject["name"].ToString() == "TicketNumber")
                        //{
                        //    // 修改字段名称
                        //    jobject["display"] = "Ticket#"; // 修改显示名称（如果需要）
                        //}
                        AddCol(row, index, jobject, jobject["display"].ToString(), workbook, true, string.Empty);
                        index++;
                    }
                }

                foreach (JObject vals in jsonVals)
                {
                    rowIndex++;
                    row = sheet.CreateRow(rowIndex);

                    var children = vals["Children"];
                    if (children != null && isAllCheck)
                    {
                        var jarChildren = children.ToObject<JArray>();

                        if (jarChildren != null)
                        {
                            ChildrenRow(jarChildren, sheet, row, ref rowIndex, targetJson, workbook);
                        }
                    }

                    for (int vindex = 0; vindex < (targetJson["cols"] as JArray).Count; vindex++)
                    {
                        JObject jobject = (targetJson["cols"] as JArray)[vindex] as JObject;
                        string propName = jobject["name"].ToString();

                        if (jobject.ContainsKey("name") && vals.ContainsKey(propName))
                        {
                            string val = vals[propName].ToString();

                            if (jobject.ContainsKey("convert"))
                            {
                                JObject convert = jobject["convert"] as JObject;
                                if (convert.ContainsKey(val))
                                {
                                    val = convert[val].ToString();
                                }
                            }
                            AddCol(row, vindex, vals[propName], val, workbook, false, propName);
                        }
                    }
                }

                for (int columnNum = 0; columnNum <= index; columnNum++)
                {
                    int columnWidth = sheet.GetColumnWidth(columnNum) / 256;
                    for (int rowNum = 1; rowNum <= sheet.LastRowNum; rowNum++)
                    {
                        IRow currentRow = sheet.GetRow(rowNum);

                        if (currentRow.GetCell(columnNum) != null)
                        {
                            ICell currentCell = currentRow.GetCell(columnNum);

                            int length = Encoding.Default.GetBytes(currentCell.ToString()).Length;
                            if (columnWidth < length)
                            {
                                columnWidth = length;
                            }
                        }
                    }
                    //限制列宽不能超出100，超出部分换行
                    if (columnWidth > 100)
                    {
                        columnWidth = 100;
                    }
                    sheet.SetColumnWidth(columnNum, columnWidth * 256);
                }

                //end


                using (MemoryStream ms = new MemoryStream())
                {
                    workbook.Write(ms);
                    //bs = ms.GetBuffer(); 打开excel会提示格式报错
                    bs = ms.ToArray();
                }
            }
            catch (Exception ex)
            {
                SystemLogRecord.createLog(ex.Message, "ExportExcel.txt", "TxtLog", true, true);
                throw;
            }

            string tick = DateTime.Now.Ticks.ToString();

            return baseController.File(bs, "application/octet-stream", tick + ".xls");

        }

        private static void ChildrenRow(JArray children, ISheet sheet, IRow row, ref int rowIndex, Dictionary<string, object> targetJson, XSSFWorkbook workbook)
        {

            foreach (JObject item in children)
            {
                var childrenNew = item["Children"];
                if (childrenNew != null)
                {
                    var jarChildren = childrenNew.ToObject<JArray>();

                    if (jarChildren != null)
                    {
                        ChildrenRow(jarChildren, sheet, row, ref rowIndex, targetJson, workbook);
                    }
                }
                rowIndex++;
                row = sheet.CreateRow(rowIndex);
                for (int vindex = 0; vindex < (targetJson["cols"] as JArray).Count; vindex++)
                {
                    JObject jobject = (targetJson["cols"] as JArray)[vindex] as JObject;
                    string propName = jobject["name"].ToString();

                    if (jobject.ContainsKey("name") && item.ContainsKey(propName))
                    {
                        string val = item[propName].ToString();

                        if (jobject.ContainsKey("convert"))
                        {
                            JObject convert = jobject["convert"] as JObject;
                            if (convert.ContainsKey(val))
                            {
                                val = convert[val].ToString();
                            }
                        }
                        AddCol(row, vindex, item[propName], val, workbook, false, propName);
                    }
                }
            }
        }

    }


}
