﻿<template>
	<el-main style="padding: 0 20px">
		<el-descriptions :column="1" border size="small">
			<el-descriptions-item label="id">{{ info.id }}</el-descriptions-item>
			<el-descriptions-item label="isDeleted">{{ info.isDeleted }}</el-descriptions-item>
			<el-descriptions-item label="createdById">{{ info.createdById }}</el-descriptions-item>
			<el-descriptions-item label="createdAt">{{ info.createdAt }}</el-descriptions-item>
			<el-descriptions-item label="modifiedById">{{ info.modifiedById }}</el-descriptions-item>
			<el-descriptions-item label="modifiedAt">{{ info.modifiedAt }}</el-descriptions-item>
			<el-descriptions-item label="userDelegateId">{{ info.userDelegateId }}</el-descriptions-item>
			<el-descriptions-item label="userTrusteeId">{{ info.userTrusteeId }}</el-descriptions-item>
			<el-descriptions-item label="dateStart">{{ info.dateStart }}</el-descriptions-item>
			<el-descriptions-item label="dateEnd">{{ info.dateEnd }}</el-descriptions-item>
			<el-descriptions-item label="isApprove">{{ info.isApprove }}</el-descriptions-item>
			<el-descriptions-item label="isEmail">{{ info.isEmail }}</el-descriptions-item>
			<el-descriptions-item label="approverType">{{ info.approverType }}</el-descriptions-item>
		</el-descriptions>
	</el-main>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent } from 'vue';
import { formatStrDate } from '/@/utils/formatTime';

export default defineComponent({
	name: 'apiDetail',
	props: {
		info: Object,
	},
	setup(props) {
		const state = reactive({
			isShowDialog: false,
			obj: {},
		});
		// 页面加载时
		onMounted(() => {
			// console.log('props', props.info);
		});
		return {
			...toRefs(state),
		};
	},
});
</script>
