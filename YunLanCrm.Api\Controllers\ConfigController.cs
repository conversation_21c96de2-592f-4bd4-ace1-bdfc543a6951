﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.Config;
using YunLanCrm.IRepositories;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class ConfigController : ControllerBase
    {
        private readonly ILogger<ConfigInfo> _logger;

        private readonly IConfigService _configService;

        private readonly ICaching _cache;

        private readonly IConfigRepository configRepository;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="configService"></param>
        /// <param name="logger"></param>
        /// <param name="cache"></param>
        public ConfigController(IConfigService configService, ILogger<ConfigInfo> logger, ICaching cache, IConfigRepository configRepository)
        {
            this._configService = configService;
            this._logger = logger;
            _cache = cache;
            this.configRepository = configRepository;
        }


        [HttpPost("SaveConfig")]
        public async Task<bool> SaveConfig(AppConfigInfo obj)
        {
            if (obj == null)
            {
                throw new Exception("信息不能为空");
            }

            var listAll = await _configService.Query(a => a.IsDeleted == false);

            var list = Appsettings.ToDbList(obj, listAll);

            foreach (var item in list)
            {
                if (string.IsNullOrWhiteSpace(item.TypeId))
                {
                    throw new Exception("TypeId不能为空");
                }
            }

            var pros = obj.GetPropertieNames();

            await _configService.Delete(a => pros.Contains(a.TypeId));

            int count = await _configService.Add(list);

            if (count > 0)
            {
                //清除缓存 
                _cache.Remove("ConfigService:GetAppConfig");
            }
            //修改静态变量
            listAll = await _configService.Query(a => a.IsDeleted == false);
            var data = ConfigInfo.Parse<AppConfigInfo>(listAll);
             YunLanCrm.Services.ConfigManager.Initialize(data);
            return count > 0;
        }

        /// <summary>
        /// 获取系统的全局配置
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetConfig")]
        public async Task<AppConfigInfo> GetConfig(string type)
        {
            //return await _configService.GetAppConfig();

            var config = await _configService.GetAppConfig();

            config.Email.Password = Consts.DefaultEmptyPassword;

            return config;
        }
        /// <summary>
        /// 获取系统的Site配置，无限制。
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAppConfig")]
        [AllowAnonymous]
        public async Task<AppInfo> GetAppConfig(string type)
        {
            var configInfo = await _configService.GetAppConfig();

            return configInfo.Info;
        }
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        [NonAction]
        public async Task<long> Add(ConfigAddDto req)
        {
            return await _configService.AddIdentity(req.Adapt<ConfigInfo>());
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        [NonAction]
        public async Task<bool> Update(ConfigEditDto req)
        {
            return await _configService.Update(req.Adapt<ConfigInfo>());
        }

        [HttpPost("UpdateByName")]
        [NonAction]
        public async Task<bool> UpdateByName(ConfigEditDto req)
        {
            var configInfo = configRepository.Db.Queryable<ConfigInfo>().Where(a => a.IsDeleted == false && a.Name == req.Name).First();

            if (configInfo != null)
            {
                configInfo.Value = req.Value;
            }

            return await _configService.Update(configInfo.Adapt<ConfigInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        [NonAction]
        public async Task<bool> Delete(long id)
        {
            int result = await _configService.Delete(a => a.Id == id);

            return result > 0;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        [NonAction]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _configService.DeleteByIds(items);

            return result;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        [NonAction]
        public async Task<ConfigDto> Get(long id)
        {
            return await _configService.QueryInfo<ConfigDto>(a => a.Id == id);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        [NonAction]
        public async Task<ConfigDetailDto> Detail(long id)
        {
            var obj = await _configService.QueryInfo(a => a.Id == id);

            if (obj != null)
            {
                return _configService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        [NonAction]
        public async Task<List<ConfigListDto>> List([FromQuery] ConfigListQueryDto req)
        {
            var list = new List<ConfigListDto>();
            var where = PredicateBuilder.New<ConfigInfo>(true);
            var orderBy = new OrderBy<ConfigInfo>(req.order, req.sort);
            var data = await _configService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _configService.Join(item);
                list.Add(detail.Adapt<ConfigListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        [NonAction]
        public async Task<PageQueryResult<ConfigListDto>> Query([FromQuery] ConfigPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        [NonAction]
        public async Task<PageQueryResult<ConfigListDto>> QueryPost(ConfigPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<ConfigListDto>> PageQuery([FromQuery] ConfigPageQueryDto req)
        {
            var list = new List<ConfigListDto>();
            var where = PredicateBuilder.New<ConfigInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            var totalCount = await _configService.CountAsync(where);
            var orderBy = new OrderBy<ConfigInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _configService.QueryList(where, orderBy, paging);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _configService.Join(item);
                list.Add(detail.Adapt<ConfigListDto>());
            }
            #endregion

            return new PageQueryResult<ConfigListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

    }
}