﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.Dict;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class DictController : ControllerBase
    {
        private readonly ILogger<DictInfo> _logger;
        private readonly IDictService _dictService;
        private readonly IDictItemService _dictItemService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dictService"></param>
        /// <param name="dictItemService"></param>
        /// <param name="logger"></param>
        public DictController(IDictService dictService, IDictItemService dictItemService, ILogger<DictInfo> logger)
        {
            _dictService = dictService;
            _logger = logger;
            _dictItemService = dictItemService;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(DictAddDto req)
        {
            return await _dictService.AddIdentity(req.Adapt<DictInfo>());
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(DictEditDto req)
        {
            return await _dictService.Update(req.Adapt<DictInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="dictId">dictId</param>
        /// <returns></returns>
        [HttpPost("Delete/{dictId}")]
        public async Task<bool> Delete(long dictId)
        {
            int result = await _dictService.Delete(a => a.DictId == dictId);
            if (result > 0)
            {
                await _dictItemService.Delete(a => a.DictId == dictId);
            }

            return result > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        [NonAction]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _dictService.DeleteByIds(items);

            return result;
        }

        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="dictId"></param>
        /// <returns></returns>
        [HttpGet("Get/{dictId}")]
        [NonAction]
        public async Task<DictDto> Get(long dictId)
        {
            return await _dictService.QueryInfo<DictDto>(a => a.DictId == dictId);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="dictId"></param>
        /// <returns></returns>
        [HttpGet("Detail/{dictId}")]
        [NonAction]
        public async Task<DictDetailDto> Detail(long dictId)
        {
            var obj = await _dictService.QueryInfo(a => a.DictId == dictId);

            if (obj != null)
            {
                return _dictService.Join(obj);
            }



            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<DictListDto>> List([FromQuery] DictListQueryDto req)
        {
            var list = new List<DictListDto>();
            var where = PredicateBuilder.New<DictInfo>(true);
            var orderBy = new OrderBy<DictInfo>(req.order, req.sort);
            var data = await _dictService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _dictService.Join(item);
                list.Add(detail.Adapt<DictListDto>());
            }
            #endregion

            

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        [NonAction]
        public async Task<PageQueryResult<DictListDto>> Query([FromQuery] DictPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<DictListDto>> QueryPost(DictPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<DictListDto>> PageQuery([FromQuery] DictPageQueryDto req)
        {
            var list = new List<DictListDto>();
            var where = PredicateBuilder.New<DictInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            var totalCount = await _dictService.CountAsync(where);
            var orderBy = new OrderBy<DictInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _dictService.QueryList(where, orderBy, paging);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _dictService.Join(item);
                list.Add(detail.Adapt<DictListDto>());
            }
            #endregion

            return new PageQueryResult<DictListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

    }
}