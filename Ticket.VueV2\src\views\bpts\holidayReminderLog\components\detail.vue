﻿<template>
    <el-main style="padding:0 20px;">
        <el-descriptions :column="1" border size="small">
           
 <el-descriptions-item label="id">{{ info.id }}</el-descriptions-item>
 <el-descriptions-item label="isDeleted">{{ info.isDeleted }}</el-descriptions-item>
 <el-descriptions-item label="createdById">{{ info.createdById }}</el-descriptions-item>
 <el-descriptions-item label="createdAt">{{ info.createdAt }}</el-descriptions-item>
 <el-descriptions-item label="modifiedById">{{ info.modifiedById }}</el-descriptions-item>
 <el-descriptions-item label="modifiedAt">{{ info.modifiedAt }}</el-descriptions-item>
 <el-descriptions-item label="cmTicketId">{{ info.cmTicketId }}</el-descriptions-item>
 <el-descriptions-item label="holiday">{{ info.holiday }}</el-descriptions-item>
        </el-descriptions>
    </el-main>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent } from 'vue';
import { formatStrDate } from '/@/utils/formatTime';

export default defineComponent({
    name: 'apiDetail',
    props: {
        info: Object
    },
    setup(props) {
        const state = reactive({
            isShowDialog: false,
            obj: {},
        });
        // 页面加载时
        onMounted(() => {
           window.console.log("props", props.info)
        });
        return {
            ...toRefs(state),
        };
    },
});
</script>


                        
        
        