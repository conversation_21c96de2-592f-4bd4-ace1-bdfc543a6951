﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hczb.Tools.HolidayOperation
{
    public class UnitedStatesProvider : IPublicHolidayProvider
    {
        private readonly ICatholicProvider _catholicProvider;

        /// <summary>
        /// UnitedStatesProvider
        /// </summary>
        public UnitedStatesProvider(ICatholicProvider catholicProvider)
        {
            this._catholicProvider = catholicProvider;
        }

        public IEnumerable<PublicHoliday> Get(int year)
        {
            var countryCode = CountryCode.US;

            var thirdMondayInJanuar = DateSystemHelper.FindDay(year, Month.January, DayOfWeek.Monday, Occurrence.Third);              //1月的第三个星期一：马丁·路德·金纪念日  
            var thirdMondayInFebruary = DateSystemHelper.FindDay(year, Month.February, DayOfWeek.Monday, Occurrence.Third);           //2月的第三个星期一：总统节
            var lastMondayInMay = DateSystemHelper.FindLastDay(year, Month.May, DayOfWeek.Monday);                                    //5月的最后一个星期一：阵亡将士纪念日
            var firstMondayInSeptember = DateSystemHelper.FindDay(year, Month.September, DayOfWeek.Monday, Occurrence.First);         //9月的第一个星期一：劳动节
            var secondMondayInOctober = DateSystemHelper.FindDay(year, Month.October, DayOfWeek.Monday, Occurrence.Second);           //10月的第二个星期一：哥伦布日
            var fourthThursdayInNovember = DateSystemHelper.FindDay(year, Month.November, DayOfWeek.Thursday, Occurrence.Fourth);     //11月的第四个星期四：感恩节

            var items = new List<PublicHoliday>();

            #region New Years Day with fallback  1月1日：元旦

            var newYearsDay = new DateTime(year, 1, 1).Shift(saturday => saturday.AddDays(-1), sunday => sunday.AddDays(1));    //1月1日：元旦
            items.Add(new PublicHoliday(newYearsDay, "New Year's Day", "New Year's Day", countryCode));

            #endregion

            items.Add(new PublicHoliday(thirdMondayInJanuar, "Martin Luther King, Jr. Day", "Martin Luther King, Jr. Day", countryCode));
            items.Add(new PublicHoliday(thirdMondayInFebruary, "Presidents Day", "Washington's Birthday", countryCode));
            items.Add(new PublicHoliday(lastMondayInMay, "Memorial Day", "Memorial Day", countryCode));

            items.Add(this._catholicProvider.GoodFriday("Good Friday", year, countryCode).SetCounties("US-CT", "US-DE", "US-HI", "US-IN", "US-KY", "US-LA", "US-NC", "US-ND", "US-NJ", "US-TN"));
            items.Add(this._catholicProvider.GoodFriday("Good Friday", year, countryCode).SetType(PublicHolidayType.Optional).SetCounties("US-TX"));

            #region Juneteenth 六月节全国独立日

            if (year >= 2021)
            {
                var juneteenth = new DateTime(year, 6, 19).Shift(saturday => saturday.AddDays(-1), sunday => sunday.AddDays(1));
                items.Add(new PublicHoliday(juneteenth, "Juneteenth", "Juneteenth", countryCode, 2021));
            }

            #endregion

            #region Independence Day with fallback 7月4日：美国国庆

            var independenceDay = new DateTime(year, 7, 4).Shift(saturday => saturday.AddDays(-1), sunday => sunday.AddDays(1));
            items.Add(new PublicHoliday(independenceDay, "Independence Day", "Independence Day", countryCode));

            #endregion

            items.Add(new PublicHoliday(firstMondayInSeptember, "Labor Day", "Labour Day", countryCode));
            items.Add(new PublicHoliday(secondMondayInOctober, "Columbus Day", "Columbus Day", countryCode, null, new string[] { "US-AL", "US-AZ", "US-CO", "US-CT", "US-DC", "US-GA", "US-ID", "US-IL", "US-IN", "US-IA", "US-KS", "US-KY", "US-LA", "US-ME", "US-MD", "US-MA", "US-MS", "US-MO", "US-MT", "US-NE", "US-NH", "US-NJ", "US-NM", "US-NY", "US-NC", "US-OH", "US-OK", "US-PA", "US-RI", "US-SC", "US-TN", "US-UT", "US-VA", "US-WV" }));

            #region Veterans Day with fallback 11月11日：退伍军人节

            var veteransDay = new DateTime(year, 11, 11).Shift(saturday => saturday.AddDays(-1), sunday => sunday.AddDays(1));
            items.Add(new PublicHoliday(veteransDay, "Veterans Day", "Veterans Day", countryCode));

            #endregion

            items.Add(new PublicHoliday(fourthThursdayInNovember, "Thanksgiving Day", "Thanksgiving Day", countryCode, 1863));

            #region Christmas Day with fallback 12月25日：圣诞节

            var christmasDay = new DateTime(year, 12, 25).Shift(saturday => saturday.AddDays(-1), sunday => sunday.AddDays(1));
            items.Add(new PublicHoliday(christmasDay, "Christmas Day", "Christmas Day", countryCode));

            #endregion

            #region Inauguration Day (every 4 years)  就职日

            if ((year - 1) % 4 == 0)
            {
                if (year >= 1937)
                {
                    items.Add(new PublicHoliday(year, 1, 20, "Inauguration Day", "Inauguration Day", countryCode, null, new string[] { "US-DC", "US-LA", "US-MD", "US-VA" }));
                }
                else
                {
                    items.Add(new PublicHoliday(year, 3, 4, "Inauguration Day", "Inauguration Day", countryCode, null, new string[] { "US-DC", "US-LA", "US-MD", "US-VA" }));
                }
            }

            #endregion

            return items.OrderBy(o => o.Date);
        }
    }
}
