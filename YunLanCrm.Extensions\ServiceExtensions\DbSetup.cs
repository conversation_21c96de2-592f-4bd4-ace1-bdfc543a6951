﻿using YunLanCrm.Common.Seed;
using Microsoft.Extensions.DependencyInjection;
using System;
using YunLanCrm.Model.Models;
using YunLanCrm.Model;
using YunLanCrm.Services;
using Microsoft.Extensions.Hosting;
using System.Threading.Tasks;
using System.Threading;

namespace YunLanCrm.Extensions
{
    /// <summary>
    /// Db 启动服务
    /// </summary>
    public static class DbSetup
    {
        public static void AddDbSetup(this IServiceCollection services)
        {
            if (services == null) throw new ArgumentNullException(nameof(services));

            //services.AddScoped<DBSeed>();
            services.AddScoped<MyContext>();
        }
    }

    public static class DbConfig
    {
        /// <summary>
        /// 加载配置信息
        /// </summary>
        /// <param name="services"></param>
        public static void AddDbConfig(this IServiceCollection services)
        {
            // 创建一个服务提供者来获取 MyContext 实例
            //var serviceProvider = services.BuildServiceProvider();
            //using (var scope = serviceProvider.CreateScope())
            //{
            //    var context = scope.ServiceProvider.GetRequiredService<MyContext>();
            //    if (context != null)
            //    {
            //        var list = context.Db.Queryable<ConfigInfo>().ToList();
            //        var data = ConfigInfo.Parse<AppConfigInfo>(list);
            //        ConfigManager.Initialize(data);
            //    }
            //}

            // 如果你需要获取配置信息，可以通过依赖注入来实现
            services.AddTransient<IHostedService, ConfigLoaderService>();
        }

    }

    public class ConfigLoaderService : IHostedService
    {
        private readonly MyContext _context;

        public ConfigLoaderService(MyContext context)
        {
            _context = context;
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            var list = _context.Db.Queryable<ConfigInfo>().ToList();
            var data = ConfigInfo.Parse<AppConfigInfo>(list);
            ConfigManager.Initialize(data);
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
    }
}
