﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using SqlSugar;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.LogEvent;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class LogEventController : ControllerBase
    {
        private readonly ILogger<LogEventInfo> _logger;

        private readonly ILogEventService _logEventService;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="logEventService"></param>
        /// <param name="logger"></param>
        public LogEventController(ILogEventService logEventService, ILogger<LogEventInfo> logger)
        {
            this._logEventService = logEventService;
            this._logger = logger;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        [NonAction]
        public async Task<long> Add(LogEventAddDto req)
        {
            return await _logEventService.AddIdentity(req.Adapt<LogEventInfo>());
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        [NonAction]
        public async Task<bool> Update(LogEventEditDto req)
        {
            return await _logEventService.Update(req.Adapt<LogEventInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        [NonAction]
        public async Task<bool> Delete(long id)
        {
            int result = await _logEventService.Delete(a => a.Id == id);

            return result > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        [NonAction]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _logEventService.DeleteByIds(items);

            return result;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<LogEventDto> Get(long id)
        {
            return await _logEventService.QueryInfo<LogEventDto>(a => a.Id == id);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        [NonAction]
        public async Task<LogEventDetailDto> Detail(long id)
        {
            var obj = await _logEventService.QueryInfo(a => a.Id == id);

            if (obj != null)
            {
                return _logEventService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        [NonAction]
        public async Task<List<LogEventListDto>> List([FromQuery] LogEventListQueryDto req)
        {
            var list = new List<LogEventListDto>();
            var where = PredicateBuilder.New<LogEventInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.logType))
            {
                where.And(a => a.LogType == req.logType);
            }
            if (req.startTime.HasValue && req.endTime.HasValue)
            {
                where.And(a => a.TimeStamp >= req.startTime.Value && a.TimeStamp <= req.endTime.Value);
            }
            var orderBy = new OrderBy<LogEventInfo>(req.order, req.sort);
            var data = await _logEventService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _logEventService.Join(item);
                list.Add(detail.Adapt<LogEventListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        [NonAction]
        public async Task<PageQueryResult<LogEventListDto>> Query([FromQuery] LogEventPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<LogEventListDto>> QueryPost(LogEventPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<LogEventListDto>> PageQuery([FromQuery] LogEventPageQueryDto req)
        {
            var list = new List<LogEventListDto>();
            var where = PredicateBuilder.New<LogEventInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.logType))
            {
                where.And(a => a.LogType == req.logType);
            }
            if (req.startTime.HasValue && req.endTime.HasValue)
            {
                where.And(a => SqlFunc.ToDateShort(a.TimeStamp) >= req.startTime.Value && SqlFunc.ToDateShort(a.TimeStamp) <= req.endTime.Value);
            }
            var totalCount = await _logEventService.CountAsync(where);
            var orderBy = new OrderBy<LogEventInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _logEventService.QueryList(where, orderBy, paging);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _logEventService.Join(item);
                list.Add(detail.Adapt<LogEventListDto>());
            }
            #endregion

            return new PageQueryResult<LogEventListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

    }
}