﻿using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Models;
using Elsa.Persistence;
using Elsa.Services;
using Elsa.Services.Models;
using YunLanCrm.Common.Extensions;
using YunLanCrm.Common.HttpContextUser;
using YunLanCrm.Dto.WorkflowCrm;
using YunLanCrm.Dto.WorkflowExecutionLogRecords;
using YunLanCrm.IRepositories;
using YunLanCrm.Model;
using YunLanCrm.Model.Models;

namespace YunLanCrm.ElsaWorkflows.Activities
{
    /// <summary>
    /// 流程开始节点
    /// </summary>
    [Action(Category = "基础节点", DisplayName = "Start", Description = "开始节点", Outcomes = new[] { OutcomeNames.Done })]
    public class StartActivity : Activity
    {
        protected readonly IUser _user;
        protected readonly IWorkflowCrmRepository _workflowCrmRepository;
        protected readonly IWorkflowDefinitionStore _workflowDefinitionStore;

        public StartActivity(IUser user, IWorkflowCrmRepository workflowCrmRepository, IWorkflowDefinitionStore workflowDefinitionStore)
        {
            _workflowDefinitionStore = workflowDefinitionStore;
            _workflowCrmRepository = workflowCrmRepository;
            _user = user;

            UserActions = new List<string>() {
                Consts.WorkflowAction_Started,
                Consts.WorkflowAction_Approve,
                Consts.WorkflowAction_Rejected,
                Consts.WorkflowAction_AssignTo,
                Consts.WorkflowAction_Revoked,
                Consts.WorkflowAction_Returned,
                Consts.WorkflowAction_Cancelled,
                Consts.WorkflowAction_Escalate,
                Consts.WorkflowAction_Resubmit,
            };
        }

        public List<string> UserActions { get; set; }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            var workflowInstance = context.WorkflowInstance;

            //第一次提交
            if (context.Input is StartWorkflowDto)
            {
                //获取传递过来的信息
                var input = context.GetInput<StartWorkflowDto>();

                //当前节点的名称
                var nodeName = GetActivityBlueprintName(context.ActivityBlueprint);

                //业务和流程管理起来
                WorkflowBusinessInfo crm = new()
                {
                    ElsaInstancesId = context.WorkflowInstance.Id,//流程实例
                    DateType = input.DataType,//业务类型
                    DataId = input.DataId.ToString(),//对应的业务
                    Title = input.Title, //标题
                    PlannedStartTime = DateTime.Now.AddSeconds(-1),
                    PlannedEndTime = DateTime.Now.AddYears(1),
                    Priority = WorkflowPriority.Normal.ToEnumInt(),
                    Status = 0,
                    CreatedById = _user.UserId,
                    CreatedAt = DateTime.Now,
                    NextNode = nodeName
                };
                crm.Id = await _workflowCrmRepository.Db.Insertable(crm).ExecuteReturnBigIdentityAsync();

                //添加流转记录
                WorkflowTransferInfo log = new()
                {
                    TransferId = Guid.NewGuid(),
                    WorkflowInstanceId = context.WorkflowInstance.Id,
                    TransferDate = DateTime.Now,
                    TransferType = Consts.WorkflowAction_Started,
                    TransferTypeName = "开始",
                    ActivityId = context.ActivityBlueprint.Id,
                    CreatedById = _user.UserId,
                    Display = true,
                };
                await _workflowCrmRepository.Db.Insertable(log).ExecuteCommandAsync();

                //更新当前审批节点
                await _workflowCrmRepository.Db.Updateable<WorkflowBusinessInfo>()
                    .SetColumns(a => a.NextNode == "开始")
                    .Where(a => a.ElsaInstancesId == workflowInstance.Id)
                    .ExecuteCommandAsync();

                //保存表单信息
                context.SetVariable("FormData", input.Data);

                var inputDone = new WorkflowExecutionDto()
                {
                    ExecutionType = Consts.WorkflowAction_Started,
                    UserId = _user.UserId,
                };


                return Done(inputDone);
            }

            //其他操作
            if (context.Input is WorkflowExecutionDto)
            {
                var input = context.GetInput<WorkflowExecutionDto>();

                //保存表单信息
                context.SetVariable("FormData", input.Data);

                if (input.ExecutionType == Consts.WorkflowAction_Returned || input.ExecutionType == Consts.WorkflowAction_Revoked)
                {
                    var obj = _workflowCrmRepository.Db.Queryable<WorkflowBusinessInfo>().Where(a => a.ElsaInstancesId == workflowInstance.Id).First();

                    //如果是退回和撤回 ，就给发起人发一条任务
                    WorkflowTaskInfo task = new WorkflowTaskInfo
                    {
                        TaskId = Guid.NewGuid(),
                        TaskMode = Consts.TaskMode_WaitAny,
                        TaskType = Consts.TaskType_Resubmit,
                        ActivityId = context.ActivityId,
                        WorkflowInstanceId = workflowInstance.Id,
                        TransferId = input.NewTransferId,
                        TaskStatus = Consts.WorkflowTaskStatus_Pending,
                        Priority = WorkflowPriority.Normal.ToEnumInt(),
                        CreatedById = _user.UserId,
                        CreatedDate = DateTime.Now,
                        Msg = "",
                    };
                    await _workflowCrmRepository.Db.Insertable(task).ExecuteCommandAsync();

                    //给任务人先添加一条处理记录，状态为未处理
                    var list = new List<WorkflowTaskUserInfo>() {
                        new WorkflowTaskUserInfo(){
                            Id=Guid.NewGuid(),
                            WorkflowInstanceId=context.WorkflowInstance.Id,
                            TaskId=task.TaskId,
                            Status=Consts.WorkflowTaskStatus_Pending,
                            UserId=obj.CreatedById,
                        }
                    };

                    await _workflowCrmRepository.Db.Insertable(list).ExecuteCommandAsync();//添加到数据库

                    //更新当前
                    await _workflowCrmRepository.Db.Updateable<WorkflowBusinessInfo>()
                        .SetColumns(a => a.NextNode == "开始")
                        .Where(a => a.ElsaInstancesId == workflowInstance.Id)
                        .ExecuteCommandAsync();

                    //清空阻断的节点
                    context.WorkflowInstance.BlockingActivities.Clear();

                    var ss = context.WorkflowInstance.BlockingActivities;

                    //暂停在开始节点
                    return Suspend();
                }

            }

            return await base.OnExecuteAsync(context);
        }

        protected override bool OnCanExecute(ActivityExecutionContext context)
        {
            if (context.Input is StartWorkflowDto)
            {
                var input = context.GetInput<StartWorkflowDto>();
                return input != null;
            }

            if (context.Input is WorkflowExecutionDto)
            {
                var input = context.GetInput<WorkflowExecutionDto>();
                return UserActions.Contains(input.ExecutionType);
            }

            return base.OnCanExecute(context);
        }

        protected override async ValueTask<IActivityExecutionResult> OnResumeAsync(ActivityExecutionContext context)
        {
            var workflowInstance = context.WorkflowInstance;
            //其他操作
            if (context.Input is WorkflowExecutionDto)
            {
                var input = context.GetInput<WorkflowExecutionDto>();

                //保存表单信息
                context.SetVariable("FormData", input.Data);
                if (input.ExecutionType == Consts.WorkflowAction_Resubmit)
                {
                    //提交 或者 重新提交 需要把所有重新提交的 流转记录都隐藏，不用显示
                    var count = await _workflowCrmRepository.Db.Updateable<WorkflowTransferInfo>()
                           .SetColumns(a => a.Display == false)
                           .Where(a => a.WorkflowInstanceId == workflowInstance.Id && a.TransferType == Consts.WorkflowAction_Resubmit)
                           .ExecuteCommandAsync();

                    //添加流转记录
                    WorkflowTransferInfo log = new()
                    {
                        TransferId = Guid.NewGuid(),
                        WorkflowInstanceId = context.WorkflowInstance.Id,
                        TransferDate = DateTime.Now,
                        TransferType = Consts.WorkflowAction_Started,
                        TransferTypeName = "重新提交",
                        ActivityId = context.ActivityBlueprint.Id,
                        CreatedById = _user.UserId,
                        Display = true,
                    };
                    await _workflowCrmRepository.Db.Insertable(log).ExecuteCommandAsync();

                    //更新当前审批节点
                    await _workflowCrmRepository.Db.Updateable<WorkflowBusinessInfo>()
                        .SetColumns(a => a.NextNode == "开始")
                        .Where(a => a.ElsaInstancesId == workflowInstance.Id)
                        .ExecuteCommandAsync();

                    //把重新提交的任务设置成已完成
                    await _workflowCrmRepository.Db.Updateable<WorkflowTaskInfo>()
                                                   .SetColumns(a => a.TaskStatus, Consts.WorkflowTaskStatus_Completed)
                                                   .SetColumns(a => a.UpdatedById, input.UserId)
                                                   .SetColumns(a => a.UpdatedDate, DateTime.Now)
                                                   .Where(a => a.TaskId == input.TaskId)
                                                   .ExecuteCommandAsync();

                    //更新任务人的操作结果
                    var taskUser = _workflowCrmRepository.Db.Queryable<WorkflowTaskUserInfo>().Where(a => a.TaskId == input.TaskId.Value && a.UserId == input.UserId).First();
                    if (taskUser != null)
                    {
                        taskUser.Status = Consts.WorkflowTaskStatus_Completed;
                        taskUser.OperationType = input.ExecutionType;
                        taskUser.OperationDate = DateTime.Now;
                        taskUser.OperationResult = input.ExecutionType;
                        taskUser.OperationMsg = input.Msg;
                        await _workflowCrmRepository.Db.Updateable(taskUser).ExecuteCommandAsync();//添加到数据库
                    }
                    var inputDone = new WorkflowExecutionDto()
                    {
                        ExecutionType = Consts.WorkflowAction_Started,
                        UserId = _user.UserId,
                    };
                    //开始节点完成
                    return Done(inputDone);
                }
            }
            return await base.OnResumeAsync(context);
        }

        /// <summary>
        /// 获取当前活动对应的名称
        /// </summary>
        /// <param name="activityBlueprint"></param>
        /// <returns></returns>
        private string GetActivityBlueprintName(IActivityBlueprint activityBlueprint)
        {
            if (activityBlueprint.Type == nameof(StartActivity))
                return "开始";
            if (string.IsNullOrWhiteSpace(activityBlueprint.DisplayName))
                return activityBlueprint.Name;

            return activityBlueprint.DisplayName;
        }
    }
}