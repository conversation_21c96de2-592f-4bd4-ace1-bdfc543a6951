﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmImportTable;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmImportTableController : ControllerBase
    {
        private readonly ILogger<CmImportTableInfo> _logger;
        private readonly ICmImportTableService _cmImportTableService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmImportTableService"></param>
        /// <param name="logger"></param>
        public CmImportTableController(ICmImportTableService cmImportTableService, ILogger<CmImportTableInfo> logger)
        {
            _logger = logger;
            _cmImportTableService = cmImportTableService;
            
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CmImportTableAddOrUpdateDto req)
        {
            return await _cmImportTableService.AddCmImportTable(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmImportTableAddOrUpdateDto req)
        {
            return await _cmImportTableService.UpdateCmImportTable(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _cmImportTableService.Delete(a => a.Id == id);

            return result > 0;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _cmImportTableService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmImportTableDto> Get(long id)
        {
            return await _cmImportTableService.QueryInfo<CmImportTableDto>(a => a.Id == id);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmImportTableDto>> GetList([FromQuery] CmImportTableListQueryDto req)
        {
            return await _cmImportTableService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<CmImportTableDetailDto> Detail(long id)
        {
            return await _cmImportTableService.Detail(id);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmImportTableDetailDto>> DetailList([FromQuery] CmImportTableListQueryDto req)
        {
            return await _cmImportTableService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmImportTableDetailDto>> Query([FromQuery] CmImportTablePageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmImportTableDetailDto>> QueryPost(CmImportTablePageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmImportTableDetailDto>> PageQuery([FromQuery] CmImportTablePageQueryDto req)
        {
            return await _cmImportTableService.PageQueryView(req);
        }

    }
}