﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.Api;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using Furion;
using YunLanCrm.Common.Helper;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class ApiController : ControllerBase
    {
        private readonly ILogger<ApiInfo> _logger;

        private readonly IApiService _apiService;
        private readonly IMenuApiService menuApiService;
        private readonly IRoleModulePermissionService roleModulePermissionService;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="apiService"></param>
        /// <param name="logger"></param>
        public ApiController(IApiService apiService, ILogger<ApiInfo> logger, IMenuApiService menuApiService, IRoleModulePermissionService roleModulePermissionService)
        {
            this._apiService = apiService;
            this._logger = logger;
            this.menuApiService = menuApiService;
            this.roleModulePermissionService = roleModulePermissionService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        [HttpGet("Tree")]
        public async Task<List<ApiTreeDto>> Tree(long? parentId)
        {
            var where = PredicateBuilder.New<ApiInfo>(true);
            if (parentId.HasValue)
            {
                where.And(a => a.ParentId == parentId.Value);
            }
            var orderBy = new OrderBy<ApiInfo>(a => a.OrderSort, SortDirection.Ascending);
            var data = await _apiService.QueryList<ApiTreeDto>(where, orderBy);

            List<ApiTreeDto> list = new List<ApiTreeDto>();

            var controllers = data.Select(a => a.Controller).Distinct();
            foreach (var item in controllers)
            {
                ApiTreeDto obj = new ApiTreeDto();
                obj.Name = item;
                obj.Gid = Guid.NewGuid().ToString();

                obj.Children = data.Where(a => a.Controller == item).ToList();

                list.Add(obj);

            }

            return list;

        }

        [HttpPost("LoadApi")]
        public async Task<long> LoadApi(string url)
        {
            IHttpContextAccessor httpContextAccessor = App.GetService<IHttpContextAccessor>();

            HttpRequest request = httpContextAccessor.HttpContext.Request;

            var json = await Common.Helper.HttpHelper.GetUtf8Async(request.Scheme + "://" + request.Host + "/swagger/Default/swagger.json", "");

            JObject data = (JObject)JsonConvert.DeserializeObject(json);

            //JArray Jarows = JArray.Parse(data["paths"].ToString());

            var paths = data["paths"];

            List<ApiInfo> apiList = new List<ApiInfo>();

            foreach (dynamic item in paths)
            {
                try
                {
                    var name = item.Name;

                    var value = item.Value;

                    foreach (dynamic itemValue in value)
                    {
                        if (itemValue == null)
                        {
                            continue;
                        }

                        ApiInfo obj = new ApiInfo();
                        obj.Controller = itemValue.Value["tags"]?.First?.Value;
                        obj.Name = name;
                        obj.LinkUrl = name;
                        obj.Action = itemValue.Name;
                        obj.Description = itemValue.Value["summary"]?.Value;
                        obj.Enabled = true;

                        int index = apiList.FindIndex(x => x.Action == obj.Action);

                        if (index >= 0)
                        {
                            if (!apiList[index].Action.Contains(obj.Action))
                            {
                                apiList[index].Action = string.Format("{0},{1}", apiList[index].Action, obj.Action);
                            }
                        }

                        apiList.Add(obj);
                    }
                }
                catch (Exception ex)
                {
                    throw;
                }
            }

            int n = 0;

            var urls = apiList.Select(a => a.LinkUrl);
            var noList = await _apiService.QueryList(a => !urls.Contains(a.LinkUrl));
            var noIds = noList.Select(a => a.Id.ObjToLong());

            //删除 不包含的 接口
            await _apiService.Delete(a => noIds.Contains(a.Id));

            //删除 菜单绑定的 接口
            await menuApiService.Delete(a => noIds.Contains(a.ApiId));

            foreach (var item in apiList)
            {
                var model = await _apiService.QueryInfo(a => a.LinkUrl.ToLower() == item.LinkUrl.ToLower());

                if (model != null)
                {
                    if (model.Action.Contains(item.Action))
                    {
                        //是否包含 请求的方式
                        continue;
                    }

                    model.Action = string.Format("{0},{1}", model.Action, item.Action);
                    model.Description = item.Description;

                    if (await _apiService.Update(model))
                    {
                        n++;
                    }
                    continue;
                }

                long apiId = await _apiService.AddIdentity(item);

                if (apiId > 0)
                {
                    n++;
                    item.Id = apiId.ObjToInt();
                    item.Code = string.Format("{0}-{1}", item.Controller, apiId);

                    await _apiService.Update(item);
                }
            }

            return n;
        }

        /// <summary>
        /// 添加
        /// </summary> 
        /// <returns></returns>
        [HttpPost("Add")]
        [NonAction]
        public async Task<long> Add(ApiAddDto req)
        {
            return await _apiService.AddIdentity(req.Adapt<ApiInfo>());
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        [NonAction]
        public async Task<bool> Update(ApiEditDto req)
        {
            return await _apiService.Update(req.Adapt<ApiInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        [NonAction]
        public async Task<bool> Delete(int id)
        {
            int result = await _apiService.Delete(a => a.Id == id);

            return result > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        [NonAction]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _apiService.DeleteByIds(items);

            return result;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<ApiDto> Get(int id)
        {
            return await _apiService.QueryInfo<ApiDto>(a => a.Id == id);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        [NonAction]
        public async Task<ApiDetailDto> Detail(int id)
        {
            var obj = await _apiService.QueryInfo(a => a.Id == id);

            if (obj != null)
            {
                return _apiService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<ApiListDto>> List([FromQuery] ApiListQueryDto req)
        {
            var list = new List<ApiListDto>();
            var where = PredicateBuilder.New<ApiInfo>(true);
            var orderBy = new OrderBy<ApiInfo>(req.order, req.sort);
            var data = await _apiService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _apiService.Join(item);
                list.Add(detail.Adapt<ApiListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        [NonAction]
        public async Task<PageQueryResult<ApiListDto>> Query([FromQuery] ApiPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<ApiListDto>> QueryPost(ApiPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<ApiListDto>> PageQuery([FromQuery] ApiPageQueryDto req)
        {
            var list = new List<ApiListDto>();
            var where = PredicateBuilder.New<ApiInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                where.And(a => (a.Name.Contains(req.searchKey) || a.LinkUrl.Contains(req.searchKey)));
            }

            var totalCount = await _apiService.CountAsync(where);
            var orderBy = new OrderBy<ApiInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _apiService.QueryList(where, orderBy, paging);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _apiService.Join(item);
                list.Add(detail.Adapt<ApiListDto>());
            }
            #endregion

            return new PageQueryResult<ApiListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

    }
}





