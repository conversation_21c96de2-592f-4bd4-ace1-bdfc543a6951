<template>
	<div style="color: #f56c6c; padding: 5px">{{ state.loginMsg }}</div>
	<el-form ref="ruleFormRef" :model="state.ruleForm" :rules="state.rules" size="large" class="login-content-form">
		<el-form-item class="login-animation1" prop="loginName">
			<el-input ref="unameInput" text placeholder="Username" v-model="state.ruleForm.loginName" clearable
				autocomplete="off" @keyup.enter="onLogin">
				<template #prefix>
					<el-icon class="el-input__icon"><ele-User /></el-icon>
				</template>
			</el-input>
		</el-form-item>
		<el-form-item class="login-animation2" prop="password">
			<el-input :type="state.isShowPassword ? 'text' : 'password'" placeholder="Password"
				v-model="state.ruleForm.password" autocomplete="off" @keyup.enter="onLogin">
				<template #prefix>
					<el-icon class="el-input__icon"><ele-Unlock /></el-icon>
				</template>
				<template #suffix>
					<el-icon @click="state.isShowPassword = !state.isShowPassword">
						<template v-if="state.isShowPassword">
							<img src="/img/show.svg" />
						</template>
						<template v-else>
							<img src="/img/hide.svg" />
						</template>
					</el-icon>
				</template>
			</el-input>
		</el-form-item>

		<el-form-item style="margin-bottom: 1px">
			<el-row style="text-align: right; width: 100%; display: block">
				<!-- <el-col :span="12" v-if="false">
					<el-checkbox :label="$t('message.login.rememberMe')" v-model="ruleForm.autologin"></el-checkbox>
				</el-col> -->
				<!-- <el-col :span="12" style="text-align: right"> -->
				<el-button type="text" @click="openForgetPwd">{{ $t('message.login.forgetPassword') }}</el-button>
				<!-- </el-col> -->
			</el-row>
		</el-form-item>

		<el-form-item class="login-animation4">
			<el-button type="primary" class="login-content-submit" round v-waves @click="onLogin"
				:loading="state.loading.signIn">
				<span>{{ $t('message.account.accountBtnText') }}</span>
			</el-button>

			<div class="box" style="margin-top: 20px;">
				<span class="line"></span>
				<span class="text">BPTS Internal Log in</span>
				<span class="line"></span>
			</div>

			<el-button type="primary" class="login-content-submit" round v-waves @click="onSSOLogin"
				:loading="state.loading.signInSSO">
				<span>{{ $t('message.account.accountSSOBtnText') }}</span>
			</el-button>
		</el-form-item>

		<el-dialog v-model="dialogVisible" :title="$t('message.login.forgetPwdTitle')" width="38%"
			:close-on-click-modal="false" :close="handleClose" center>
			<el-row class="forgetForm-row">
				<div style="margin-bottom: 20px; width: 100%">
					<b>{{ $t('message.login.forgetTips') }}</b>
				</div>
				<el-form ref="forgetFormRef" :model="forgetForm" :rules="state.forgetRules" label-position="top"
					class="forgetForm">
					<el-form-item :label="$t('message.login.userName')" :label-width="140" prop="UserName"
						style="font-weight: bold">
						<el-input v-model="forgetForm.UserName" />
					</el-form-item>
					<el-form-item :label="$t('message.login.email')" :label-width="140" prop="Email"
						style="font-weight: bold">
						<el-input v-model="forgetForm.Email" />
					</el-form-item>
				</el-form>
				<div style="margin-top: 20px; width: 100%">{{ $t('message.login.additional') }}</div>
			</el-row>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="handleClose" size="default">{{ $t('message.login.cancel') }}</el-button>
					<el-button type="primary" @click="submitForgetPwd" size="default"> {{ $t('message.login.resetPwd')
					}} </el-button>
				</span>
			</template>
		</el-dialog>
	</el-form>
</template>

<script setup lang="ts" name="loginAccount">
import { reactive, computed, ref, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, type FormInstance, ElLoading, MessageParamsWithType } from 'element-plus';
import { useI18n } from 'vue-i18n';
import Cookies from 'js-cookie';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import { useThemeConfig } from '/@/stores/themeConfig';
import { initFrontEndControlRoutes } from '/@/router/frontEnd';
import { initBackEndControlRoutes } from '/@/router/backEnd';
import { Session, Local } from '/@/utils/storage';
import { formatAxis } from '/@/utils/formatTime';
import { NextLoading } from '/@/utils/loading';
import userApi from '/@/api/user';

import loginApi from '/@/api/login';

// 定义变量内容
let loadingAutoLogin: any = null;
const dialogVisible = ref(false);
const forgetFormRef = ref<FormInstance>();
const unameInput = ref();

// 响应式数据
const forgetForm = reactive({
	UserName: '',
	Email: '',
});

const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

const { t } = useI18n();
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const route = useRoute();
const router = useRouter();
const state = reactive({
	isShowPassword: false,
	ruleForm: {
		autologin: false,
		loginName: '',
		password: '',
		code: '',
		userType: 'Staff Member',
		loginType: 0,
	},
	rules: {
		loginName: [{ required: true, message: t('message.login.inputUserName'), trigger: 'blur' }],
		password: [{ required: true, message: t('message.login.inputPwd'), trigger: 'blur' }],
	},
	forgetRules: {
		UserName: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' }],
		Email: [
			{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' },
			{
				pattern: /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
				message: t('message.login.invalidEmail'),
				trigger: ['blur', 'change'],
			},
		],
	},
	loading: {
		signIn: false,
		signInSSO: false,
	},
	loginMsg: '' as string,
});

// 时间获取
const currentTime = computed(() => {
	return formatAxis(new Date());
});

// 打开忘记密码对话框
const openForgetPwd = () => {
	dialogVisible.value = true;
	forgetForm.UserName = '';
	forgetForm.Email = '';
};

// 开始自动登录加载
const startLoadingAutoLogin = () => {
	loadingAutoLogin = ElLoading.service({
		lock: true,
		text: 'Loading',
		background: 'rgba(0, 0, 0, 0.7)',
	});
};

const handleClose = () => {
	forgetFormRef.value?.resetFields();
	dialogVisible.value = false;
};

// 提交忘记密码表单
const submitForgetPwd = () => {
	forgetFormRef.value?.validate((valid: boolean) => {
		if (!valid) {
			return;
		} else {
			userApi
				.ForgetPwd(forgetForm)
				.then((rs: { data: boolean }) => {
					if (rs.data == false) {
						ElMessage.error(t('message.login.error'));
					} else {
						ElMessage.success(t('message.login.success'));
						dialogVisible.value = false;
					}
				})
				.catch((rs: { resultMsg: MessageParamsWithType }) => {
					ElMessage.error(rs.resultMsg);
				});
		}
	});
};

const ruleFormRef = ref<FormInstance>();

// 登录
const onLogin = async () => {
	ruleFormRef.value?.validate((valid: boolean) => {
		if (!valid) {
			return;
		}
		commonLogin(false, 0);
	});
};

const onSSOLogin = async () => {
	Local.clearToken();

	startLoadingAutoLogin();

	await loginApi
		.SSOLogin({ bptsSSOLogin: '8c303c46-79d1-44cd-ade6-fd0b2c7bec18' })
		.then((rs) => {
			window.location.href = rs.data.data;
		})
		.finally(() => {
			if (loadingAutoLogin != null) {
				loadingAutoLogin.close();
			}
		});
};


// 通用登录
const commonLogin = (ssoLogin: any, bptsSSOLogin: any) => {
	state.loading.signIn = true;

	loginApi
		.Signin(state.ruleForm)
		.then((rs) => {
			if (rs.data.loginMsg != '') {
				state.loginMsg = rs.data.loginMsg;
				return false;
			}

			Session.clear();
			Local.clearToken();

			const accessToken = rs.data.token;
			const accessTokenExp = rs.data.exp;
			const refreshToken = rs.data.refreshToken;

			Local.set('token', accessToken);
			Local.set('tokenExp', accessTokenExp);
			Local.set('refreshToken', refreshToken);

			onSignIn(accessToken, accessTokenExp, state.ruleForm.loginName, ssoLogin, bptsSSOLogin);
		})
		.catch((rs) => {
			ElMessage.error(rs.resultMsg);
			state.loginMsg = '';
		})
		.finally(() => {
			state.loading.signIn = false;

			if (loadingAutoLogin != null) {
				setTimeout(() => {
					loadingAutoLogin.close();
				}, 2000);
			}
		});
};

// 登录成功后的处理
const onSignIn = async (loginToken: any, loginTokenExp: any, loginName: any, ssoLogin: any, bptsSSOLogin: any) => {
	const rs: any = await loginApi.GetMyInfo();

	if (rs.resultCode !== 200) {
		ElMessage.error(rs.resultMsg);
		return;
	}

	if (rs.data.realName.length == 0) {
		ElMessage.error(t('lang.staticRoutes.noAssign'));
		return;
	}

	Local.set('SystemUrl', rs.data.systemUrl);

	if (rs.data.needToChangePwd) {
		Local.set('needToChangePwd', rs.data.needToChangePwd);
	}

	// 存储用户信息到浏览器缓存
	let userInfos = {
		userId: rs.data.id,
		userName: rs.data.name,
		mobile: rs.data.mobile,
		email: rs.data.email,
		photo: rs.data.photo,
		lastLoginTime: rs.data.lastLoginTime,
		realName: rs.data.realName,
		lastLoginIp: rs.data.lastLoginIp,
		roles: rs.data.roleNames,
		authBtnList: rs.data.permissions,
		userType: rs.data.userType,
		language: rs.data.language,
		projects: rs.data.projects,
		privateTicket: rs.data.privateTicket,
		nodeId: rs.data.nodeId,
		ssoLogin: ssoLogin,
		bptsSSOLogin: bptsSSOLogin
	};

	if (userInfos.language == null) {
		userInfos.language = 'en';
	}

	Local.set('userInfo', userInfos);

	if (!themeConfig.value.isRequestRoutes) {
		// 前端控制路由，2、请注意执行顺序
		const isNoPower = await initFrontEndControlRoutes();
		signInSuccess(isNoPower);
	} else {
		// 模拟后端控制路由，isRequestRoutes 为 true，则开启后端控制路由
		// 添加完动态路由，再进行 router 跳转，否则可能报错 No match found for location with path "/"
		const isNoPower = await initBackEndControlRoutes();
		// 执行完 initBackEndControlRoutes，再执行 signInSuccess
		signInSuccess(isNoPower);
	}
};

type LocationQueryRaw = {};

// 登录成功后的跳转
const signInSuccess = (isNoPower: boolean | undefined) => {
	if (isNoPower) {
		ElMessage.warning('Sorry, you do not have login privileges');
		Session.clear();
		Local.clearToken();
	} else {
		let path = '/';

		let query: LocationQueryRaw = {};

		const queryParams = route.query?.params;
		const params = queryParams ? JSON.parse(<string>queryParams) : null;
		const crmAutoLogin = params ? params.crmAutoLogin : null;
		const needToChangePwd = Local.get('needToChangePwd');

		if (needToChangePwd) {
			path = '/change';
		} else if (route.query?.redirect) {
			path = <string>route.query?.redirect;

			if (params && crmAutoLogin != 1) {
				query = Object.keys(params).length > 0 ? params : '';
			}
		}

		router.push({ path });
		// router.push({ path, query });

		// 登录成功提示
		const signInText = t('message.signInText');
		ElMessage.success(`${signInText}`);
		// 添加 loading，防止第一次进入界面时出现短暂空白
		NextLoading.start();
	}

	state.loading.signIn = false;

	if (loadingAutoLogin != null) {
		setTimeout(() => {
			loadingAutoLogin.close();
		}, 2000);
	}
};

onMounted(async () => {
	const queryParams = route.query;

	const strParams: any = route.query?.params;

	// if (queryParams?.autoLogin) {
	// 	if (queryParams?.autoLogin.toString() == '1') {
	// 		alert(JSON.stringify(route.query));
	// 		const userInfos = Local.get('userInfo');

	// 		onSignIn(Local.get('token'), Local.get('tokenExp'), userInfos.userName);
	// 	} else {
	// 	}
	// } else


	if (queryParams?.messageType) {
		await SsoLogin(queryParams?.messageType, queryParams?.bptsSSOLogin)
	} else if (queryParams?.bptsSSOLogout && queryParams?.bptsSSOLogout == "8c303c46-79d1-44cd-ade6-fd0b2c7bec18") {
		startLoadingAutoLogin();

		if (Local.get('token')) {
			await loginApi.SignOut({ token: Local.get('token'), userName: userInfos.value.userName }).then(async (rs) => {
				Local.clearToken();;
				loadingAutoLogin.close();
				// router.push('/login');
			});
		} else {
			loadingAutoLogin.close();
		}

		return;
	}


	if (strParams && strParams !== '{}') {
		const obj = JSON.parse(strParams);

		// alert(JSON.stringify(obj));

		if (obj.crmAutoLogin == 1) {
			startLoadingAutoLogin();

			// state.ruleForm.loginName = JSON.parse(<string>route.query?.params).loginEmail;
			// state.ruleForm.loginType = 3;

			// commonLogin(false, 0);

			const accessToken = JSON.parse(<string>route.query?.params).token;
			const accessTokenExp = JSON.parse(<string>route.query?.params).tokenExp;
			const refreshToken = JSON.parse(<string>route.query?.params).refreshToken;

			Local.set('token', accessToken);
			Local.set('tokenExp', accessTokenExp);
			Local.set('refreshToken', refreshToken);

			onSignIn(accessToken, accessTokenExp, "", false, "");
		}
	} else {
		const token = Local.get('token');
		const userInfos = Local.get('userInfo');

		if (token && userInfos) {
			//在获取当前用户的左侧菜单
			await initBackEndControlRoutes();

			signInSuccess(false);
		}
	}

	nextTick(() => {
		unameInput.value.focus();
	});
});

let callCount = 0;

const SsoLogin = async (messageType: any, bptsSSOLogin: any) => {
	callCount++;
	if (callCount > 50) {
		if (loadingAutoLogin != null) {
			loadingAutoLogin.close();
		}

		return;
	}

	startLoadingAutoLogin();

	loginApi.GetSSOLoginMessage({ dataKey: messageType })
		.then((rs) => {
			if (rs.data.data && (rs.data.data == 'SSOLogin1' || rs.data.data == 'SSOLogin2')) {
				if (loadingAutoLogin != null) {
					loadingAutoLogin.close();
				}

				state.loginMsg = rs.data.resultMsg;

				return;
			} else if (rs.data.data && rs.data.data == 'SSOLogin3') {
				// state.ruleForm.loginName = rs.data.resultMsg;
				// state.ruleForm.loginType = 3;

				// commonLogin(true, bptsSSOLogin);


				if (rs.data.resultMsg != null && rs.data.resultMsg != '') {
					var loginResult = JSON.parse(rs.data.resultMsg);

					Session.clear();

					const accessToken = loginResult.Token;
					const accessTokenExp = loginResult.Exp;
					const refreshToken = loginResult.RefreshToken;

					Local.set('token', accessToken);
					Local.set('tokenExp', accessTokenExp);
					Local.set('refreshToken', refreshToken);

					onSignIn(accessToken, accessTokenExp, loginResult.UserName, true, bptsSSOLogin);
				}
			} else {
				SsoLogin(messageType, bptsSSOLogin);
			}
		});
}
</script>

<style scoped lang="scss">
.login-content-form {
	margin-top: 20px;

	@for $i from 1 through 4 {
		.login-animation#{$i} {
			opacity: 0;
			animation-name: error-num;
			animation-duration: 0.5s;
			animation-fill-mode: forwards;
			animation-delay: calc($i/10) + s;
		}
	}

	.login-content-password {
		display: inline-block;
		width: 20px;
		cursor: pointer;

		&:hover {
			color: #909399;
		}
	}

	.login-content-code {
		width: 100%;
		padding: 0;
		font-weight: bold;
		letter-spacing: 5px;
	}

	.login-content-submit {
		width: 100%;
		letter-spacing: 2px;
		font-weight: 300;
		margin-top: 15px;
	}

	.el-icon img {
		height: 1em;
		width: 1em;
		cursor: pointer;
	}
}

.forgetForm-row {
	max-width: 400px;
	margin: 0 auto;
}

.forgetForm {
	width: 100%;
}

.el-form-item ::v-deep(.el-form-item__error) {
	display: block;
}

.box {
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 20px 0;
	width: 100%;
}

.line {
	height: 1px;
	flex-grow: 1;
	background-color: #bcbcbc;
}

.text {
	font-size: 14px;
	padding: 0 10px;
	white-space: nowrap;
}
</style>
