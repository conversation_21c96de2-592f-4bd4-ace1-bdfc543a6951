﻿<#@ template debug="false" hostspecific="true" language="C#" #>
<#@ output extension="/" #>
<#@ assembly name="System.Core.dll" #>
<#@ assembly name="System.Data.dll" #>
<#@ assembly name="System.Data.DataSetExtensions.dll" #>
<#@ assembly name="System.Xml.dll" #>
<#@ import namespace="System" #>
<#@ import namespace="System.Xml" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Data.SqlClient" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.IO" #>
<#@ include file="$(ProjectDir)DbHelper.ttinclude"  #>
<#@ include file="$(ProjectDir)ModelAuto.ttinclude"	#>
<# var manager = new Manager(Host, GenerationEnvironment, true); #>
<# 
	var OutputPath1 =Path.GetDirectoryName(Path.GetDirectoryName(Path.GetDirectoryName(Host.TemplateFile+"..")+"..")+"..");
	OutputPath1=Path.Combine(OutputPath1,"YunLanCrm.Model","Models_New");
	if (!Directory.Exists(OutputPath1))
	{
	    Directory.CreateDirectory(OutputPath1);
	}
#>
<# foreach (var item in DbHelper.GetDbTablesNew(config.ConnectionString, config.DbDatabase,config.TableName))
   {
		 var tableName=item.ToString();
		manager.StartBlock(tableName+".cs",OutputPath1);//文件名
	   #>
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace YunLanCrm.Model.Models
{
	 ///<summary>
	 ///<#=tableName#>
	 ///</summary>
	 [Table("<#=tableName#>")]	
	 public class <#=tableName#>
	 {
	 <# foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, tableName)){#>

		 /// <summary>
        /// <#= column.Remark == "" ? column.ColumnName : column.Remark.Replace("\r\n"," ") #>
        /// </summary>
		<# 
		 if(column.IsPrimaryKey) 
		 {#>[Key]
		<#}#><# if(!column.IsNullable) {#>[Required]
		<# }#>public <#= column.CSharpType#><# if(column.CommonType.IsValueType && column.IsNullable){#>?<#}#> <#=column.ColumnName#> { get; set; }
	<#
	 }
	 #> 
	 }
}	 
<#
   manager.EndBlock(); 
   }
  manager.Process(true);
   #>