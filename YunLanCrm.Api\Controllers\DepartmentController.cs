﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.Department;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    [NonController]
    public class DepartmentController : ControllerBase
    {
        private readonly ILogger<DepartmentInfo> _logger;
        /// <summary>
        /// 
        /// </summary>
        private readonly IDepartmentService _departmentService;
        private readonly IOrganizationService _organizationService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="departmentService"></param>
        /// <param name="organizationService"></param>
        /// <param name="logger"></param>
        public DepartmentController(IDepartmentService departmentService, IOrganizationService organizationService, ILogger<DepartmentInfo> logger)
        {
            this._departmentService = departmentService;
            this._logger = logger;
            this._organizationService = organizationService;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        [HttpGet("Tree")]
        public async Task<List<DepartmentTreeDto>> Tree(long? parentId)
        {
            var where = PredicateBuilder.New<DepartmentInfo>(true);
            if (parentId.HasValue)
            {
                where.And(a => a.Pid == parentId.Value);
            }
            var orderBy = new OrderBy<DepartmentInfo>(a => a.Sort, SortDirection.Ascending);

            var data = await _departmentService.QueryList<DepartmentTreeDto>(where, orderBy);

            foreach (var item in data)
            {
                List<int> pidarr = new() { };
                var parent = data.FirstOrDefault(d => d.Id == item.Pid);

                while (parent != null)
                {
                    pidarr.Add(parent.Id);
                    parent = data.FirstOrDefault(d => d.Id == parent.Pid);
                }

                pidarr.Reverse();
                pidarr.Insert(0, 0);
                item.PidArr = pidarr;
            }

            return _departmentService.ParseTree(data, 0);
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(DepartmentAddDto req)
        {
            if ((await _organizationService.AnyAsync(a => a.Name == req.Name && a.ParentId == req.ParentOrgId && a.OrgType == 2)))
            {
                throw new Exception("Name already exists");
            }

            //先把数据添加到 组织架构表
            OrganizationInfo org = new OrganizationInfo();
            org.Name = req.Name;
            org.OrgType = 2;
            org.Sort = 0;
            org.Status = 0;
            org.IsDeleted = false;
            org.CreateAt = DateTime.Now;
            org.UpdateAt = DateTime.Now;
            //这个很重要，请注意区分
            org.ParentId = req.ParentOrgId;

            req.OrgId = await _organizationService.AddIdentity(org);

            return await _departmentService.AddIdentity(req.Adapt<DepartmentInfo>());
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(DepartmentEditDto req)
        {
            //部门信息
            var obj = await _departmentService.QueryInfo(a => a.Id == req.Id);
            if (obj == null)
            {
                throw new Exception("Company does not exist");
            }

            //组织信息
            var org = await _organizationService.QueryInfo(a => a.OrgId == obj.OrgId);

            if (req.Name != obj.Name)
            {
                if ((await _organizationService.AnyAsync(a => a.Name == req.Name && a.ParentId == req.ParentOrgId && a.OrgType == 1)))
                {
                    throw new Exception("Name already exists");
                }
                //新的名称
                org.Name = req.Name;
            }

            org.ParentId = req.ParentOrgId;

            await _organizationService.Update(org);

            return await _departmentService.Update(req.Adapt<DepartmentInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(int id)
        {
            var obj = await _departmentService.QueryInfo(a => a.Id == id);

            int result = await _departmentService.Delete(a => a.Id == id);

            if (result > 0)
            {
                await _departmentService.Delete(a => a.Pid == id);
                await _organizationService.DeleteById(obj.OrgId);
            }

            return result > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _departmentService.DeleteByIds(items);

            return result;
        }

        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<DepartmentDto> Get(int id)
        {
            var obj= await _departmentService.QueryInfo<DepartmentDto>(a => a.Id == id);

            if (obj != null)
            {
                var orgObj = await _organizationService.QueryInfo(a => a.OrgId == obj.OrgId);

                obj.ParentOrgId = orgObj.ParentId;
            }

            return obj;
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<DepartmentDetailDto> Detail(int id)
        {
            var obj = await _departmentService.QueryInfo(a => a.Id == id);

            if (obj != null)
            {
                return _departmentService.Join(obj);
            }

            return null;
        }


        /// <summary>
        /// 根据组织架构ID
        /// 获取一个详细信息
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        [HttpGet("DetailByOrgId/{orgId}")]
        public async Task<DepartmentDetailDto> DetailByOrgId(int orgId)
        {
            var obj = await _departmentService.QueryInfo(a => a.OrgId == orgId);

            if (obj != null)
            {
                return _departmentService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<DepartmentListDto>> List([FromQuery] DepartmentListQueryDto req)
        {
            var list = new List<DepartmentListDto>();
            var where = PredicateBuilder.New<DepartmentInfo>(true);
            var orderBy = new OrderBy<DepartmentInfo>(req.order, req.sort);
            var data = await _departmentService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _departmentService.Join(item);
                list.Add(detail.Adapt<DepartmentListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<DepartmentListDto>> Query([FromQuery] DepartmentPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<DepartmentListDto>> QueryPost(DepartmentPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<DepartmentListDto>> PageQuery([FromQuery] DepartmentPageQueryDto req)
        {
            var list = new List<DepartmentListDto>();
            var where = PredicateBuilder.New<DepartmentInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            var totalCount = await _departmentService.CountAsync(where);
            var orderBy = new OrderBy<DepartmentInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _departmentService.QueryList(where, orderBy, paging);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _departmentService.Join(item);
                list.Add(detail.Adapt<DepartmentListDto>());
            }
            #endregion

            return new PageQueryResult<DepartmentListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

    }
}