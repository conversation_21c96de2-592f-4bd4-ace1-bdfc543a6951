﻿<template>
	<div class="selectTempEmail-container">
		<el-dialog :title="title" v-model="isShowDialog" width="769px" :close-on-click-modal="false">
			<div class="list-search-container">
				<el-form :inline="true" size="small">
					<el-form-item label="所属分类">
						<el-select v-model="tableData.param.categoryId" placeholder="Please Select" clearable class="w100">
							<el-option v-for="item in workflowTypes" :label="item.itemName" :value="item.itemValue" :key="item.itemId"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="关键字">
						<el-input size="small" v-model="tableData.param.searchKey" placeholder="请输入关键字" clearable style="max-width: 180px"> </el-input>
					</el-form-item>

					<el-form-item style="vertical-align: top">
						<el-button size="small" type="primary" class="ml10" @click="onSearch">
							<el-icon>
								<ele-Search />
							</el-icon>
							查询
						</el-button>
					</el-form-item>
				</el-form>
			</div>
			<div class="scTable" style="height: 100%" ref="scTableMain">
				<div class="scTable-table">
					<el-table
						:data="tableData.data"
						v-loading="tableData.loading"
						height="calc(100%)"
						@row-dblclick="onDetail"
						@selection-change="selectionChange"
					>
						<template #empty>
							<el-empty description="暂无数据" :image-size="100"></el-empty>
						</template>
						<el-table-column align="left" label="操作" width="150">
							<template #default="{ row }">
								<el-button size="mini" type="text" @click="onSelect(row)">选择</el-button>
							</template>
						</el-table-column>

						<el-table-column label="所属分类" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.categoryName }}</span>
							</template>
						</el-table-column>
						<el-table-column label="模板名称" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.name }}</span>
							</template>
						</el-table-column>
						<el-table-column label="状态" show-overflow-tooltip>
							<template #default="{ row }">
								<el-icon v-if="row.status == 0" style="color: #67c23a">
									<ele-SuccessFilled />
								</el-icon>
								<el-icon v-if="row.status == 1" style="color: red">
									<ele-SuccessFilled />
								</el-icon>
							</template>
						</el-table-column>
						<el-table-column label="创建时间" width="200">
							<template #default="{ row }">
								<span>{{ formatStrDate(row.createdAt) }}</span>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<div class="scTable-page">
					<el-pagination
						@size-change="onSizechange"
						@current-change="onCurrentChange"
						:pager-count="5"
						:page-sizes="[10, 20, 30]"
						v-model:current-page="tableData.param.pageIndex"
						background
						v-model:page-size="tableData.param.pageSize"
						layout="total, sizes, prev, pager, next, jumper"
						:total="tableData.total"
					>
					</el-pagination>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, ref, defineComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate } from '/@/utils/formatTime';
import emailTemplateApi from '/@/api/emailTemplate/index';
import { init } from 'echarts';

interface DialogParams {
	action: string;
	templateId: number;
}

export default defineComponent({
	name: 'selectTempEmail',
	components: {},
	setup(props, context) {
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					pageIndex: 1,
					pageSize: 5,
					categoryId: '',
					searchKey: '',
					order: 'templateId',
					sort: 'desc', // asc or desc
				},
			},
			title: '选择邮件模板',
			isShowDialog: false,
			dialogParams: {} as DialogParams,
		});
		//初始化
		const onInit = () => {
			state.tableData.loading = true;
			emailTemplateApi
				.Query(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
				})
				.catch((rs) => {})
				.finally(() => (state.tableData.loading = false));
		};
		//搜索
		const onSearch = () => {
			onInit();
		};
		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};

		//选择
		const onSelect = (row: any) => {
			context.emit('fetchData', row);
			closeDialog();
		};

		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			state.dialogParams = parmas;
			state.isShowDialog = true;

			onInit();
		};
		// 关闭弹窗
		const closeDialog = () => {
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		return {
			formatStrDate,
			onInit,
			onSizechange,
			onCurrentChange,
			onSearch,
			openDialog,
			onCancel,
			onSelect,
			...toRefs(state),
		};
	},
});
</script>
