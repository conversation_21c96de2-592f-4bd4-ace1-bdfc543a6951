export default {
	workflow_btnArea : {
        search : 'Search',
        create : 'Create',

        edit : 'Edit',
        delete : 'Delete',
        more : 'More',
        disable : 'Disable Workflow',
    },
    workflow_msgArea : {
        missingStartNode : 'Missing start node.',
        success : 'Success',
        fail : 'Fail',
    },
    workflow_placeholder : {
        description : 'Please input a description'
    },
    workflow_labelArea : {
        keywords : 'Keywords',
        type : 'Type',
        enable : 'Enable',
        disable : 'Disable',
        workflowDesign : 'Design',
        information : 'Information',
        back : 'Back',
        next : 'Next',
        save : 'Save',
        cancel : 'Cancel',
        clickImageToUpload : 'Click to select picture',
        invoiceInfo : 'Invoice Info',
        requireInfo : 'Require Info',
        ticketInfo : 'Ticket Info',
    },
    workflow_fieldArea : {
        workflowName : 'Workflow Name',
        workflowCode : 'Workflow Code',
        type : 'Type',
        createdByName : 'Created By Name',
        createdAt : 'CreatedAt',
        status : 'Status',
        operation : 'Operation',
        businessTable : 'Business Table',
        sort : 'Sort',
        description: 'Description',
        flowChart : 'Flow Chart',
    }
    
};