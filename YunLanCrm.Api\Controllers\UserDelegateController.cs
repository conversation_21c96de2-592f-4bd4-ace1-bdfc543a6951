﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.UserDelegate;
using YunLanCrm.Dto.User;
using YunLanCrm.Services;
using YunLanCrm.Model.Dto.UserDelegate;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class UserDelegateController : ControllerBase
    {
        private readonly ILogger<UserDelegateInfo> _logger;
        private readonly IUserDelegateService _userDelegateService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="userDelegateService"></param>
        /// <param name="logger"></param>
        public UserDelegateController(IUserDelegateService userDelegateService, ILogger<UserDelegateInfo> logger)
        {
            _logger = logger;
            _userDelegateService = userDelegateService;
            
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(UserDelegateAddOrUpdateDto req)
        {
            return await _userDelegateService.AddUserDelegate(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(UserDelegateAddOrUpdateDto req)
        {
            return await _userDelegateService.UpdateUserDelegate(req);
        }


        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _userDelegateService.Delete(a => a.Id == id);

            return result > 0;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _userDelegateService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<UserDelegateDto> Get(long id)
        {
            return await _userDelegateService.QueryInfo<UserDelegateDto>(a => a.Id == id);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<UserDelegateDto>> GetList([FromQuery] UserDelegateListQueryDto req)
        {
            return await _userDelegateService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<UserDelegateDetailDto> Detail(long id)
        {
            return await _userDelegateService.Detail(id);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<UserDelegateDetailDto>> DetailList([FromQuery] UserDelegateListQueryDto req)
        {
            return await _userDelegateService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<UserDelegateDetailDto>> Query([FromQuery] UserDelegatePageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<UserDelegateDetailDto>> QueryPost(UserDelegatePageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<UserDelegateDetailDto>> PageQuery([FromQuery] UserDelegatePageQueryDto req)
        {
            return await _userDelegateService.PageQueryView(req);
        }
        /// <summary>
        /// 取出所有Delegate用户
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("GetSelectUserDelegate")]
        public async Task<List<SelectUserDelegateDto>> GetSelectUserDelegate([FromQuery] UserDelegateDto req)
        {
            return await _userDelegateService.GetSelectUserDelegate(req);
        }
        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("UpdateIsApprove")]
        public async Task<bool> UpdateIsApprove(UserDelegateAddOrUpdateDto req)
        {
            return await _userDelegateService.UpdateIsApprove(req);
        }
        /// <summary>
        /// 批量OnOff
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("UpdateIsApproveBatch")]
        public async Task<bool> UpdateIsApproveBatch(SelectUserDelegateOnOffDto items)
        {
            var result = await _userDelegateService.UpdateIsApproveBatch(items);

            return result;
        }

    }
}