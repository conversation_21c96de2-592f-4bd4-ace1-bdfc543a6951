﻿<#@ template debug="false" hostspecific="true" language="C#" #>
<#@ output extension=".cs" #>
<#@ assembly name="System.Core.dll" #>
<#@ assembly name="System.Data.dll" #>
<#@ assembly name="System.Data.DataSetExtensions.dll" #>
<#@ assembly name="System.Xml.dll" #>
<#@ import namespace="System" #>
<#@ import namespace="System.Xml" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Data.SqlClient" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.IO" #>
<#@ include file="$(ProjectDir)DbHelper.ttinclude"  #>
<#@ include file="$(ProjectDir)ModelAuto.ttinclude"	#>
<# var manager = new Manager(Host, GenerationEnvironment, true); #>


<# 
	var OutputPath1 =Path.GetDirectoryName(Path.GetDirectoryName(Path.GetDirectoryName(Host.TemplateFile+"..")+"..")+"..");
	OutputPath1=Path.Combine(OutputPath1,"YunLanCrm.IServices","IServices_New");
	if (!Directory.Exists(OutputPath1))
	{
	    Directory.CreateDirectory(OutputPath1);
	}
#>



//--------------------------------------------------------------------
//     此代码由T4模板自动生成
//	   生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#> 
//     对此文件的更改可能会导致不正确的行为，并且如果重新生成代码，这些更改将会丢失。
//--------------------------------------------------------------------
<# 
	var tableName=config.TableName;
 #>
<# 
if(tableName!=""){
    #>  


using YunLanCrm.IServices.BASE;
using YunLanCrm.Model.Models;

namespace YunLanCrm.IServices
{	
	/// <summary>
	/// I<#=tableName#>Services
	/// </summary>	
	public interface I<#=tableName#>Services :IBaseServices<<#=tableName#>>
    {

       
    }
}

<# 
    } else{ 

	#>

<# 
    SqlConnection conn = new SqlConnection(config.ConnectionString); 
    conn.Open(); 
    System.Data.DataTable schema = conn.GetSchema("TABLES"); 
 #>

<# 
    foreach(System.Data.DataRow row in schema.Rows) 
    {  
		manager.StartBlock("I"+row["TABLE_NAME"].ToString()+"Services"+".cs",OutputPath1);
	 #>
	//----------<#=row["TABLE_NAME"].ToString()#>开始----------
    

using YunLanCrm.IServices.BASE;
using YunLanCrm.Model.Models;

namespace YunLanCrm.IServices
{	
	/// <summary>
	/// <#=row["TABLE_NAME"].ToString()#>Services
	/// </summary>	
    public interface I<#=row["TABLE_NAME"].ToString()#>Services :IBaseServices<<#=row["TABLE_NAME"].ToString()#>>
	{

       
    }
}

	//----------<#=row["TABLE_NAME"].ToString()#>结束----------
	<# 
		manager.EndBlock(); 
		} 

		{  
		manager.StartBlock("IBaseServices.cs",OutputPath1);//文件名
	 #>
    
	//----------开始----------
	
using YunLanCrm.IServices.BASE;
using YunLanCrm.Model.Models;

namespace YunLanCrm.IServices
{	
	/// <summary>
	/// IBaseRepository
	/// </summary>	
	public interface IBaseServices<TEntity> where TEntity : class
    {

       
    }
}

	//----------结束----------
	<# 
		manager.EndBlock(); 
		} 

		manager.Process(true);
	}
	#> 

