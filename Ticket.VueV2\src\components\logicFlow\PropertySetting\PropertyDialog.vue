<template>
	<div class="property-dialog">
		<ApprovalSettings v-if="nodeData.type === 'ApprovalNode' || nodeData.type === 'StartNode'" :nodeData="nodeData" :lf="lf" :entity="entity" @onClose="handleClose" />
		<CommonProperty v-if="nodeData.type === 'Fork'" :nodeData="nodeData" :lf="lf" @onClose="handleClose" />
		<CommonProperty v-if="nodeData.type === 'Join'" :nodeData="nodeData" :lf="lf" @onClose="handleClose" />
		<IFElseSettings v-if="nodeData.type === 'JugementNode'" :nodeData="nodeData" :lf="lf" :entity="entity" @onClose="handleClose" />
		<PolylineSettings v-if="nodeData.type === 'polyline'" :nodeData="nodeData" :lf="lf" @onClose="handleClose" />
		<CommonProperty v-if="nodeData.type === 'UserTask'" :nodeData="nodeData" :lf="lf" @onClose="handleClose" />
		<CommonProperty v-if="nodeData.type === 'EndNode'" :nodeData="nodeData" :lf="lf" @onClose="handleClose" />
	</div>
</template>
<script>
import ApprovalSettings from './ApprovalSettings.vue';
import IFElseSettings from './IFElseSettings.vue';
import PolylineSettings from './PolylineSettings.vue';
import CommonProperty from './CommonProperty.vue';

export default {
	name: 'PropertyDialog',
	components: {
		ApprovalSettings,
		IFElseSettings,
		PolylineSettings,
		CommonProperty,
	},
	props: {
		nodeData: Object,
		lf: Object,
		entity: String,
	},
	data() {
		return {
			
		};
	},
	mounted() {
	
	},
	methods: {
		handleClose() {
			this.$emit('setPropertiesFinish');
		},
	},
};
</script>
<style>
.property-dialog {
	padding: 40px 20px;
}
</style>
