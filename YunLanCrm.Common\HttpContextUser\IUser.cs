﻿using System.Collections.Generic;
using System.Security.Claims;

namespace YunLanCrm.Common.HttpContextUser
{
    public interface IUser
    {
        string ClientIP { get;  }
        string Device { get;  }
        string Name { get; }
        string Email { get; }
        int UserId { get; set; }
        bool IsAuthenticated();
        IEnumerable<Claim> GetClaimsIdentity();
        List<string> GetClaimValueByType(string ClaimType);

        string GetToken();
        List<string> GetUserInfoFromToken(string ClaimType);

        string UserType { get; set; }

        string GroupIds { get; set; }

        List<int> RoleIds { get; set; }
    }
}
