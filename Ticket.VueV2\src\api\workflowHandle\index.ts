﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class workflowH<PERSON>leApi extends BaseApi {

	GetWorkflowNodeLogs(params: any) {
		return request({
			url: this.baseurl + 'GetWorkflowNodeLogs',
			method: 'get',
			params,
		});
	}

	ApproveWorkflow(data: any) {
		return request({
			url: this.baseurl + 'ApproveWorkflow',
			method: 'post',
			data,
		});
	}

	RejectWorkflow(data: any) {
		return request({
			url: this.baseurl + 'RejectWorkflow',
			method: 'post',
			data,
		});
	}

	TransferWorkflow(data: any) {
		return request({
			url: this.baseurl + 'TransferWorkflow',
			method: 'post',
			data,
		});
	}

	WithdrawWorkflow(data: any) {
		return request({
			url: this.baseurl + 'WithdrawWorkflow',
			method: 'post',
			data,
		});
	}
}

export default new workflowHandleApi('/api/workflowHandle/', 'handleId');
