﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YunLanCrm.Common
{
    public class StringHelper
    {
        private static readonly string _keys = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";


        public static string GetAbcStr(int num)//生成字母随机数
        {
            string a = "0123456789";
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < num; i++)
            {
                sb.Append(a[new Random(Guid.NewGuid().GetHashCode()).Next(0, a.Length - 1)]);
            }

            return sb.ToString();
        }


        public static string NewUnique()
        {
            var TimeStamps = (DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 10000;

            return GetAbcStr(1) + ToString(TimeStamps, 36);
        }


        public static double ToDouble(string v, int radix)
        {
            return BitConverter.Int64BitsToDouble(ToInt64(v, radix));
        }

        public static long ToInt64(string v, int radix)
        {
            return (long)ToUInt64(v, radix);
        }

        public static ulong ToUInt64(string v, int radix)
        {
            if (string.IsNullOrEmpty(v))
            {
                return 0;
            }
            if (radix < 2)
            {
                radix = 10;
            }
            else if (radix > _keys.Length)
            {
                radix = _keys.Length;
            }
            ulong r = 0;
            ulong p = (ulong)radix;
            ulong b = 0;
            for (int l = v.Length - 1, i = l; i > -1; i--)
            {
                char ch = v[i];
                ulong k;
                if (ch >= '0' && ch <= '9')
                {
                    k = (ulong)(ch - '0');
                }
                else if (ch >= 'A' && ch <= 'Z')
                {
                    k = 10 + (ulong)(ch - 'A');
                }
                else if (ch >= 'a' && ch <= 'z')
                {
                    k = 10 + (ulong)(ch - 'a');
                }
                else
                {
                    return 0;
                }
                if (k >= p)
                {
                    return 0;
                }
                if (i == l)
                {
                    r += k;
                    b = p;
                }
                else
                {
                    r += k * b;
                    b = b * p;
                }
            }
            return r;
        }

        public static string ToString(long v, int radix)
        {
            return ToString(v, radix, 0);
        }

        public static string ToString(ulong v, int radix)
        {
            return ToString(v, radix, 0);
        }

        public static string ToString(long v, int radix, int pading)
        {
            return ToString(v, radix, pading, '0');
        }

        public static string ToString(ulong v, int radix, int pading)
        {
            return ToString(v, radix, pading, '0');
        }

        public static string ToString(double v, int radix)
        {
            return ToString(v, radix, 0);
        }

        public static string ToString(double v, int radix, int pading)
        {
            return ToString(v, radix, pading, '0');
        }

        public static string ToString(double v, int radix, int pading, char paddingChar)
        {
            return ToString(BitConverter.DoubleToInt64Bits(v), radix, pading, paddingChar);
        }

        public static string ToString(long v, int radix, int pading, char paddingChar)
        {
            return ToString((ulong)v, radix, pading, paddingChar);
        }

        public static string ToString(ulong v, int radix, int pading, char paddingChar)
        {
            if (radix < 2)
            {
                radix = 10;
            }
            else if (radix > _keys.Length)
            {
                radix = _keys.Length;
            }
            uint p = (uint)radix;
            string r;
            if (v == 0)
            {
                r = "0";
            }
            else
            {
                r = string.Empty;
                while (v > 0)
                {
                    ulong n = v % p;
                    r = _keys[(int)n] + r;
                    v /= p;
                }
            }
            if (pading > r.Length)
            {
                r = r.PadLeft(pading, paddingChar);
            }
            return r;
        }
    }
}
