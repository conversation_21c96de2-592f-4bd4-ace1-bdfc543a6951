﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.RoleModulePermission;
using YunLanCrm.AuthHelper;
using YunLanCrm.IRepository.UnitOfWork;
using YunLanCrm.Model.Dto.RoleModulePermission;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class RoleModulePermissionController : BaseApiController
    {
        private readonly ILogger<RoleModulePermissionInfo> _logger;

        private readonly IRoleModulePermissionService _roleModulePermissionService;

        private readonly IMenuService _menuService;
        private readonly IMenuApiService _menuApiService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly PermissionRequirement _requirement;
        private readonly IUserService _userService;


        /// <summary>
        /// 
        /// </summary>
        /// <param name="roleModulePermissionService"></param>
        /// <param name="menuService"></param>
        /// <param name="requirement"></param>
        /// <param name="menuApiService"></param>
        /// <param name="unitOfWork"></param>
        /// <param name="logger"></param>
        public RoleModulePermissionController(IRoleModulePermissionService roleModulePermissionService,
            IMenuService menuService,
            PermissionRequirement requirement,
            IMenuApiService menuApiService,
            IUnitOfWork unitOfWork,
            IUserService userService,
            ILogger<RoleModulePermissionInfo> logger)
        {
            this._menuService = menuService;
            this._roleModulePermissionService = roleModulePermissionService;
            this._logger = logger;
            this._menuApiService = menuApiService;
            _requirement = requirement;
            _unitOfWork = unitOfWork;
            _userService = userService;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        [NonAction]
        public async Task<long> Add(RoleModulePermissionAddDto req)
        {
            return await _roleModulePermissionService.AddIdentity(req.Adapt<RoleModulePermissionInfo>());
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        [NonAction]
        public async Task<bool> Update(RoleModulePermissionEditDto req)
        {
            return await _roleModulePermissionService.Update(req.Adapt<RoleModulePermissionInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        [NonAction]
        public async Task<bool> Delete(int id)
        {
            int result = await _roleModulePermissionService.Delete(a => a.Id == id);

            return result > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        [NonAction]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _roleModulePermissionService.DeleteByIds(items);

            return result;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        [NonAction]
        public async Task<RoleModulePermissionDto> Get(int id)
        {
            return await _roleModulePermissionService.QueryInfo<RoleModulePermissionDto>(a => a.Id == id);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        [NonAction]
        public async Task<RoleModulePermissionDetailDto> Detail(int id)
        {
            var obj = await _roleModulePermissionService.QueryInfo(a => a.Id == id);

            if (obj != null)
            {
                return _roleModulePermissionService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        [NonAction]
        public async Task<List<RoleModulePermissionListDto>> List([FromQuery] RoleModulePermissionListQueryDto req)
        {
            var list = new List<RoleModulePermissionListDto>();
            var where = PredicateBuilder.New<RoleModulePermissionInfo>(true);
            if (req.menuId.HasValue)
            {
                where.And(a => a.PermissionId == req.menuId.Value);
            }
            if (req.roleId.HasValue)
            {
                where.And(a => a.RoleId == req.roleId.Value);
            }
            if (req.apiId.HasValue)
            {
                where.And(a => a.ModuleId == req.apiId.Value);
            }
            var orderBy = new OrderBy<RoleModulePermissionInfo>(req.order, req.sort);
            var data = await _roleModulePermissionService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _roleModulePermissionService.Join(item);
                list.Add(detail.Adapt<RoleModulePermissionListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        [NonAction]
        public async Task<PageQueryResult<RoleModulePermissionListDto>> Query([FromQuery] RoleModulePermissionPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        [NonAction]
        public async Task<PageQueryResult<RoleModulePermissionListDto>> QueryPost(RoleModulePermissionPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<RoleModulePermissionListDto>> PageQuery([FromQuery] RoleModulePermissionPageQueryDto req)
        {
            var list = new List<RoleModulePermissionListDto>();
            var where = PredicateBuilder.New<RoleModulePermissionInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            var totalCount = await _roleModulePermissionService.CountAsync(where);
            var orderBy = new OrderBy<RoleModulePermissionInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _roleModulePermissionService.QueryList(where, orderBy, paging);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _roleModulePermissionService.Join(item);
                list.Add(detail.Adapt<RoleModulePermissionListDto>());
            }
            #endregion

            return new PageQueryResult<RoleModulePermissionListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

        /// <summary>
        /// 保存菜单权限分配
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Assign")]
        public async Task<bool> Assign([FromBody] RoleMenuInput req)
        {

            bool result = await _roleModulePermissionService.SaveRoleMenus(req);

            if (result)
            {
                _requirement.Permissions.Clear();
            }

            return result;
        }

        [HttpGet("GetPermissionIdByRoleId/{roleId}")]
        public async Task<List<int>> GetPermissionIdByRoleId(int roleId)
        {
            //角色-菜单-api关系表
            var rolePers = await _roleModulePermissionService.Query(d => d.IsDeleted == false && d.RoleId == roleId);

            var permissionTrees = (from child in rolePers
                                   orderby child.Id
                                   select child.PermissionId.ObjToInt()).ToList();

            /*var permissions = await _permissionService.Query(d => d.IsDeleted == false);
            List<string> assignbtns = new List<string>();

            foreach (var item in permissionTrees)
            {
                var pername = permissions.FirstOrDefault(d => d.IsButton && d.Id == item)?.Name;
                if (!string.IsNullOrEmpty(pername))
                {
                    //assignbtns.Add(pername + "_" + item);
                    assignbtns.Add(item.ObjToString());
                }
            }*/

            return permissionTrees;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("GetPermissionList")]
        public async Task<List<int>> GetPermissionList([FromQuery] GetPermissionListInput req)
        {
            return await _roleModulePermissionService.GetPermissionList(req.RoleId, req.menuType);
        }

    }
}