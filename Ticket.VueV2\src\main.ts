import { createApp } from 'vue';
import pinia from '/@/stores/index';
import App from '/@/App.vue';
import router from '/@/router';
import { directive } from '/@/directive/index';
import { i18n } from '/@/i18n/index';
import other from '/@/utils/other';
import { handleError } from '/@/utils/index';

import ElementPlus from 'element-plus';
import { ElMessage } from 'element-plus';
import '/@/theme/index.scss';

import VueGridLayout from 'vue-grid-layout';

import scUploadFile from '/@/components/scUpload/file.vue';
import scUpload from '/@/components/scUpload/index.vue';
import scCron from '/@/components/scCron/index.vue';

import { setupAutoLogout } from './utils/autoLogout';
import { startRefreshingToken } from './utils/tokenRefresher';
import { setupStorageListener } from './utils/storageListener';
import appSettings from './config/index.js';

import VxePCUI from 'vxe-pc-ui';
import 'vxe-pc-ui/lib/style.css';
import VxeUITable from 'vxe-table';
import { VxeUI } from 'vxe-table';
import 'xe-utils';
import 'vxe-table/lib/style.css';
import VxeUIPluginRenderElement from '@vxe-ui/plugin-render-element';
import '@vxe-ui/plugin-render-element/dist/style.css';
import zhCN from '/@/vxetable/lang/zh-CN'
import enUS from '/@/vxetable/lang/en-US'

const app = createApp(App);

app.component('scUploadFile', scUploadFile);
app.component('scUpload', scUpload);
app.component('scCron', scCron);

directive(app);
other.elSvg(app);

// 设置自动退出
setupAutoLogout(router);
setupStorageListener(router);
startRefreshingToken();

VxePCUI.use(VxeUIPluginRenderElement);
// 注册语言
VxeUI.setI18n('zh-CN', zhCN)
VxeUI.setI18n('en-US', enUS)
VxeUI.setLanguage('en-US')

app.use(pinia).use(router).use(ElementPlus).use(i18n).use(VxePCUI).use(VxeUITable).use(VueGridLayout).mount('#app');

let messageInstance: { close: () => void } | null = null;

const resetMessage = (msg: any, type: any) => {
  if (messageInstance != null) {
    messageInstance.close();
  }
  messageInstance = ElMessage({ message: msg, type: type });
};

app.config.globalProperties.$appSettings = appSettings;
app.config.globalProperties.$message = resetMessage;
app.config.globalProperties.$handleError = handleError;
