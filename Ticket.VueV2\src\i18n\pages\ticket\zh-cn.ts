export default {
	ticketSearch: {
		//查询区域
		searchKeyPlaceholder: '请输入关键字',
	},
	ticketButtons: {
		//非按钮
		SubmitTicketSuccess: '提交工单成功',
		submitTicket: '提交工单',
		more: '更多',
		workflowRecords: '流程记录',
		attachments: '附件',
		clickToUpload: '添加附件',
		//按钮

		viewComments: '查看备注',

		newTicket: '创建工单',
		newScheduleTicket: '创建计划工单',
	},
	ticketLabels: {
		status: '工单状态',
		scheduledDate: '计划日期',
		priority: '优先级',
		category: '分类',
		customers: '客户',
		projects: '项目',
		caseNumber: '工单号',
		ticketSubject: '工单标题',
		createDate: '创建日期',
		modifiedAt: '修改日期',
		to: '至',
		createSubTicket: '创建子工单',
		assignedtoMe: '分配给我',
		contentPlaceholder: '请在备注字段中输入任何其他信息、相关详细信息，或分享您对工单的想法和反馈。',
		uploadTips: '上传文件不得大于10M',
		ticketContentType: '工单类型',
	},
	ticketFields: {
		//table 列名
		status: '状态',
		caseNumber: '单号',
		assignTo: '指定人',
		delegateTo: '代理人',
		priority: '优先级',
		category: '分类',
		company: '公司名',
		project: '项目名',
		attachments: '有无邮件',
		ticketType: '工单类型',
		openedBy: '开启人',
		solvedBy: '解决人',
		createdDate: '创建日期',
		createdBy: '创建人',
		modifiedDate: '修改日期',
		modifiedBy: '修改人',
		ticketFrom: '工单来源',
		openDate: '开启日期',
		closeDate: '关闭日期',
		//创建工单页面
		information: '工单信息',
		internal: '内部',
		assignee: '指定人',
		telephone: '手机号',
		customerEmail: '客户邮箱',
		customer: '项目',
		lastName: '姓',
		firstName: '名',
		ticketSubject: '工单标题',
		subject: '标题',
		attachment: '附件',
		content: '备注',
		subjectType: '展现方式',

		submitTicketError: '您选择的类别尚未分配给任何人，如有任何问题，请联系系统管理员',
		submitTicketOtherError: '提交工单出现了一些问题，请联系管理员',
		commentSavedSuccessfully: '备注保存成功',
		scheduledDate: '计划日期',
		operatorName: '操作人',

		parentTicket: '父工单',
		grantAccess: '指定访问',
		privateTicket: '私有工单',
		Emails: '邮箱',
		BusinessUnit: '业务单元',
	},
};
