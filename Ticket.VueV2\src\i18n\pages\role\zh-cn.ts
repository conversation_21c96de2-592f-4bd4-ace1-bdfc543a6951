﻿export default {
	roleSearch: {
		//查询区域
		searchKeyPlaceholder: '请输入关键字',
	},
	roleButtons: {
		//非按钮
	},
	roleFields: {
		//table 列名
		id: 'Id',
		code: '编码',
		name: '名称',
		description: '备注',
		orderSort: '排序',
		dids: '',
		authorityScope: ' ',
		enabled: '启用',
		createId: '',
		createBy: '',
		createTime: '创建时间',
		modifyId: '',
		modifyBy: ' ',
		modifyTime: ' ',
		permission: '权限',
		customRole: '自定义角色',
		systemRole: '系统角色',
        allRole:'所有角色',
        privilegeGroupMember:'权限组成员',
        PrivilegeConfiguration:'权限配置',
		createRole:'创建角色',
		editRole:'创建角色'
	},
	permissionTableFields: {
		name: '模块名称',
		visit: '访问页面',
		permissions: '权限',
	},
};
