﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class workflowExecutionLogRecordsApi extends BaseApi {

    WorkflowExecution(data: any) {
        return request({
            url: this.baseurl + 'WorkflowExecution',
            method: 'post',
            data,
        });
    }

    WorkflowUrge(data: any) {
        return request({
            url: this.baseurl + 'WorkflowUrge',
            method: 'post',
            data,
        });
    }

    GetWorkflowNodeLogs(params: any) {
		return request({
			url: this.baseurl + 'GetWorkflowNodeLogs',
			method: 'get',
			params,
		});
	}
}

export default new workflowExecutionLogRecordsApi('/api/workflowExecutionLogRecords/','id');




                        
        
        