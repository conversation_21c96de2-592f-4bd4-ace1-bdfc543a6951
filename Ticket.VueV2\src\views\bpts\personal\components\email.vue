
<template>
	<div class="email-content">
		<el-card>
			<template #header>Change My Email</template>
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-position="top" label-width="130px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item :label="$t('message.profileFields.Email')" class="form-control">
							<el-input v-model="ruleForm.email" :readonly="true" class="readonly" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item :label="$t('message.profileFields.NewEmail')" prop="newEmail" class="form-control">
							<el-input v-model="ruleForm.newEmail" style="width: 500px" :placeholder="$t('message.profilePlaceholder.newEmail')" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item class="el-form-item_msg">
							<ul class="message-list">
								<li v-for="message in messages" :key="message.msg" :class="`message-item ${message.type === 'error' ? 'error' : ''}`">
									<span class="dot"></span> {{ message.msg }}
								</li>
							</ul>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item class="el-form-item_btn">
							<el-button :loading="saveLoading" color="#626aef" type="primary" @click="onSubmit">{{
								$t('message.profileButtons.Changemail')
							}}</el-button>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent, watch, getCurrentInstance } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { checkEmail } from '/@/utils/toolsValidate';
import { useI18n } from 'vue-i18n';
import userApi from '/@/api/user';
import { IApiResultMessage } from '/@/types/models';

export default defineComponent({
	name: 'email',
	props: {
		info: Object,
	},
	setup(props, context) {
		const { t } = useI18n();
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			messages: [] as IApiResultMessage[], // 初始为空数组
			saveLoading: false,
			projectsOptions: [],
			timeZoneOptions: [],
			statusOptions: [],
			ruleForm: {
				id: 0,
				email: '',
				newEmail: '',
			},
			rules: {
				newEmail: [
					{ required: true, message: t('message.profilePlaceholder.newEmail'), trigger: 'blur' },
					{
						validator: checkEmail,
						min: 9,
						max: 18,
						message: t('message.validates.email'),
						trigger: 'blur',
					},
				],
			},
		});

		watch(
			() => props.info,
			(newVal: any, oldVal) => {
				state.ruleForm.email = newVal.email;
				state.ruleForm.id = newVal.id;
			}
		);

		watch(
			() => state.ruleForm,
			(newRuleForm, oldRuleForm) => {
				state.messages = [];
			},
			{ deep: true }
		);

		const onSubmit = () => {
			state.messages = [];
			proxy.$refs.ruleFormRef.validate((valid: any, invalidFields: any) => {
				if (!valid) {
					Object.keys(invalidFields).forEach((field) => {
						invalidFields[field].forEach((error: any) => {
							let msg = error.message;
							const errorMessage = msg || `${field} is invalid`;
							addMessage(errorMessage, 'error');
						});
					});
					return;
				}

				// 将两个邮箱地址都转换为小写，然后进行比较
				if (state.ruleForm.email.toLowerCase() === state.ruleForm.newEmail.toLowerCase()) {
					addMessage('The new email address must be different from the current one.');
				}

				if (state.messages.length > 0) return;

				var obj = {
					id: state.ruleForm.id,
					email: state.ruleForm.newEmail,
				};

				state.saveLoading = true;

				userApi
					.UpdateUserEmail(obj)
					.then((rs) => {
						ElMessage.success('Succeed');
						context.emit('fetchData', obj.id);
						state.ruleForm.newEmail = '';
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};
		const addMessage = (msg: string, type: 'success' | 'error' | 'info' = 'error') => {
			state.messages.push({ msg, type });
		};
		// 页面加载时
		onMounted(() => {
			if (props.info) {
				state.ruleForm.email = props.info.email;
				state.ruleForm.id = props.info.id;
			}
		});
		return {
			onSubmit,
			...toRefs(state),
		};
	},
});
</script>


<style scoped>
.lpx-content {
	max-width: 1280px !important;
	padding: 1.25em 2em 3em;
	margin: 0 auto;
}

.lpx-avatar-img-lg {
	height: 144px;
	width: 144px;
	border-radius: 144px;
}
.mb-0 {
	margin-bottom: 0 !important;
}
.mb-3 {
	margin-bottom: 1.5rem !important;
}

.mt-2 {
	margin-top: 0.75rem !important;
}

.pb-3 {
	padding-bottom: 1.5rem !important;
}

.card .card-body img {
	max-width: 100%;
}

.text-center {
	text-align: center !important;
}

.border-bottom {
	border-bottom: 1px solid #e8eef3;
}

.card .card-title {
	font-size: 1.25em;
	font-weight: 500;
	color: #062a44;
}

.text-muted {
	--bs-text-opacity: 1;
	color: #325168 !important;
	font-size: 12px;
}

.text-muted {
	opacity: 0.8;
}

.fw-normal {
	font-weight: 400 !important;
}
h5,
.h5 {
	font-size: 13px !important;
}

.menu_btn_wrapper {
	display: flex;
	flex-direction: column;
}

.menu_btn {
	width: 100% !important;
}

.button_container {
	margin-bottom: 10px;
}
</style>

                        
        
        