﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup Label="Globals">
	  <SccProjectName>SAK</SccProjectName>
	  <SccProvider>SAK</SccProvider>
	  <SccAuxPath>SAK</SccAuxPath>
	  <SccLocalPath>SAK</SccLocalPath>
	</PropertyGroup>

	<PropertyGroup>

		<OutputType>Exe</OutputType>

		<TargetFramework>net6.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<AspNetCoreHostingModel>OutOfProcess</AspNetCoreHostingModel>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<EnableUnsafeBinaryFormatterSerialization>true</EnableUnsafeBinaryFormatterSerialization>
		<IsTransformWebConfigDisabled>true</IsTransformWebConfigDisabled>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>..\YunLanCrm.Api\YunLanCrm.xml</DocumentationFile>
		<NoWarn>1701;1702;1591</NoWarn>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<DocumentationFile>..\YunLanCrm\YunLanCrm.xml</DocumentationFile> 
		<NoWarn>1701;1702;1591</NoWarn>
	</PropertyGroup>
	<PropertyGroup>
		<ServerGarbageCollection>false</ServerGarbageCollection>
	</PropertyGroup>
	<ItemGroup>
		<Compile Remove="2\**" />
		<Compile Remove="CaseManagementNetCore.Vue\**" />
		<Compile Remove="ElsaWorkflows\InvoiceIndexing\**" />
		<Compile Remove="Extensions\**" />
		<Compile Remove="Hubs\**" />
		<Compile Remove="log\**" />
		<Compile Remove="Middlewares\**" />
		<Compile Remove="upload\**" />
		<Compile Remove="wwwroot\**" />
		<Content Remove="2\**" />
		<Content Remove="CaseManagementNetCore.Vue\**" />
		<Content Remove="ElsaWorkflows\InvoiceIndexing\**" />
		<Content Remove="Extensions\**" />
		<Content Remove="Hubs\**" />
		<Content Remove="log\**" />
		<Content Remove="Middlewares\**" />
		<Content Remove="upload\**" />
		<Content Remove="wwwroot\**" />
		<EmbeddedResource Remove="2\**" />
		<EmbeddedResource Remove="CaseManagementNetCore.Vue\**" />
		<EmbeddedResource Remove="ElsaWorkflows\InvoiceIndexing\**" />
		<EmbeddedResource Remove="Extensions\**" />
		<EmbeddedResource Remove="Hubs\**" />
		<EmbeddedResource Remove="log\**" />
		<EmbeddedResource Remove="Middlewares\**" />
		<EmbeddedResource Remove="upload\**" />
		<EmbeddedResource Remove="wwwroot\**" />
		<None Remove="2\**" />
		<None Remove="CaseManagementNetCore.Vue\**" />
		<None Remove="ElsaWorkflows\InvoiceIndexing\**" />
		<None Remove="Extensions\**" />
		<None Remove="Hubs\**" />
		<None Remove="log\**" />
		<None Remove="Middlewares\**" />
		<None Remove="upload\**" />
		<None Remove="wwwroot\**" />
	</ItemGroup>
	<ItemGroup>
	  <Compile Remove="Controllers\AdvertisementController.cs" />
	  <Compile Remove="Controllers\ApImagesController.cs" />
	  <Compile Remove="Controllers\ApImagesOriginalController.cs" />
	  <Compile Remove="Controllers\GuestbookController.cs" />
	  <Compile Remove="Controllers\IndustryController.cs" />
	  <Compile Remove="Controllers\InvoiceController.cs" />
	  <Compile Remove="Controllers\NotifyController.cs" />
	  <Compile Remove="Controllers\PasswordLibController.cs" />
	  <Compile Remove="Controllers\PositionController.cs" />
	  <Compile Remove="Controllers\RegionController.cs" />
	  <Compile Remove="Controllers\RequisitionsController.cs" />
	  <Compile Remove="Controllers\TaskLogController.cs" />
	  <Compile Remove="Controllers\TasksController.cs" />
	  <Compile Remove="Controllers\TestTicketController.cs" />
	  <Compile Remove="Controllers\UserAuditController.cs" />
	  <Compile Remove="Controllers\VendorController.cs" />
	  <Compile Remove="ElsaWorkflows\Activities\ApprovedBak.cs" />
	  <Compile Remove="ElsaWorkflows\Activities\BaseActivity.cs" />
	  <Compile Remove="ElsaWorkflows\Activities\OperationActivity.cs" />
	  <Compile Remove="ElsaWorkflows\InvoiceApprovalWorkflow.cs" />
	  <Compile Remove="ElsaWorkflows\PurchaseOrderWorkflow.cs" />
	</ItemGroup>
	<ItemGroup>
	  <Content Remove="appsettings.json" />
	  <Content Remove="appsettings1.apollo.json" />
	  <Content Remove="appsettings1.Development.json" />
	  <Content Remove="appsettings1.json" />
	  <Content Remove="web.config" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="index.html" />
		<None Remove="Install.bat" />
		<None Remove="Uninstall.bat" />
	</ItemGroup>

	<ItemGroup>
	  <Content Include="Install.bat">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Include="Uninstall.bat">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Include="wwwroot\CorsPost.html" />
	  <Content Include="wwwroot\index.html" />
	  <Content Include="wwwroot\JMeterTest.png" />
	  <Content Include="wwwroot\laozhangisphigood.jpg" />
	  <Content Include="wwwroot\logo.jpg.jpg" />
	  <Content Include="wwwroot\logo.png" />
	  <Content Include="wwwroot\logo.png.png" />
	  <Content Include="wwwroot\logocore.png" />
	  <Content Include="wwwroot\logo\favicon-32x32.png" />
	  <Content Include="wwwroot\menu.json" />
	  <Content Include="wwwroot\MVP_Logo_Horizontal_Preferred_Cyan300_CMYK_72ppi.png" />
	  <Content Include="wwwroot\swg-login.html" />
	  <Content Include="wwwroot\ui\css\app.77b8d2be.css" />
	  <Content Include="wwwroot\ui\css\chunk-47211100.6b9a8428.css" />
	  <Content Include="wwwroot\ui\css\chunk-4b6066be.57cc0d2f.css" />
	  <Content Include="wwwroot\ui\css\chunk-7287e918.735a054c.css" />
	  <Content Include="wwwroot\ui\css\chunk-789b0e7e.2afc78bb.css" />
	  <Content Include="wwwroot\ui\css\chunk-c673e236.597cf4d0.css" />
	  <Content Include="wwwroot\ui\css\chunk-c75b8e6e.40b63f23.css" />
	  <Content Include="wwwroot\ui\css\chunk-d726e0f8.ac918284.css" />
	  <Content Include="wwwroot\ui\css\chunk-ef28925c.f5aa9d10.css" />
	  <Content Include="wwwroot\ui\css\chunk-vendors.9b6f85aa.css" />
	  <Content Include="wwwroot\ui\favicon.ico" />
	  <Content Include="wwwroot\ui\fonts\element-icons.535877f5.woff" />
	  <Content Include="wwwroot\ui\fonts\element-icons.732389de.ttf" />
	  <Content Include="wwwroot\ui\fonts\fontawesome-webfont.674f50d2.eot" />
	  <Content Include="wwwroot\ui\fonts\fontawesome-webfont.b06871f2.ttf" />
	  <Content Include="wwwroot\ui\fonts\fontawesome-webfont.fee66e71.woff" />
	  <Content Include="wwwroot\ui\img\loginbck.ed4cec20.png" />
	  <Content Include="wwwroot\ui\index.html" />
	  <Content Include="wwwroot\ui\static\images\7.png" />
	  <Content Include="wwwroot\workflow\json.json" />
	  <Content Include="wwwroot\workflow\json1.json" />
	  <Content Include="wwwroot\workflow\json2.json" />
	  <Content Include="wwwroot\workflow\workflow.json" />
	</ItemGroup>

	<ItemGroup>
		
		<PackageReference Include="Furion" Version="3.1.3" />
		<PackageReference Include="Furion.Extras.Logging.Serilog" Version="3.2.0" />
		<PackageReference Include="HtmlAgilityPack" Version="1.11.72" />
		<PackageReference Include="log4mongo-netcore" Version="3.2.0" />
		<PackageReference Include="Mapster" Version="7.3.0" />
		<PackageReference Include="MicroKnights.Log4NetAdoNetAppender" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.ResponseCompression" Version="2.2.0" />
		<PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="6.0.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.9.10" />
		<PackageReference Include="Serilog.Sinks.Async" Version="1.5.0" />
		<PackageReference Include="Serilog.Sinks.Map" Version="1.0.2" />
		<PackageReference Include="SkyAPM.Agent.AspNetCore" Version="1.3.0" />
		<PackageReference Include="System.Data.SqlClient" Version="4.8.3" />
		<PackageReference Include="System.IO.FileSystem" Version="4.3.0" />
		<PackageReference Include="System.Text.Encoding.CodePages" Version="5.0.0" />
		
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\YunLanCrm.Extensions\YunLanCrm.Extensions.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Include="Pages\_Host.cshtml" />
		<None Include="Pages\_ViewImports.cshtml" />
		<None Include="web.config">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Include="wwwroot\BlogCore.Data.json\BlogArticle.tsv" />
		<None Include="wwwroot\BlogCore.Data.json\Department.tsv" />
		<None Include="wwwroot\BlogCore.Data.json\Modules.tsv" />
		<None Include="wwwroot\BlogCore.Data.json\Permission.tsv" />
		<None Include="wwwroot\BlogCore.Data.json\Role.tsv" />
		<None Include="wwwroot\BlogCore.Data.json\RoleModulePermission.tsv" />
		<None Include="wwwroot\BlogCore.Data.json\sysUserInfo.tsv" />
		<None Include="wwwroot\BlogCore.Data.json\TasksQz.tsv" />
		<None Include="wwwroot\BlogCore.Data.json\Topic.tsv" />
		<None Include="wwwroot\BlogCore.Data.json\TopicDetail.tsv" />
		<None Include="wwwroot\BlogCore.Data.json\UserRole.tsv" />
		<None Include="wwwroot\js\jquery-3.3.1.min.js" />
		<None Include="wwwroot\NoInterAutofacIOC.rar" />
		<None Include="wwwroot\ui.zip" />
		<None Include="wwwroot\ui\fonts\fontawesome-webfont.af7ae505.woff2" />
		<None Include="wwwroot\ui\img\fontawesome-webfont.912ec66d.svg" />
		<None Include="wwwroot\ui\js\app.d6fdef78.js" />
		<None Include="wwwroot\ui\js\app.d6fdef78.js.map" />
		<None Include="wwwroot\ui\js\chunk-23e41f57.ae5fabaa.js" />
		<None Include="wwwroot\ui\js\chunk-23e41f57.ae5fabaa.js.map" />
		<None Include="wwwroot\ui\js\chunk-276b085c.39103cdf.js" />
		<None Include="wwwroot\ui\js\chunk-276b085c.39103cdf.js.map" />
		<None Include="wwwroot\ui\js\chunk-2d0a4854.aee50383.js" />
		<None Include="wwwroot\ui\js\chunk-2d0a4854.aee50383.js.map" />
		<None Include="wwwroot\ui\js\chunk-2d0c0c66.4faa5607.js" />
		<None Include="wwwroot\ui\js\chunk-2d0c0c66.4faa5607.js.map" />
		<None Include="wwwroot\ui\js\chunk-2d0c4aa3.703d6172.js" />
		<None Include="wwwroot\ui\js\chunk-2d0c4aa3.703d6172.js.map" />
		<None Include="wwwroot\ui\js\chunk-2d0cf4f3.4034e115.js" />
		<None Include="wwwroot\ui\js\chunk-2d0cf4f3.4034e115.js.map" />
		<None Include="wwwroot\ui\js\chunk-2d0d2f25.359b78e4.js" />
		<None Include="wwwroot\ui\js\chunk-2d0d2f25.359b78e4.js.map" />
		<None Include="wwwroot\ui\js\chunk-2d0da5bf.c22ad0ee.js" />
		<None Include="wwwroot\ui\js\chunk-2d0da5bf.c22ad0ee.js.map" />
		<None Include="wwwroot\ui\js\chunk-2d213196.5c2d76ce.js" />
		<None Include="wwwroot\ui\js\chunk-2d213196.5c2d76ce.js.map" />
		<None Include="wwwroot\ui\js\chunk-2d21f214.ec5ee5a8.js" />
		<None Include="wwwroot\ui\js\chunk-2d21f214.ec5ee5a8.js.map" />
		<None Include="wwwroot\ui\js\chunk-2d229214.cfe33fe9.js" />
		<None Include="wwwroot\ui\js\chunk-2d229214.cfe33fe9.js.map" />
		<None Include="wwwroot\ui\js\chunk-2d22d746.bc86ccfd.js" />
		<None Include="wwwroot\ui\js\chunk-2d22d746.bc86ccfd.js.map" />
		<None Include="wwwroot\ui\js\chunk-40df6ae2.e79ba86d.js" />
		<None Include="wwwroot\ui\js\chunk-40df6ae2.e79ba86d.js.map" />
		<None Include="wwwroot\ui\js\chunk-47211100.16761898.js" />
		<None Include="wwwroot\ui\js\chunk-47211100.16761898.js.map" />
		<None Include="wwwroot\ui\js\chunk-479d738e.57cdb42a.js" />
		<None Include="wwwroot\ui\js\chunk-479d738e.57cdb42a.js.map" />
		<None Include="wwwroot\ui\js\chunk-47dd42da.97513c3e.js" />
		<None Include="wwwroot\ui\js\chunk-47dd42da.97513c3e.js.map" />
		<None Include="wwwroot\ui\js\chunk-4b6066be.f63d0f19.js" />
		<None Include="wwwroot\ui\js\chunk-4b6066be.f63d0f19.js.map" />
		<None Include="wwwroot\ui\js\chunk-6e83591c.a520c082.js" />
		<None Include="wwwroot\ui\js\chunk-6e83591c.a520c082.js.map" />
		<None Include="wwwroot\ui\js\chunk-6f1c3bea.5a9acc22.js" />
		<None Include="wwwroot\ui\js\chunk-6f1c3bea.5a9acc22.js.map" />
		<None Include="wwwroot\ui\js\chunk-7287e918.7e428c29.js" />
		<None Include="wwwroot\ui\js\chunk-7287e918.7e428c29.js.map" />
		<None Include="wwwroot\ui\js\chunk-735deb8e.2bbe62a6.js" />
		<None Include="wwwroot\ui\js\chunk-735deb8e.2bbe62a6.js.map" />
		<None Include="wwwroot\ui\js\chunk-770e833a.0890b50d.js" />
		<None Include="wwwroot\ui\js\chunk-770e833a.0890b50d.js.map" />
		<None Include="wwwroot\ui\js\chunk-77279526.e54ae03e.js" />
		<None Include="wwwroot\ui\js\chunk-77279526.e54ae03e.js.map" />
		<None Include="wwwroot\ui\js\chunk-789b0e7e.df774071.js" />
		<None Include="wwwroot\ui\js\chunk-789b0e7e.df774071.js.map" />
		<None Include="wwwroot\ui\js\chunk-bf843d8a.ed731235.js" />
		<None Include="wwwroot\ui\js\chunk-bf843d8a.ed731235.js.map" />
		<None Include="wwwroot\ui\js\chunk-c5ac0cca.605768e7.js" />
		<None Include="wwwroot\ui\js\chunk-c5ac0cca.605768e7.js.map" />
		<None Include="wwwroot\ui\js\chunk-c673e236.156eaf15.js" />
		<None Include="wwwroot\ui\js\chunk-c673e236.156eaf15.js.map" />
		<None Include="wwwroot\ui\js\chunk-c75b8e6e.73103030.js" />
		<None Include="wwwroot\ui\js\chunk-c75b8e6e.73103030.js.map" />
		<None Include="wwwroot\ui\js\chunk-cae4df82.7e75b1da.js" />
		<None Include="wwwroot\ui\js\chunk-cae4df82.7e75b1da.js.map" />
		<None Include="wwwroot\ui\js\chunk-d726e0f8.c8fe5894.js" />
		<None Include="wwwroot\ui\js\chunk-d726e0f8.c8fe5894.js.map" />
		<None Include="wwwroot\ui\js\chunk-ef28925c.a547d73e.js" />
		<None Include="wwwroot\ui\js\chunk-ef28925c.a547d73e.js.map" />
		<None Include="wwwroot\ui\js\chunk-vendors.60d435c3.js" />
		<None Include="wwwroot\ui\js\chunk-vendors.60d435c3.js.map" />
		<None Include="wwwroot\web.config">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="index.html" />
	</ItemGroup>

	<ItemGroup>
		<None Update="Dockerfile">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="StopContainerImg.sh">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="PendingProcess\" />
	</ItemGroup>

	<ProjectExtensions>
		<VisualStudio><UserProperties /></VisualStudio>
	</ProjectExtensions>

</Project>
