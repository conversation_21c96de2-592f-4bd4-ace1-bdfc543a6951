﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmProjects;
using YunLanCrm.Common.Helper;
using YunLanCrm.Dto.CmGroups;
using YunLanCrm.Services;
using YunLanCrm.IRepositories;
using Org.BouncyCastle.Ocsp;
using YunLanCrm.Dto.DictItem;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmProjectsController : ControllerBase
    {
        private readonly ILogger<CmProjectsInfo> _logger;
        private readonly ICmProjectsService _cmProjectsService;
        private readonly ICmUserCompanyService _cmUserCompanyService;
        private readonly ICmProjectsRepository _cmProjectsRepository;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmProjectsService"></param>
        /// <param name="cmUserCompanyService"></param>
        /// <param name="logger"></param>
        public CmProjectsController(ICmProjectsService cmProjectsService, ILogger<CmProjectsInfo> logger, ICmUserCompanyService cmUserCompanyService, ICmProjectsRepository cmProjectsRepository)
        {
            _logger = logger;
            _cmProjectsService = cmProjectsService;
            _cmUserCompanyService = cmUserCompanyService;
            _cmProjectsRepository = cmProjectsRepository;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CmProjectsAddOrUpdateDto req)
        {
            return await _cmProjectsService.AddCmProjects(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmProjectsAddOrUpdateDto req)
        {
            return await _cmProjectsService.UpdateCmProjects(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("UpdateBusinessUnit")]
        public async Task<bool> UpdateBusinessUnit(DictItemValueDto req)
        {
            return await _cmProjectsService.UpdateCmProjectsBusinessUnit(req);
        }
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            bool hasExists = await _cmUserCompanyService.HasCurrenlyInUseByProjectId(id);

            int result = 0;

            if (!hasExists)
            {
                var oldProject = await _cmProjectsRepository.Db.Queryable<CmProjectsInfo>()
                .Where(a => a.IsDeleted == false && a.Id == id).ToListAsync();

                if (oldProject.Count > 0)
                {
                    await _cmProjectsRepository.Db.Updateable<OrganizationInfo>()
                        .SetColumns(a => a.IsDeleted == true)
                        .SetColumns(a => a.UpdateAt == DateTime.Now)
                        .Where(a => a.OrgId == oldProject[0].OrgId).ExecuteCommandAsync();
                }

                result = await _cmProjectsService.Delete(a => a.Id == id);
            }
            else
            {
                throw new Exception("You're deleting a record that has been applied to a ticket or user,please click 'Ok' to cancel this action");
            }

            return result > 0;
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _cmProjectsService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }

        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmProjectsDto> Get(long id)
        {
            return await _cmProjectsService.QueryInfo<CmProjectsDto>(a => a.Id == id);
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmProjectsDto>> GetList([FromQuery] CmProjectsListQueryDto req)
        {
            return await _cmProjectsService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="idType"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}/{idType}")]
        public async Task<CmProjectsDetailDto> Detail(long id, string idType)
        {
            return await _cmProjectsService.Detail(id, idType);
        }

        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmProjectsDetailDto>> DetailList([FromQuery] CmProjectsListQueryDto req)
        {
            return await _cmProjectsService.DetailList(req);
        }

        /// <summary>
        /// 获取一个GetProjectID
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [HttpGet("GetProjectID/{code}")]
        public async Task<string> GetProjectID(string code)
        {
            return await _cmProjectsService.GetProjectID(code);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmProjectsDetailDto>> Query([FromQuery] CmProjectsPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmProjectsDetailDto>> QueryPost(CmProjectsPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmProjectsDetailDto>> PageQuery([FromQuery] CmProjectsPageQueryDto req)
        {
            return await _cmProjectsService.PageQueryView(req);
        }

        [HttpPost("Export")]
        public async Task<IActionResult> Export(CmProjectsPageQueryDto req)
        {
            //SystemLogRecord.createLog("Export22222: Export", "ExportExcel.txt", "TxtLog", true, true);

            IActionResult result = null;
            try
            {
                var isAllCheck = req.ischeckType == 0 ? true : false;

                result = await ExcelHelper.Export<Task<PageQueryResult<CmProjectsDetailDto>>>(_cmProjectsService,
                    nameof(ICmProjectsService), nameof(ICmProjectsService.PageQueryView), this, isAllCheck, req);
            }
            catch (Exception ex)
            {
                SystemLogRecord.createLog("Export22222 error: " + ex.Message, "ExportExcel.txt", "TxtLog", true, true);
            }

            return result;
        }
    }
}