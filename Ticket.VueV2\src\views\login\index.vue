<template>
	<div class="login-container flex">
		<div class="login-left">
			<div class="login-left-logo">
				<!-- <img :src="logoMini" /> -->
				<!-- <img src="/img/Logo_DocXtract.png?v=1.1.1" /> -->
				<img v-if="isLeftImageVisible" :src="getSiteInfo.logoOutLeft" :width="state.info.logoOutLeftWidth"
					:height="state.info.logoOutLeftHeight" />
				<div class="login-left-logo-text">
					<!-- <span>{{ getThemeConfig.globalViceTitle }}</span>
					<span class="login-left-logo-text-msg">{{ getThemeConfig.globalViceTitleMsg }}</span> -->
					<span>{{ getSiteInfo.demo }}</span>
				</div>
			</div>
			<div class="login-left-img">
				&nbsp;
				<!-- <img :src="loginMain" /> -->
			</div>

			<img :src="loginBg" class="login-left-waves" />
		</div>
		<div class="login-right flex">
			<div class="login-right-warp flex-margin">
				<span class="login-right-warp-one"></span>
				<span class="login-right-warp-two"></span>
				<div class="login-right-warp-mian">
					<!-- <div class="login-right-warp-main-title">{{ getThemeConfig.globalTitle }} 欢迎您！</div> -->
					<div class="login-right-warp-main-title">Log into DocXtract</div>
					<div class="login-right-warp-main-name">{{ getSiteInfo.systemName }}</div>
					<div class="login-right-warp-main-form">
						<div v-if="!state.isScan">
							<Account />
						</div>
						<!-- <Scan v-if="state.isScan" />
						<div class="login-content-main-sacn" @click="state.isScan = !state.isScan">
							<i class="iconfont" :class="state.isScan ? 'icon-diannao1' : 'icon-barcode-qr'"></i>
							<div class="login-content-main-sacn-delta"></div>
						</div> -->
					</div>
				</div>
			</div>
		</div>
		<!-- <div class="topRightLogo">WWE Dashboard</div> -->
		<div class="topRightLogo">
			<!-- <img src="/img/wweLogo.png?v=1.1.1" width="300px" /> -->
			<img v-if="isRightImageVisible" :src="getSiteInfo.logoOutRigth" :width="getSiteInfo.logoOutRigthWidth"
				:height="getSiteInfo.logoOutRigthHeight" />
		</div>

		<div class="login_adv__bottom">
			<p class="p1">
				Copyright © <span id="ctlLoginView_ctlCopyright_lblYear">{{ new Date().getFullYear() }}</span>&nbsp;
				<a id="ctlLoginView_ctlCopyright_lnkTGS" class="copyRightLink" href="http://www.bptsllc.com"
					target="_blank">BPTS LLC</a>
			</p>

			<p>{{ getSiteInfo.copyright }}</p>
		</div>
	</div>
</template>

<script setup lang="ts" name="loginIndex">
import { defineAsyncComponent, onMounted, reactive, computed, getCurrentInstance, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { NextLoading } from '/@/utils/loading';
// import logoMini from '/@/assets/logo-mini.svg';
// import loginMain from '/@/assets/login-main.svg';
import loginBg from '/@/assets/login-bg.svg';
import configApi from '/@/api/config/index';
import { Siteinfo } from '/@/types/siteConfig';

import loginApi from '/@/api/login';

import { useRoute, useRouter } from 'vue-router';

import { Session, Local } from '/@/utils/storage';

import { ElMessage, type FormInstance, ElLoading, MessageParamsWithType } from 'element-plus';

const route = useRoute();

// 引入组件
const Account = defineAsyncComponent(() => import('/@/views/login/component/account.vue'));
const Mobile = defineAsyncComponent(() => import('/@/views/login/component/mobile.vue'));
const Scan = defineAsyncComponent(() => import('/@/views/login/component/scan.vue'));

const { proxy } = getCurrentInstance() as any;

//webSystemUrl.ENV = 'production'

// 定义变量内容

const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const state = reactive({
	tabsActiveName: 'account',
	isScan: false,
	info: {} as Siteinfo,
});

// 获取布局配置信息
const getThemeConfig = computed(() => {
	return themeConfig.value;
});
const getSiteInfo = computed(() => {
	return state.info;
});
// 页面加载时
onMounted(async () => {
	NextLoading.done();
	configApi.GetAppConfig().then((rs) => {
		state.info = rs.data;

		loadImage(state.info.logoOutLeft).then(img => {
			isLeftImageVisible.value = true
		}).catch(err => {
			isLeftImageVisible.value = false
		});

		loadImage(state.info.logoOutRigth).then(img => {
			isRightImageVisible.value = true
		}).catch(err => {
			isRightImageVisible.value = false
		});
	});

	const queryParams = route.query;

	if (queryParams?.bptsSSOLogin && queryParams?.bptsSSOLogin == '8c303c46-79d1-44cd-ade6-fd0b2c7bec18' && !queryParams?.messageType) {
		await SSOLogin();
	}
});


let loadingAutoLogin: any = null;

const startLoadingAutoLogin = () => {
	loadingAutoLogin = ElLoading.service({
		lock: true,
		text: 'Loading',
		background: 'rgba(0, 0, 0, 0.7)',
	});
};

const SSOLogin = async () => {
	Local.clearToken();;

	startLoadingAutoLogin();

	await loginApi
		.SSOLogin({ bptsSSOLogin: '8c303c46-79d1-44cd-ade6-fd0b2c7bec18' })
		.then((rs) => {
			window.location.href = rs.data.data;
		})
		.finally(() => {
			if (loadingAutoLogin != null) {
				loadingAutoLogin.close();
			}
		});
};


const isLeftImageVisible = ref(true)

const isRightImageVisible = ref(true)

const loadImage = (src: any) => {
	return new Promise((resolve, reject) => {
		let img = new Image();
		img.onload = () => resolve(img);
		img.onerror = () => reject(new Error('Image load failed'));
		img.src = src;
	});
}
</script>

<style scoped lang="scss">
.login-container {
	height: 100%;
	background: var(--el-color-white);

	.login-left {
		flex: 1;
		position: relative;
		background-color: rgba(211, 239, 255, 1);
		margin-right: 100px;

		.login-left-logo {
			display: flex;
			align-items: center;
			position: absolute;
			top: 50px;
			left: 80px;
			z-index: 1;
			animation: logoAnimation 0.3s ease;

			img {
				// width: 52px;
				height: 52px;
			}

			.login-left-logo-text {
				display: flex;
				flex-direction: column;

				span {
					margin-left: 10px;
					font-size: 28px;
					color: #f00606;
				}

				.login-left-logo-text-msg {
					font-size: 12px;
					color: #32a99e;
				}
			}
		}

		.login-left-img {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 100%;
			height: 52%;

			img {
				width: 100%;
				height: 100%;
				animation: error-num 0.6s ease;
			}
		}

		.login-left-waves {
			position: absolute;
			top: 0;
			right: -100px;
		}
	}

	.login-right {
		width: 700px;

		.login-right-warp {
			border: 1px solid var(--el-color-primary-light-3);
			border-radius: 3px;
			width: 500px;
			position: relative;
			overflow: hidden;
			background-color: var(--el-color-white);

			.login-right-warp-one,
			.login-right-warp-two {
				position: absolute;
				display: block;
				width: inherit;
				height: inherit;

				&::before,
				&::after {
					content: '';
					position: absolute;
					z-index: 1;
				}
			}

			.login-right-warp-one {
				&::before {
					filter: hue-rotate(0deg);
					top: 0px;
					left: 0;
					width: 100%;
					height: 3px;
					background: linear-gradient(90deg, transparent, var(--el-color-primary));
					animation: loginLeft 3s linear infinite;
				}

				&::after {
					filter: hue-rotate(60deg);
					top: -100%;
					right: 2px;
					width: 3px;
					height: 100%;
					background: linear-gradient(180deg, transparent, var(--el-color-primary));
					animation: loginTop 3s linear infinite;
					animation-delay: 0.7s;
				}
			}

			.login-right-warp-two {
				&::before {
					filter: hue-rotate(120deg);
					bottom: 2px;
					right: -100%;
					width: 100%;
					height: 3px;
					background: linear-gradient(270deg, transparent, var(--el-color-primary));
					animation: loginRight 3s linear infinite;
					animation-delay: 1.4s;
				}

				&::after {
					filter: hue-rotate(300deg);
					bottom: -100%;
					left: 0px;
					width: 3px;
					height: 100%;
					background: linear-gradient(360deg, transparent, var(--el-color-primary));
					animation: loginBottom 3s linear infinite;
					animation-delay: 2.1s;
				}
			}

			.login-right-warp-mian {
				display: flex;
				flex-direction: column;
				height: 100%;

				.login-right-warp-main-title {
					margin-top: 40px;
					width: 100%;
					font-size: 27px;
					text-align: center;
					//letter-spacing: 3px;
					animation: logoAnimation 0.3s ease;
					animation-delay: 0.3s;
					color: var(--el-color-info);
					padding-top: 10px;
					font-family: Arial, Verdana, Helvetica, sans-serif;
				}

				.login-right-warp-main-name {
					margin-top: 20px;
					width: 100%;
					line-height: 10px;
					font-size: 27px;
					text-align: center;
					//letter-spacing: 3px;
					animation: logoAnimation 0.3s ease;
					animation-delay: 0.3s;
					color: var(--el-text-color-primary);
					padding-bottom: 35px;
					font-weight: 550;
					font-family: Arial, Verdana, Helvetica, sans-serif;
				}

				.login-right-warp-main-form {
					flex: 1;
					padding: 0 50px 50px;

					.login-content-main-sacn {
						position: absolute;
						top: 0;
						right: 0;
						width: 50px;
						height: 50px;
						overflow: hidden;
						cursor: pointer;
						transition: all ease 0.3s;
						color: var(--el-color-primary);

						&-delta {
							position: absolute;
							width: 35px;
							height: 70px;
							z-index: 2;
							top: 2px;
							right: 21px;
							background: var(--el-color-white);
							transform: rotate(-45deg);
						}

						&:hover {
							opacity: 1;
							transition: all ease 0.3s;
							color: var(--el-color-primary) !important;
						}

						i {
							width: 47px;
							height: 50px;
							display: inline-block;
							font-size: 48px;
							position: absolute;
							right: 1px;
							top: 0px;
						}
					}
				}
			}
		}
	}

	.topRightLogo {
		position: fixed; // 修改为fixed
		top: 20px;
		right: 20px;
		padding: 10px;
		font-size: 40px;
		font-weight: bold;
		background: linear-gradient(90deg, #5ba7ee, #111011);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		font-style: italic;
		z-index: 1000; // 确保在最前
	}
}

.login_adv__bottom {
	display: block;
	position: absolute;
	left: 0px;
	right: 0px;
	bottom: 0px;
	// color: #b3b0b0;
	padding: 40px 20px;

	.p1 {
		padding-left: 200px;
		padding-bottom: 5px;
		display: inline-block;
		/* 根据内容宽度调整 */
	}

	// background-image: linear-gradient(transparent, #000);
	.copyRightLink {
		color: #2a72c5;
		text-decoration: none;
	}
}

/* 页面宽度小于576px
------------------------------- */
@media screen and (max-width: 1024px) {
	.login-container {
		.login_adv__bottom {
			text-align: center;

			.p1 {
				padding-left: 0px;
			}
		}
	}
}

@media screen and (max-width: 1370px) {
	.login-container {
		.login-right {
			.login-right-warp {
				.login-right-warp-mian {
					.login-right-warp-main-title {
						height: auto;
					}
				}
			}
		}
	}
}
</style>
