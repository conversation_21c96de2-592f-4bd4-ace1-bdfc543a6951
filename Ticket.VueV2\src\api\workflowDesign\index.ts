﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class workflowDesignApi extends BaseApi {

    WorkflowDesignSimples(params: any) {
        return request({
            url: this.baseurl + 'WorkflowDesignSimples',
            method: 'get',
            params,
        });
    }

}

export default new workflowDesignApi('/api/workflowDesign/','designId');




                        
        
        