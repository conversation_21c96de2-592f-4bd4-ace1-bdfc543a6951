<template>
	<div class="property-dialog">
		<UserSelect ref="userSelectRef" v-if="dataType === 'User'" @fetchData="fetchData" />
		<DepartmentSelect ref="departmentSelectRef" v-if="dataType === 'Department'" @fetchData="fetchData" />
		<RoleSelect ref="roleSelectRef" v-if="dataType === 'Role'" @fetchData="fetchData" />
		<!-- <PositionSelect ref="positionSelectRef" v-if="dataType === 'Position'" @fetchData="fetchData" /> -->
	</div>
</template>

<script lang="ts">
import { defineComponent, nextTick, onMounted, reactive, ref, toRefs } from 'vue';
import UserSelect from '/@/views/bpts/user/components/SelectUser.vue';
import DepartmentSelect from '/@/views/bpts/organization/components/departmentSelectDlg.vue';
import RoleSelect from '/@/views/bpts/role/components/roleSelectDlg.vue';
//import PositionSelect from '/@/views/bpts/position/components/positionSelectDlg.vue';

export default defineComponent({
	name: 'ApproverDialogs',
	components: { DepartmentSelect, RoleSelect, UserSelect },
	setup(props, context) {
		const state = reactive({
			dataType: '',
			multiple: false,
			initData: {},
		});
		const userSelectRef = ref();
		const departmentSelectRef = ref();
		const roleSelectRef = ref();
		const positionSelectRef = ref();

		const fetchData = (items: any) => {
			const arr = [] as any[];
			if (state.dataType === 'Department') {
				items.forEach((item: any) => {
					arr.push({ name: item.name, id: item.orgTypeId });
				});
			}
			if (state.dataType === 'Role') {
				items.forEach((item: any) => {
					arr.push({ name: item.name, id: item.id });
				});
			}
			if (state.dataType === 'Position') {
				items.forEach((item: any) => {
					arr.push({ name: item.name, id: item.id });
				});
			}
			if (state.dataType === 'User') {
				items.forEach((item: any) => {
					arr.push({ name: item.itemName, id: item.itemId });
				});
			}
			context.emit('fetchData', state.dataType, arr);
		};

		const setDialogParams = async (params: any) => {
			state.dataType = params.dataType;
			state.initData = params.data;
			state.multiple = params.multiple;
			await nextTick();
			if (state.dataType === 'User') userSelectRef.value.openDialog(params);
			if (state.dataType === 'Department') departmentSelectRef.value.openDialog(params);
			if (state.dataType === 'Role') roleSelectRef.value.openDialog(params);
			if (state.dataType === 'Position') positionSelectRef.value.openDialog(params);
		};

		onMounted(() => {
			//console.log('props11', props);
		});

		return { userSelectRef, positionSelectRef, departmentSelectRef, roleSelectRef, setDialogParams, fetchData, ...toRefs(state) };
	},
});
</script>
