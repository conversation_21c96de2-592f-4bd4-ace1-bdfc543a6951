﻿<template>
	<div class="company-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="769px">
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="90px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="orgId" prop="orgId">
							<el-input v-model="ruleForm.orgId" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="code" prop="code">
							<el-input v-model="ruleForm.code" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="name" prop="name">
							<el-input v-model="ruleForm.name" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="postCode" prop="postCode">
							<el-input v-model="ruleForm.postCode" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="address" prop="address">
							<el-input v-model="ruleForm.address" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="legalPerson" prop="legalPerson">
							<el-input v-model="ruleForm.legalPerson" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="scale" prop="scale">
							<el-input v-model="ruleForm.scale" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="linkMan" prop="linkMan">
							<el-input v-model="ruleForm.linkMan" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="phone" prop="phone">
							<el-input v-model="ruleForm.phone" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="email" prop="email">
							<el-input v-model="ruleForm.email" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="fax" prop="fax">
							<el-input v-model="ruleForm.fax" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="linkTel" prop="linkTel">
							<el-input v-model="ruleForm.linkTel" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="logo" prop="logo">
							<el-input v-model="ruleForm.logo" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="region" prop="region">
							<el-input v-model="ruleForm.region" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="regions" prop="regions">
							<el-input v-model="ruleForm.regions" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="sort" prop="sort">
							<el-input-number v-model="ruleForm.sort" :min="0" class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="status" prop="status">
							<el-input-number v-model="ruleForm.status" :min="0" class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="shortName" prop="shortName">
							<el-input v-model="ruleForm.shortName" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="source" prop="source">
							<el-input v-model="ruleForm.source" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="createAt" prop="createAt">
							<el-date-picker v-model="ruleForm.createAt" type="date" placeholder="Pick a day" class="w100"> </el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="updateAt" prop="updateAt">
							<el-date-picker v-model="ruleForm.updateAt" type="date" placeholder="Pick a day" class="w100"> </el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="industryId" prop="industryId">
							<el-input-number v-model="ruleForm.industryId" :min="0" class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="description" prop="description">
							<el-input v-model="ruleForm.Msg" type="textarea" placeholder="请输入备注" maxlength="150"></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="select">
							<el-cascader
								:options="deptData"
								:props="{ checkStrictly: true, value: 'deptName', label: 'deptName' }"
								placeholder="select"
								clearable
								class="w100"
								v-model="ruleForm.department"
							>
								<template #default="{ node, data }">
									<span>{{ data.deptName }}</span>
									<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
								</template>
							</el-cascader>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">Cannel</el-button>
					<el-button v-if="dialogParams.action === 'Create'" @click="onInitForm" size="small" type="danger">Reset</el-button>
					<el-button v-if="dialogParams.action === 'Edit'" @click="onDelete" type="danger" size="small">Delete</el-button>
					<el-button :loading="saveLoading" type="primary" @click="onSubmit" size="small">Save</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';

import companyApi from '/@/api/company';

interface DialogParams {
	action: string;
	companyId: number;
}

export default defineComponent({
	name: 'companyCreateOrEdit',
	components: {},
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			title: 'Create Company',
			isShowDialog: false,
			saveLoading: false,
			deleteLoading: false,
			dialogParams: {
				action: '',
				id: -1,
			},
			ruleForm: {
				companyId: 0, //自增id
				orgId: 0, //
				code: '', //编码
				name: '', //名称
				postCode: '', //邮编
				address: '', //注册地址
				legalPerson: '', //法人
				scale: '', //企业规模
				linkMan: '', //联系人
				phone: '', //电话
				email: '', //邮箱
				fax: '', //传真
				linkTel: '', //联系号码
				logo: '', //商户logo
				region: '', //商户所属区域
				regions: '', //完成的区域代码 用 > 隔开
				sort: 0, //排序
				status: 0, //状态 0 正常 1 未审核
				shortName: '', //助记名称 简称
				source: '', //来源
				createAt: new Date(), //创建时间
				updateAt: new Date(), //更新时间
				industryId: 0, //所属行业
				description: '', //商户备注
			},
			rules: {
				companyId: [{ required: true, message: 'Please input', trigger: 'blur' }],
				orgId: [{ required: true, message: 'Please input', trigger: 'blur' }],
				name: [{ required: true, message: 'Please input', trigger: 'blur' }],
				sort: [{ required: true, message: 'Please input', trigger: 'blur' }],
				status: [{ required: true, message: 'Please input', trigger: 'blur' }],
				createAt: [{ type: 'date', required: true, message: 'Pick date', trigger: 'change' }],
				updateAt: [{ type: 'date', required: true, message: 'Pick date', trigger: 'change' }],
				industryId: [{ required: true, message: 'Please input', trigger: 'blur' }],

				//type1: [{ type: 'array', required: true, message: '', trigger: 'change' }],
			},
		});
		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			state.dialogParams = parmas;

			onInitForm();

			if (parmas.action == 'Create') {
				state.title = 'Create company';
			} else if (parmas.action == 'Edit') {
				state.title = 'Edit Company';
				getData(parmas.companyId);
			} else {
				ElMessage.error('Parameter action cannot be empty.');
			}
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}
				var obj = state.ruleForm;
				state.saveLoading = true;
				companyApi
					.Save(obj)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};
		//根据id获取完整的信息
		const getData = (id: any) => {
			companyApi.GetByKey(id).then((rs) => {
				state.ruleForm = Object.assign({}, rs.data);
			});
		};
		//删除纪录
		const onDelete = () => {
			ElMessageBox.confirm(`Are you sure to delete this?`, 'Tips', {
				confirmButtonText: 'OK',
				cancelButtonText: 'No,Thanks',
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				state.deleteLoading = true;
				companyApi
					.DeleteByKey(state.ruleForm.companyId)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.deleteLoading = false;
					});
			});
		};
		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.ruleForm = {
				companyId: 0, //自增id
				orgId: 0, //
				code: '', //编码
				name: '', //名称
				postCode: '', //邮编
				address: '', //注册地址
				legalPerson: '', //法人
				scale: '', //企业规模
				linkMan: '', //联系人
				phone: '', //电话
				email: '', //邮箱
				fax: '', //传真
				linkTel: '', //联系号码
				logo: '', //商户logo
				region: '', //商户所属区域
				regions: '', //完成的区域代码 用 > 隔开
				sort: 0, //排序
				status: 0, //状态 0 正常 1 未审核
				shortName: '', //助记名称 简称
				source: '', //来源
				createAt: new Date(), //创建时间
				updateAt: new Date(), //更新时间
				industryId: 0, //所属行业
				description: '', //商户备注
			};
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			...toRefs(state),
		};
	},
});
</script>
