<?xml version="1.0"?>
<doc>
    <assembly>
        <name>YunLanCrm.Api</name>
    </assembly>
    <members>
        <member name="T:YunLanCrm.WebApi.Controllers.ApiController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiController.#ctor(YunLanCrm.IServices.IApiService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.ApiInfo},YunLanCrm.IServices.IMenuApiService,YunLanCrm.IServices.IRoleModulePermissionService)">
            <summary>
            
            </summary>
            <param name="apiService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiController.Tree(System.Nullable{System.Int64})">
            <summary>
            
            </summary>
            <param name="parentId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiController.Add(YunLanCrm.Dto.Api.ApiAddDto)">
            <summary>
            添加
            </summary> 
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiController.Update(YunLanCrm.Dto.Api.ApiEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiController.Delete(System.Int32)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiController.Get(System.Int32)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiController.Detail(System.Int32)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiController.List(YunLanCrm.Dto.Api.ApiListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiController.Query(YunLanCrm.Dto.Api.ApiPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiController.QueryPost(YunLanCrm.Dto.Api.ApiPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiController.PageQuery(YunLanCrm.Dto.Api.ApiPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.ApiKeyController">
            <summary>
            API Key管理控制器
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiKeyController.CreateApiKey(YunLanCrm.WebApi.Controllers.CreateApiKeyRequest)">
            <summary>
            创建新的API Key
            </summary>
            <param name="request">创建请求</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiKeyController.GetApiKeys">
            <summary>
            获取API Key列表
            </summary>
            <returns>API Key列表</returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiKeyController.EnableApiKey(System.Int64)">
            <summary>
            启用API Key
            </summary>
            <param name="id">API Key ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiKeyController.DisableApiKey(System.Int64)">
            <summary>
            禁用API Key
            </summary>
            <param name="id">API Key ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiKeyController.DeleteApiKey(System.Int64)">
            <summary>
            删除API Key
            </summary>
            <param name="id">API Key ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ApiKeyController.GetApiKeyUsage(System.Int64,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取API Key使用统计
            </summary>
            <param name="id">API Key ID</param>
            <param name="startDate">开始日期</param>
            <param name="endDate">结束日期</param>
            <returns>使用统计</returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CreateApiKeyRequest">
            <summary>
            创建API Key请求模型
            </summary>
        </member>
        <member name="P:YunLanCrm.WebApi.Controllers.CreateApiKeyRequest.Name">
            <summary>
            API Key名称
            </summary>
        </member>
        <member name="P:YunLanCrm.WebApi.Controllers.CreateApiKeyRequest.ExternalSystemName">
            <summary>
            外部系统名称
            </summary>
        </member>
        <member name="P:YunLanCrm.WebApi.Controllers.CreateApiKeyRequest.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:YunLanCrm.WebApi.Controllers.CreateApiKeyRequest.ExpiryDate">
            <summary>
            过期时间（可选）
            </summary>
        </member>
        <member name="P:YunLanCrm.WebApi.Controllers.CreateApiKeyRequest.AllowedIPs">
            <summary>
            允许的IP地址列表（可选）
            </summary>
        </member>
        <member name="P:YunLanCrm.WebApi.Controllers.CreateApiKeyRequest.AllowedEndpoints">
            <summary>
            允许的端点列表（可选）
            </summary>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.ATestController">
            <summary>
            测试的控制
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ATestController.LogToDatabase">
            <summary>
            把日志写入数据库
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ATestController.LogToFile">
            <summary>
            把日志写入默认文件application.log
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ATestController.VueLog(System.String)">
            <summary>
            把日志写入默认文件application.log
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ATestController.LogToFileName">
            <summary>
            把日志写入指定文件
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ATestController.GetUserId(System.String,System.String)">
            <summary>
            获取用户id
            </summary>
            <param name="category">所属类别</param>
            <param name="company">所属公司</param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmCategoryController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryController.#ctor(YunLanCrm.IServices.ICmCategoryService,YunLanCrm.IServices.ICmTicketsService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmCategoryInfo})">
            <summary>
            
            </summary>
            <param name="cmCategoryService"></param>
            <param name="cmTicketsService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryController.Add(YunLanCrm.Dto.CmCategory.CmCategoryAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryController.Update(YunLanCrm.Dto.CmCategory.CmCategoryAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryController.GetList(YunLanCrm.Dto.CmCategory.CmCategoryListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryController.GetDetailByIdAndCompanyId(System.Int64,System.String)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryController.GetDetailByIdAndCompanyId(YunLanCrm.Dto.CmCategory.CmCategoryDetailDto)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryController.DetailList(YunLanCrm.Dto.CmCategory.CmCategoryListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryController.Query(YunLanCrm.Dto.CmCategory.CmCategoryPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryController.QueryPost(YunLanCrm.Dto.CmCategory.CmCategoryPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryController.PageQuery(YunLanCrm.Dto.CmCategory.CmCategoryPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmCategoryGroupsController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryGroupsController.#ctor(YunLanCrm.IServices.ICmCategoryGroupsService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmCategoryGroupsInfo})">
            <summary>
            
            </summary>
            <param name="cmCategoryGroupsService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryGroupsController.Add(YunLanCrm.Dto.CmCategoryGroups.CmCategoryGroupsAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryGroupsController.Update(YunLanCrm.Dto.CmCategoryGroups.CmCategoryGroupsAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryGroupsController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryGroupsController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryGroupsController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryGroupsController.GetList(YunLanCrm.Dto.CmCategoryGroups.CmCategoryGroupsListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryGroupsController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryGroupsController.DetailList(YunLanCrm.Dto.CmCategoryGroups.CmCategoryGroupsListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryGroupsController.Query(YunLanCrm.Dto.CmCategoryGroups.CmCategoryGroupsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryGroupsController.QueryPost(YunLanCrm.Dto.CmCategoryGroups.CmCategoryGroupsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryGroupsController.PageQuery(YunLanCrm.Dto.CmCategoryGroups.CmCategoryGroupsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmCategoryUsersController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryUsersController.#ctor(YunLanCrm.IServices.ICmCategoryUsersService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmCategoryUsersInfo})">
            <summary>
            
            </summary>
            <param name="cmCategoryUsersService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryUsersController.Add(YunLanCrm.Dto.CmCategoryUsers.CmCategoryUsersAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryUsersController.Update(YunLanCrm.Dto.CmCategoryUsers.CmCategoryUsersAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryUsersController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryUsersController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryUsersController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryUsersController.GetList(YunLanCrm.Dto.CmCategoryUsers.CmCategoryUsersListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryUsersController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryUsersController.DetailList(YunLanCrm.Dto.CmCategoryUsers.CmCategoryUsersListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryUsersController.Query(YunLanCrm.Dto.CmCategoryUsers.CmCategoryUsersPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryUsersController.QueryPost(YunLanCrm.Dto.CmCategoryUsers.CmCategoryUsersPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCategoryUsersController.PageQuery(YunLanCrm.Dto.CmCategoryUsers.CmCategoryUsersPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmCompanyContactController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCompanyContactController.#ctor(YunLanCrm.IServices.ICmCompanyContactService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmCompanyContactInfo})">
            <summary>
            
            </summary>
            <param name="cmCompanyContactService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCompanyContactController.Add(YunLanCrm.Dto.CmCompanyContact.CmCompanyContactAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCompanyContactController.Update(YunLanCrm.Dto.CmCompanyContact.CmCompanyContactAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCompanyContactController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCompanyContactController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCompanyContactController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCompanyContactController.GetList(YunLanCrm.Dto.CmCompanyContact.CmCompanyContactListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCompanyContactController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCompanyContactController.DetailList(YunLanCrm.Dto.CmCompanyContact.CmCompanyContactListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCompanyContactController.Query(YunLanCrm.Dto.CmCompanyContact.CmCompanyContactPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCompanyContactController.QueryPost(YunLanCrm.Dto.CmCompanyContact.CmCompanyContactPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCompanyContactController.PageQuery(YunLanCrm.Dto.CmCompanyContact.CmCompanyContactPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmCustomersController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCustomersController.#ctor(YunLanCrm.IServices.ICmCustomersService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmCustomersInfo})">
            <summary>
            
            </summary>
            <param name="cmCustomersService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCustomersController.Add(YunLanCrm.Dto.CmCustomers.CmCustomersAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCustomersController.Update(YunLanCrm.Dto.CmCustomers.CmCustomersAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCustomersController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCustomersController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCustomersController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCustomersController.GetList(YunLanCrm.Dto.CmCustomers.CmCustomersListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCustomersController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCustomersController.DetailList(YunLanCrm.Dto.CmCustomers.CmCustomersListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCustomersController.Query(YunLanCrm.Dto.CmCustomers.CmCustomersPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCustomersController.QueryPost(YunLanCrm.Dto.CmCustomers.CmCustomersPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmCustomersController.PageQuery(YunLanCrm.Dto.CmCustomers.CmCustomersPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmGroupsController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsController.#ctor(YunLanCrm.IServices.ICmGroupsService,YunLanCrm.IServices.ICmGroupsUsersService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmGroupsInfo})">
            <summary>
            
            </summary>
            <param name="cmGroupsService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsController.Add(YunLanCrm.Dto.CmGroups.CmGroupsAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsController.Update(YunLanCrm.Dto.CmGroups.CmGroupsAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsController.GetList(YunLanCrm.Dto.CmGroups.CmGroupsListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsController.DetailList(YunLanCrm.Dto.CmGroups.CmGroupsListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsController.Query(YunLanCrm.Dto.CmGroups.CmGroupsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsController.QueryPost(YunLanCrm.Dto.CmGroups.CmGroupsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsController.PageQuery(YunLanCrm.Dto.CmGroups.CmGroupsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmGroupsUsersController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsUsersController.#ctor(YunLanCrm.IServices.ICmGroupsUsersService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmGroupsUsersInfo})">
            <summary>
            
            </summary>
            <param name="cmGroupsUsersService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsUsersController.Add(YunLanCrm.Dto.CmGroupsUsers.CmGroupsUsersAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsUsersController.Update(YunLanCrm.Dto.CmGroupsUsers.CmGroupsUsersAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsUsersController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsUsersController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsUsersController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsUsersController.GetList(YunLanCrm.Dto.CmGroupsUsers.CmGroupsUsersListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsUsersController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsUsersController.DetailList(YunLanCrm.Dto.CmGroupsUsers.CmGroupsUsersListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsUsersController.Query(YunLanCrm.Dto.CmGroupsUsers.CmGroupsUsersPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsUsersController.QueryPost(YunLanCrm.Dto.CmGroupsUsers.CmGroupsUsersPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmGroupsUsersController.PageQuery(YunLanCrm.Dto.CmGroupsUsers.CmGroupsUsersPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmImportTableController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmImportTableController.#ctor(YunLanCrm.IServices.ICmImportTableService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmImportTableInfo})">
            <summary>
            
            </summary>
            <param name="cmImportTableService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmImportTableController.Add(YunLanCrm.Dto.CmImportTable.CmImportTableAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmImportTableController.Update(YunLanCrm.Dto.CmImportTable.CmImportTableAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmImportTableController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmImportTableController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmImportTableController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmImportTableController.GetList(YunLanCrm.Dto.CmImportTable.CmImportTableListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmImportTableController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmImportTableController.DetailList(YunLanCrm.Dto.CmImportTable.CmImportTableListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmImportTableController.Query(YunLanCrm.Dto.CmImportTable.CmImportTablePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmImportTableController.QueryPost(YunLanCrm.Dto.CmImportTable.CmImportTablePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmImportTableController.PageQuery(YunLanCrm.Dto.CmImportTable.CmImportTablePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmOpenItemController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemController.#ctor(YunLanCrm.IServices.ICmOpenItemService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmOpenItemInfo})">
            <summary>
            
            </summary>
            <param name="cmOpenItemService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemController.Add(YunLanCrm.Dto.CmOpenItem.CmOpenItemAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemController.Update(YunLanCrm.Dto.CmOpenItem.CmOpenItemAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemController.GetList(YunLanCrm.Dto.CmOpenItem.CmOpenItemListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemController.DetailList(YunLanCrm.Dto.CmOpenItem.CmOpenItemListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemController.Query(YunLanCrm.Dto.CmOpenItem.CmOpenItemPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemController.QueryPost(YunLanCrm.Dto.CmOpenItem.CmOpenItemPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemController.PageQuery(YunLanCrm.Dto.CmOpenItem.CmOpenItemPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmOpenItemDetailAttachmentController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailAttachmentController.#ctor(YunLanCrm.IServices.ICmOpenItemDetailAttachmentService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmOpenItemDetailAttachmentInfo})">
            <summary>
            
            </summary>
            <param name="cmOpenItemDetailAttachmentService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailAttachmentController.Add(YunLanCrm.Dto.CmOpenItemDetailAttachment.CmOpenItemDetailAttachmentAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailAttachmentController.Update(YunLanCrm.Dto.CmOpenItemDetailAttachment.CmOpenItemDetailAttachmentAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailAttachmentController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailAttachmentController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailAttachmentController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailAttachmentController.GetList(YunLanCrm.Dto.CmOpenItemDetailAttachment.CmOpenItemDetailAttachmentListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailAttachmentController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailAttachmentController.DetailList(YunLanCrm.Dto.CmOpenItemDetailAttachment.CmOpenItemDetailAttachmentListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailAttachmentController.Query(YunLanCrm.Dto.CmOpenItemDetailAttachment.CmOpenItemDetailAttachmentPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailAttachmentController.QueryPost(YunLanCrm.Dto.CmOpenItemDetailAttachment.CmOpenItemDetailAttachmentPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailAttachmentController.PageQuery(YunLanCrm.Dto.CmOpenItemDetailAttachment.CmOpenItemDetailAttachmentPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailAttachmentController.UpLoadFileV2(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            文件上传
            </summary>
            <param name="files"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmOpenItemDetailCommentController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailCommentController.#ctor(YunLanCrm.IServices.ICmOpenItemDetailCommentService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmOpenItemDetailCommentInfo})">
            <summary>
            
            </summary>
            <param name="cmOpenItemDetailCommentService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailCommentController.Add(YunLanCrm.Dto.CmOpenItemDetailComment.CmOpenItemDetailCommentAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailCommentController.Update(YunLanCrm.Dto.CmOpenItemDetailComment.CmOpenItemDetailCommentAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailCommentController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailCommentController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailCommentController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailCommentController.GetList(YunLanCrm.Dto.CmOpenItemDetailComment.CmOpenItemDetailCommentListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailCommentController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailCommentController.DetailList(YunLanCrm.Dto.CmOpenItemDetailComment.CmOpenItemDetailCommentListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailCommentController.Query(YunLanCrm.Dto.CmOpenItemDetailComment.CmOpenItemDetailCommentPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailCommentController.QueryPost(YunLanCrm.Dto.CmOpenItemDetailComment.CmOpenItemDetailCommentPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailCommentController.PageQuery(YunLanCrm.Dto.CmOpenItemDetailComment.CmOpenItemDetailCommentPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmOpenItemDetailController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailController.#ctor(YunLanCrm.IServices.ICmOpenItemDetailService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmOpenItemDetailInfo})">
            <summary>
            
            </summary>
            <param name="cmOpenItemDetailService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailController.Add(YunLanCrm.Dto.CmOpenItemDetail.CmOpenItemDetailAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailController.Update(YunLanCrm.Dto.CmOpenItemDetail.CmOpenItemDetailAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailController.GetList(YunLanCrm.Dto.CmOpenItemDetail.CmOpenItemDetailListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailController.DetailList(YunLanCrm.Dto.CmOpenItemDetail.CmOpenItemDetailListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailController.Query(YunLanCrm.Dto.CmOpenItemDetail.CmOpenItemDetailPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailController.QueryPost(YunLanCrm.Dto.CmOpenItemDetail.CmOpenItemDetailPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailController.PageQuery(YunLanCrm.Dto.CmOpenItemDetail.CmOpenItemDetailPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmOpenItemDetailRelatedTicketController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailRelatedTicketController.#ctor(YunLanCrm.IServices.ICmOpenItemDetailRelatedTicketService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmOpenItemDetailRelatedTicketInfo})">
            <summary>
            
            </summary>
            <param name="cmOpenItemDetailRelatedTicketService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailRelatedTicketController.Add(YunLanCrm.Dto.CmOpenItemDetailRelatedTicket.CmOpenItemDetailRelatedTicketAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailRelatedTicketController.Update(YunLanCrm.Dto.CmOpenItemDetailRelatedTicket.CmOpenItemDetailRelatedTicketAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailRelatedTicketController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailRelatedTicketController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailRelatedTicketController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailRelatedTicketController.GetList(YunLanCrm.Dto.CmOpenItemDetailRelatedTicket.CmOpenItemDetailRelatedTicketListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailRelatedTicketController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailRelatedTicketController.DetailList(YunLanCrm.Dto.CmOpenItemDetailRelatedTicket.CmOpenItemDetailRelatedTicketListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailRelatedTicketController.Query(YunLanCrm.Dto.CmOpenItemDetailRelatedTicket.CmOpenItemDetailRelatedTicketPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailRelatedTicketController.QueryPost(YunLanCrm.Dto.CmOpenItemDetailRelatedTicket.CmOpenItemDetailRelatedTicketPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemDetailRelatedTicketController.PageQuery(YunLanCrm.Dto.CmOpenItemDetailRelatedTicket.CmOpenItemDetailRelatedTicketPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmOpenItemProjectController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemProjectController.#ctor(YunLanCrm.IServices.ICmOpenItemProjectService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmOpenItemProjectInfo})">
            <summary>
            
            </summary>
            <param name="cmOpenItemProjectService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemProjectController.Add(YunLanCrm.Dto.CmOpenItemProject.CmOpenItemProjectAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemProjectController.Update(YunLanCrm.Dto.CmOpenItemProject.CmOpenItemProjectAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemProjectController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemProjectController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemProjectController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemProjectController.GetList(YunLanCrm.Dto.CmOpenItemProject.CmOpenItemProjectListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemProjectController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemProjectController.DetailList(YunLanCrm.Dto.CmOpenItemProject.CmOpenItemProjectListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemProjectController.Query(YunLanCrm.Dto.CmOpenItemProject.CmOpenItemProjectPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemProjectController.QueryPost(YunLanCrm.Dto.CmOpenItemProject.CmOpenItemProjectPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmOpenItemProjectController.PageQuery(YunLanCrm.Dto.CmOpenItemProject.CmOpenItemProjectPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmProjectCompanyController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectCompanyController.#ctor(YunLanCrm.IServices.ICmProjectCompanyService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmProjectCompanyInfo})">
            <summary>
            
            </summary>
            <param name="cmProjectCompanyService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectCompanyController.Add(YunLanCrm.Dto.CmProjectCompany.CmProjectCompanyAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectCompanyController.Update(YunLanCrm.Dto.CmProjectCompany.CmProjectCompanyAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectCompanyController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectCompanyController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectCompanyController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectCompanyController.GetList(YunLanCrm.Dto.CmProjectCompany.CmProjectCompanyListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectCompanyController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectCompanyController.DetailList(YunLanCrm.Dto.CmProjectCompany.CmProjectCompanyListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectCompanyController.Query(YunLanCrm.Dto.CmProjectCompany.CmProjectCompanyPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectCompanyController.QueryPost(YunLanCrm.Dto.CmProjectCompany.CmProjectCompanyPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectCompanyController.PageQuery(YunLanCrm.Dto.CmProjectCompany.CmProjectCompanyPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmProjectsController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectsController.#ctor(YunLanCrm.IServices.ICmProjectsService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmProjectsInfo},YunLanCrm.IServices.ICmUserCompanyService,YunLanCrm.IRepositories.ICmProjectsRepository)">
            <summary>
            
            </summary>
            <param name="cmProjectsService"></param>
            <param name="cmUserCompanyService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectsController.Add(YunLanCrm.Dto.CmProjects.CmProjectsAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectsController.Update(YunLanCrm.Dto.CmProjects.CmProjectsAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectsController.UpdateBusinessUnit(YunLanCrm.Dto.DictItem.DictItemValueDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectsController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectsController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectsController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectsController.GetList(YunLanCrm.Dto.CmProjects.CmProjectsListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectsController.Detail(System.Int64,System.String)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <param name="idType"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectsController.DetailList(YunLanCrm.Dto.CmProjects.CmProjectsListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectsController.GetProjectID(System.String)">
            <summary>
            获取一个GetProjectID
            </summary>
            <param name="code"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectsController.Query(YunLanCrm.Dto.CmProjects.CmProjectsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectsController.QueryPost(YunLanCrm.Dto.CmProjects.CmProjectsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmProjectsController.PageQuery(YunLanCrm.Dto.CmProjects.CmProjectsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmTicketAttachmentController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketAttachmentController.#ctor(YunLanCrm.IServices.ICmTicketAttachmentService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmTicketAttachmentInfo})">
            <summary>
            
            </summary>
            <param name="cmTicketAttachmentService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketAttachmentController.Add(YunLanCrm.Dto.CmTicketAttachment.CmTicketAttachmentAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketAttachmentController.Update(YunLanCrm.Dto.CmTicketAttachment.CmTicketAttachmentAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketAttachmentController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketAttachmentController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketAttachmentController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketAttachmentController.GetList(YunLanCrm.Dto.CmTicketAttachment.CmTicketAttachmentListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketAttachmentController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketAttachmentController.DetailList(YunLanCrm.Dto.CmTicketAttachment.CmTicketAttachmentListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketAttachmentController.Query(YunLanCrm.Dto.CmTicketAttachment.CmTicketAttachmentPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketAttachmentController.QueryPost(YunLanCrm.Dto.CmTicketAttachment.CmTicketAttachmentPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketAttachmentController.PageQuery(YunLanCrm.Dto.CmTicketAttachment.CmTicketAttachmentPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmTicketContentGroupsController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentGroupsController.#ctor(YunLanCrm.IServices.ICmTicketContentGroupsService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmTicketContentGroupsInfo})">
            <summary>
            
            </summary>
            <param name="cmTicketContentGroupsService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentGroupsController.Add(YunLanCrm.Dto.CmTicketContentGroups.CmTicketContentGroupsAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentGroupsController.Update(YunLanCrm.Dto.CmTicketContentGroups.CmTicketContentGroupsAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentGroupsController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentGroupsController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentGroupsController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentGroupsController.GetList(YunLanCrm.Dto.CmTicketContentGroups.CmTicketContentGroupsListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentGroupsController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentGroupsController.DetailList(YunLanCrm.Dto.CmTicketContentGroups.CmTicketContentGroupsListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentGroupsController.Query(YunLanCrm.Dto.CmTicketContentGroups.CmTicketContentGroupsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentGroupsController.QueryPost(YunLanCrm.Dto.CmTicketContentGroups.CmTicketContentGroupsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentGroupsController.PageQuery(YunLanCrm.Dto.CmTicketContentGroups.CmTicketContentGroupsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmTicketContentsController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentsController.#ctor(YunLanCrm.IServices.ICmTicketContentsService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmTicketContentsInfo})">
            <summary>
            
            </summary>
            <param name="cmTicketContentsService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentsController.Add(YunLanCrm.Dto.CmTicketContents.CmTicketContentsAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentsController.Update(YunLanCrm.Dto.CmTicketContents.CmTicketContentsAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentsController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentsController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentsController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentsController.GetList(YunLanCrm.Dto.CmTicketContents.CmTicketContentsListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentsController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentsController.DetailList(YunLanCrm.Dto.CmTicketContents.CmTicketContentsListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentsController.Query(YunLanCrm.Dto.CmTicketContents.CmTicketContentsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentsController.QueryPost(YunLanCrm.Dto.CmTicketContents.CmTicketContentsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentsController.PageQuery(YunLanCrm.Dto.CmTicketContents.CmTicketContentsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentsController.QueryCard(YunLanCrm.Dto.CmTicketContents.CmTicketContentsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentsController.PageQueryCard(YunLanCrm.Dto.CmTicketContents.CmTicketContentsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmTicketContentUsersController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentUsersController.#ctor(YunLanCrm.IServices.ICmTicketContentUsersService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmTicketContentUsersInfo})">
            <summary>
            
            </summary>
            <param name="cmTicketContentUsersService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentUsersController.Add(YunLanCrm.Dto.CmTicketContentUsers.CmTicketContentUsersAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentUsersController.Update(YunLanCrm.Dto.CmTicketContentUsers.CmTicketContentUsersAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentUsersController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentUsersController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentUsersController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentUsersController.GetList(YunLanCrm.Dto.CmTicketContentUsers.CmTicketContentUsersListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentUsersController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentUsersController.DetailList(YunLanCrm.Dto.CmTicketContentUsers.CmTicketContentUsersListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentUsersController.Query(YunLanCrm.Dto.CmTicketContentUsers.CmTicketContentUsersPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentUsersController.QueryPost(YunLanCrm.Dto.CmTicketContentUsers.CmTicketContentUsersPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketContentUsersController.PageQuery(YunLanCrm.Dto.CmTicketContentUsers.CmTicketContentUsersPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmTicketDelegateController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketDelegateController.#ctor(YunLanCrm.IServices.ICmTicketDelegateService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmTicketDelegateInfo})">
            <summary>
            
            </summary>
            <param name="cmTicketDelegateService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketDelegateController.Add(YunLanCrm.Dto.CmTicketDelegate.CmTicketDelegateAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketDelegateController.Update(YunLanCrm.Dto.CmTicketDelegate.CmTicketDelegateAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketDelegateController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketDelegateController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketDelegateController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketDelegateController.GetList(YunLanCrm.Dto.CmTicketDelegate.CmTicketDelegateListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketDelegateController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketDelegateController.DetailList(YunLanCrm.Dto.CmTicketDelegate.CmTicketDelegateListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketDelegateController.Query(YunLanCrm.Dto.CmTicketDelegate.CmTicketDelegatePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketDelegateController.QueryPost(YunLanCrm.Dto.CmTicketDelegate.CmTicketDelegatePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketDelegateController.PageQuery(YunLanCrm.Dto.CmTicketDelegate.CmTicketDelegatePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmTicketGroupsController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketGroupsController.#ctor(YunLanCrm.IServices.ICmTicketGroupsService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmTicketGroupsInfo})">
            <summary>
            
            </summary>
            <param name="cmTicketGroupsService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketGroupsController.Add(YunLanCrm.Dto.CmTicketGroups.CmTicketGroupsAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketGroupsController.Update(YunLanCrm.Dto.CmTicketGroups.CmTicketGroupsAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketGroupsController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketGroupsController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketGroupsController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketGroupsController.GetList(YunLanCrm.Dto.CmTicketGroups.CmTicketGroupsListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketGroupsController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketGroupsController.DetailList(YunLanCrm.Dto.CmTicketGroups.CmTicketGroupsListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketGroupsController.Query(YunLanCrm.Dto.CmTicketGroups.CmTicketGroupsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketGroupsController.QueryPost(YunLanCrm.Dto.CmTicketGroups.CmTicketGroupsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketGroupsController.PageQuery(YunLanCrm.Dto.CmTicketGroups.CmTicketGroupsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmTicketHistoryController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketHistoryController.#ctor(YunLanCrm.IServices.ICmTicketHistoryService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmTicketHistoryInfo})">
            <summary>
            
            </summary>
            <param name="cmTicketHistoryService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketHistoryController.Add(YunLanCrm.Dto.CmTicketHistory.CmTicketHistoryAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketHistoryController.Update(YunLanCrm.Dto.CmTicketHistory.CmTicketHistoryAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketHistoryController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketHistoryController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketHistoryController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketHistoryController.GetList(YunLanCrm.Dto.CmTicketHistory.CmTicketHistoryListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketHistoryController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketHistoryController.DetailList(YunLanCrm.Dto.CmTicketHistory.CmTicketHistoryListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketHistoryController.Query(YunLanCrm.Dto.CmTicketHistory.CmTicketHistoryPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketHistoryController.QueryPost(YunLanCrm.Dto.CmTicketHistory.CmTicketHistoryPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketHistoryController.PageQuery(YunLanCrm.Dto.CmTicketHistory.CmTicketHistoryPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmTicketsController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketsController.#ctor(YunLanCrm.IServices.ICmTicketsService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmTicketsInfo},YunLanCrm.IServices.IWorkflowCrmService)">
            <summary>
            
            </summary>
            <param name="cmTicketsService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketsController.Save(YunLanCrm.Dto.CmTickets.CmTicketsAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketsController.Add(YunLanCrm.Dto.CmTickets.CmTicketsAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketsController.Update(YunLanCrm.Dto.CmTickets.CmTicketsAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketsController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketsController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketsController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketsController.GetList(YunLanCrm.Dto.CmTickets.CmTicketsListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketsController.Detail(System.String)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketsController.DetailList(YunLanCrm.Dto.CmTickets.CmTicketsListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketsController.Query(YunLanCrm.Dto.CmTickets.CmTicketsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketsController.QueryPost(YunLanCrm.Dto.CmTickets.CmTicketsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketsController.PageQuery(YunLanCrm.Dto.CmTickets.CmTicketsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketsController.GetTicketCount">
            <summary>
            统计
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketsController.SaveCmTicketComment(YunLanCrm.Dto.CmTickets.CmTicketsAddOrUpdateDto)">
            <summary>
            添加评论
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmTicketUsersController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersController.#ctor(YunLanCrm.IServices.ICmTicketUsersService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmTicketUsersInfo})">
            <summary>
            
            </summary>
            <param name="cmTicketUsersService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersController.Add(YunLanCrm.Dto.CmTicketUsers.CmTicketUsersAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersController.Update(YunLanCrm.Dto.CmTicketUsers.CmTicketUsersAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersController.GetList(YunLanCrm.Dto.CmTicketUsers.CmTicketUsersListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersController.DetailList(YunLanCrm.Dto.CmTicketUsers.CmTicketUsersListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersController.Query(YunLanCrm.Dto.CmTicketUsers.CmTicketUsersPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersController.QueryPost(YunLanCrm.Dto.CmTicketUsers.CmTicketUsersPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersController.PageQuery(YunLanCrm.Dto.CmTicketUsers.CmTicketUsersPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmTicketUsersGrantController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersGrantController.#ctor(YunLanCrm.IServices.ICmTicketUsersGrantService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmTicketUsersGrantInfo})">
            <summary>
            
            </summary>
            <param name="cmTicketUsersGrantService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersGrantController.Add(YunLanCrm.Dto.CmTicketUsersGrant.CmTicketUsersGrantAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersGrantController.Update(YunLanCrm.Dto.CmTicketUsersGrant.CmTicketUsersGrantAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersGrantController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersGrantController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersGrantController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersGrantController.GetList(YunLanCrm.Dto.CmTicketUsersGrant.CmTicketUsersGrantListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersGrantController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersGrantController.DetailList(YunLanCrm.Dto.CmTicketUsersGrant.CmTicketUsersGrantListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersGrantController.Query(YunLanCrm.Dto.CmTicketUsersGrant.CmTicketUsersGrantPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersGrantController.QueryPost(YunLanCrm.Dto.CmTicketUsersGrant.CmTicketUsersGrantPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmTicketUsersGrantController.PageQuery(YunLanCrm.Dto.CmTicketUsersGrant.CmTicketUsersGrantPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmUserCompanyController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserCompanyController.#ctor(YunLanCrm.IServices.ICmUserCompanyService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmUserCompanyInfo})">
            <summary>
            
            </summary>
            <param name="cmUserCompanyService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserCompanyController.Add(YunLanCrm.Dto.CmUserCompany.CmUserCompanyAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserCompanyController.Update(YunLanCrm.Dto.CmUserCompany.CmUserCompanyAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserCompanyController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserCompanyController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserCompanyController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserCompanyController.GetList(YunLanCrm.Dto.CmUserCompany.CmUserCompanyListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserCompanyController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserCompanyController.DetailList(YunLanCrm.Dto.CmUserCompany.CmUserCompanyListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserCompanyController.Query(YunLanCrm.Dto.CmUserCompany.CmUserCompanyPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserCompanyController.QueryPost(YunLanCrm.Dto.CmUserCompany.CmUserCompanyPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserCompanyController.PageQuery(YunLanCrm.Dto.CmUserCompany.CmUserCompanyPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CmUserMasterTypeController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserMasterTypeController.#ctor(YunLanCrm.IServices.ICmUserMasterTypeService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CmUserMasterTypeInfo})">
            <summary>
            
            </summary>
            <param name="cmUserMasterTypeService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserMasterTypeController.Add(YunLanCrm.Dto.CmUserMasterType.CmUserMasterTypeAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserMasterTypeController.Update(YunLanCrm.Dto.CmUserMasterType.CmUserMasterTypeAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserMasterTypeController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserMasterTypeController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserMasterTypeController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserMasterTypeController.GetList(YunLanCrm.Dto.CmUserMasterType.CmUserMasterTypeListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserMasterTypeController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserMasterTypeController.DetailList(YunLanCrm.Dto.CmUserMasterType.CmUserMasterTypeListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserMasterTypeController.Query(YunLanCrm.Dto.CmUserMasterType.CmUserMasterTypePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserMasterTypeController.QueryPost(YunLanCrm.Dto.CmUserMasterType.CmUserMasterTypePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CmUserMasterTypeController.PageQuery(YunLanCrm.Dto.CmUserMasterType.CmUserMasterTypePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.CompanyController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CompanyController.#ctor(YunLanCrm.IServices.ICompanyService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.CompanyInfo},YunLanCrm.IRepositories.ICompanyRepository)">
            <summary>
            
            </summary>
            <param name="companyService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CompanyController.Add(YunLanCrm.Dto.Company.CompanyAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CompanyController.Update(YunLanCrm.Dto.Company.CompanyEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CompanyController.UpdateCompanyIndustry(YunLanCrm.Dto.DictItem.DictItemValueDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CompanyController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="companyId">companyId</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CompanyController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CompanyController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="companyId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CompanyController.Detail(System.Int64,System.String)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="companyId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CompanyController.DetailByOrgId(System.Int32)">
            <summary>
            根据组织架构ID
            获取一个详细信息
            </summary>
            <param name="orgId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CompanyController.List(YunLanCrm.Dto.Company.CompanyListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CompanyController.Query(YunLanCrm.Dto.Company.CompanyPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CompanyController.QueryPost(YunLanCrm.Dto.Company.CompanyPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.CompanyController.PageQuery(YunLanCrm.Dto.Company.CompanyPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.ConfigController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ConfigController.#ctor(YunLanCrm.IServices.IConfigService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.ConfigInfo},YunLanCrm.Common.ICaching,YunLanCrm.IRepositories.IConfigRepository)">
            <summary>
            
            </summary>
            <param name="configService"></param>
            <param name="logger"></param>
            <param name="cache"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ConfigController.GetConfig(System.String)">
            <summary>
            获取系统的全局配置
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ConfigController.GetAppConfig(System.String)">
            <summary>
            获取系统的Site配置，无限制。
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ConfigController.Add(YunLanCrm.Dto.Config.ConfigAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ConfigController.Update(YunLanCrm.Dto.Config.ConfigEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ConfigController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ConfigController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ConfigController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ConfigController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ConfigController.List(YunLanCrm.Dto.Config.ConfigListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ConfigController.Query(YunLanCrm.Dto.Config.ConfigPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ConfigController.QueryPost(YunLanCrm.Dto.Config.ConfigPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.ConfigController.PageQuery(YunLanCrm.Dto.Config.ConfigPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.DepartmentController">
            <summary>
            
            </summary>
        </member>
        <member name="F:YunLanCrm.WebApi.Controllers.DepartmentController._departmentService">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DepartmentController.#ctor(YunLanCrm.IServices.IDepartmentService,YunLanCrm.IServices.IOrganizationService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.DepartmentInfo})">
            <summary>
            
            </summary>
            <param name="departmentService"></param>
            <param name="organizationService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DepartmentController.Tree(System.Nullable{System.Int64})">
            <summary>
            
            </summary>
            <param name="parentId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DepartmentController.Add(YunLanCrm.Dto.Department.DepartmentAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DepartmentController.Update(YunLanCrm.Dto.Department.DepartmentEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DepartmentController.Delete(System.Int32)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DepartmentController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DepartmentController.Get(System.Int32)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DepartmentController.Detail(System.Int32)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DepartmentController.DetailByOrgId(System.Int32)">
            <summary>
            根据组织架构ID
            获取一个详细信息
            </summary>
            <param name="orgId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DepartmentController.List(YunLanCrm.Dto.Department.DepartmentListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DepartmentController.Query(YunLanCrm.Dto.Department.DepartmentPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DepartmentController.QueryPost(YunLanCrm.Dto.Department.DepartmentPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DepartmentController.PageQuery(YunLanCrm.Dto.Department.DepartmentPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.DictController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictController.#ctor(YunLanCrm.IServices.IDictService,YunLanCrm.IServices.IDictItemService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.DictInfo})">
            <summary>
            
            </summary>
            <param name="dictService"></param>
            <param name="dictItemService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictController.Add(YunLanCrm.Dto.Dict.DictAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictController.Update(YunLanCrm.Dto.Dict.DictEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="dictId">dictId</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="dictId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="dictId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictController.List(YunLanCrm.Dto.Dict.DictListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictController.Query(YunLanCrm.Dto.Dict.DictPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictController.QueryPost(YunLanCrm.Dto.Dict.DictPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictController.PageQuery(YunLanCrm.Dto.Dict.DictPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.DictItemController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictItemController.#ctor(YunLanCrm.IServices.IDictItemService,YunLanCrm.Common.HttpContextUser.IUser,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.DictItemInfo},YunLanCrm.IServices.IDictService)">
            <summary>
            
            </summary>
            <param name="dictItemService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictItemController.Add(YunLanCrm.Dto.DictItem.DictItemAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictItemController.Update(YunLanCrm.Dto.DictItem.DictItemEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictItemController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="itemId">itemId</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictItemController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictItemController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="itemId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictItemController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="itemId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictItemController.Many(System.Collections.Generic.List{System.String})">
            <summary>
            获取多类型的字典列表
            </summary>
            <param name="types"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictItemController.Query(YunLanCrm.Dto.DictItem.DictItemPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictItemController.QueryPost(YunLanCrm.Dto.DictItem.DictItemPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.DictItemController.PageQuery(YunLanCrm.Dto.DictItem.DictItemPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.EmailsController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailsController.#ctor(YunLanCrm.IServices.IEmailsService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.EmailsInfo})">
            <summary>
            
            </summary>
            <param name="emailsService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailsController.GetEmails">
            <summary>
            获取Email
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailsController.SendEmail(YunLanCrm.Dto.Emails.EmailsAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailsController.Update(YunLanCrm.Dto.Emails.EmailsAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailsController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailsController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailsController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailsController.GetList(YunLanCrm.Dto.Emails.EmailsListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailsController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailsController.DetailList(YunLanCrm.Dto.Emails.EmailsListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailsController.Query(YunLanCrm.Dto.Emails.EmailsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailsController.QueryPost(YunLanCrm.Dto.Emails.EmailsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailsController.PageQuery(YunLanCrm.Dto.Emails.EmailsPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.EmailTemplateController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailTemplateController.#ctor(YunLanCrm.IServices.IEmailTemplateService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.EmailTemplateInfo})">
            <summary>
            
            </summary>
            <param name="emailTemplateService"></param>
            <param name="user"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailTemplateController.Add(YunLanCrm.Dto.EmailTemplate.EmailTemplateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailTemplateController.Update(YunLanCrm.Dto.EmailTemplate.EmailTemplateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailTemplateController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="templateId">templateId</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailTemplateController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailTemplateController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="templateId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailTemplateController.GetList(YunLanCrm.Dto.EmailTemplate.EmailTemplateListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailTemplateController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="templateId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailTemplateController.DetailList(YunLanCrm.Dto.EmailTemplate.EmailTemplateListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailTemplateController.Query(YunLanCrm.Dto.EmailTemplate.EmailTemplatePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailTemplateController.QueryPost(YunLanCrm.Dto.EmailTemplate.EmailTemplatePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.EmailTemplateController.PageQuery(YunLanCrm.Dto.EmailTemplate.EmailTemplatePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.FilesController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.#ctor(YunLanCrm.IServices.IFilesService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.FilesInfo})">
            <summary>
            
            </summary>
            <param name="filesService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.FileDown(Microsoft.AspNetCore.Hosting.IWebHostEnvironment,System.Nullable{System.Int64},System.String)">
            <summary>
            下载文件
            </summary>
            <param name="environment"></param>
            <param name="id">文件id</param>
            <param name="name">文件名称</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.FileDownByName(System.String)">
            <summary>
            下载文件
            </summary>
            <param name="name">文件名称（新的名称）</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.UpLoadFrom(Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            多文件上传（包含其他参数）
            </summary>
            <param name="environment"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.UpLoadFile(System.Collections.Generic.List{Microsoft.AspNetCore.Http.IFormFile})">
            <summary>
            文件上传
            </summary>
            <param name="files"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.UpLoadFileV2(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            文件上传
            </summary>
            <param name="files"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.Add(YunLanCrm.Dto.Files.FilesAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.Update(YunLanCrm.Dto.Files.FilesEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="fileId">fileId</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="fileId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="fileId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.List(YunLanCrm.Dto.Files.FilesListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.Query(YunLanCrm.Dto.Files.FilesPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.QueryPost(YunLanCrm.Dto.Files.FilesPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.PageQuery(YunLanCrm.Dto.Files.FilesPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.FilesController.UploadFile(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            文件上传
            </summary>
            <param name="files"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.HolidayController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayController.#ctor(YunLanCrm.IServices.IHolidayService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.HolidayInfo})">
            <summary>
            
            </summary>
            <param name="holidayService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayController.Add(YunLanCrm.Dto.Holiday.HolidayAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayController.Update(YunLanCrm.Dto.Holiday.HolidayAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayController.SaveHoliday(System.Collections.Generic.List{YunLanCrm.Dto.Holiday.HolidayAddOrUpdateDto})">
            <summary>
            批量添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayController.GetList(YunLanCrm.Dto.Holiday.HolidayListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayController.DetailList(YunLanCrm.Dto.Holiday.HolidayListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayController.Query(YunLanCrm.Dto.Holiday.HolidayPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayController.QueryPost(YunLanCrm.Dto.Holiday.HolidayPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayController.PageQuery(YunLanCrm.Dto.Holiday.HolidayPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.HolidayReminderLogController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayReminderLogController.#ctor(YunLanCrm.IServices.IHolidayReminderLogService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.HolidayReminderLogInfo})">
            <summary>
            
            </summary>
            <param name="holidayReminderLogService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayReminderLogController.Add(YunLanCrm.Dto.HolidayReminderLog.HolidayReminderLogAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayReminderLogController.Update(YunLanCrm.Dto.HolidayReminderLog.HolidayReminderLogAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayReminderLogController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayReminderLogController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayReminderLogController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayReminderLogController.GetList(YunLanCrm.Dto.HolidayReminderLog.HolidayReminderLogListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayReminderLogController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayReminderLogController.DetailList(YunLanCrm.Dto.HolidayReminderLog.HolidayReminderLogListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayReminderLogController.Query(YunLanCrm.Dto.HolidayReminderLog.HolidayReminderLogPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayReminderLogController.QueryPost(YunLanCrm.Dto.HolidayReminderLog.HolidayReminderLogPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.HolidayReminderLogController.PageQuery(YunLanCrm.Dto.HolidayReminderLog.HolidayReminderLogPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.LogEventController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.LogEventController.#ctor(YunLanCrm.IServices.ILogEventService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.LogEventInfo})">
            <summary>
            
            </summary>
            <param name="logEventService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.LogEventController.Add(YunLanCrm.Dto.LogEvent.LogEventAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.LogEventController.Update(YunLanCrm.Dto.LogEvent.LogEventEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.LogEventController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.LogEventController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.LogEventController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.LogEventController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.LogEventController.List(YunLanCrm.Dto.LogEvent.LogEventListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.LogEventController.Query(YunLanCrm.Dto.LogEvent.LogEventPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.LogEventController.QueryPost(YunLanCrm.Dto.LogEvent.LogEventPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.LogEventController.PageQuery(YunLanCrm.Dto.LogEvent.LogEventPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.MenuApiController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuApiController.#ctor(YunLanCrm.IServices.IMenuApiService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.MenuApiInfo})">
            <summary>
            
            </summary>
            <param name="menuApiService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuApiController.Add(YunLanCrm.Dto.MenuApi.MenuApiAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuApiController.Update(YunLanCrm.Dto.MenuApi.MenuApiEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuApiController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="apiId">apiId</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuApiController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuApiController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="apiId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuApiController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="apiId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuApiController.List(YunLanCrm.Dto.MenuApi.MenuApiListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuApiController.Query(YunLanCrm.Dto.MenuApi.MenuApiPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuApiController.QueryPost(YunLanCrm.Dto.MenuApi.MenuApiPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuApiController.PageQuery(YunLanCrm.Dto.MenuApi.MenuApiPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.MenuController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.#ctor(YunLanCrm.IServices.IMenuService,YunLanCrm.IServices.IMenuApiService,YunLanCrm.AuthHelper.PermissionRequirement,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.MenuInfo})">
            <summary>
            
            </summary>
            <param name="menuService"></param>
            <param name="menuApiService"></param>
            <param name="requirement"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.SynRoute(System.Collections.Generic.List{YunLanCrm.Dto.Menu.RouteTreeDto})">
            <summary>
            把前台写好的路由配置同步给后台数据库
            </summary>
            <param name="req"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.WebMenus(YunLanCrm.Dto.Menu.RouteTreeInput)">
            <summary>
            获取用户的后台菜单
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.PermissionTable">
            <summary>
            获取功能模块对应的权限 Table
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.PermissionTree">
            <summary>
            获取功能模块对应的权限Tree
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.Tree(System.Nullable{System.Int64},System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="parentId"></param>
            <param name="menuType"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.Add(YunLanCrm.Dto.Menu.MenuAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.Update(YunLanCrm.Dto.Menu.MenuEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.List(YunLanCrm.Dto.Menu.MenuListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.Query(YunLanCrm.Dto.Menu.MenuPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.QueryPost(YunLanCrm.Dto.Menu.MenuPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.MenuController.PageQuery(YunLanCrm.Dto.Menu.MenuPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.OnlineUserController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OnlineUserController.#ctor(YunLanCrm.IServices.IOnlineUserService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.OnlineUserInfo})">
            <summary>
            
            </summary>
            <param name="onlineUserService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OnlineUserController.Add(YunLanCrm.Dto.OnlineUser.OnlineUserAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OnlineUserController.Update(YunLanCrm.Dto.OnlineUser.OnlineUserEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OnlineUserController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OnlineUserController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OnlineUserController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OnlineUserController.List(YunLanCrm.Dto.OnlineUser.OnlineUserListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OnlineUserController.Query(YunLanCrm.Dto.OnlineUser.OnlineUserPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OnlineUserController.QueryPost(YunLanCrm.Dto.OnlineUser.OnlineUserPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OnlineUserController.PageQuery(YunLanCrm.Dto.OnlineUser.OnlineUserPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.OperateLogController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OperateLogController.#ctor(YunLanCrm.IServices.IOperateLogService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.OperateLogInfo})">
            <summary>
            
            </summary>
            <param name="operateLogService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OperateLogController.Add(YunLanCrm.Dto.OperateLog.OperateLogAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OperateLogController.Update(YunLanCrm.Dto.OperateLog.OperateLogEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OperateLogController.Delete(System.Int32)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OperateLogController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OperateLogController.Get(System.Int32)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OperateLogController.Detail(System.Int32)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OperateLogController.List(YunLanCrm.Dto.OperateLog.OperateLogListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OperateLogController.Query(YunLanCrm.Dto.OperateLog.OperateLogPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OperateLogController.QueryPost(YunLanCrm.Dto.OperateLog.OperateLogPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OperateLogController.PageQuery(YunLanCrm.Dto.OperateLog.OperateLogPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.OrganizationController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.#ctor(YunLanCrm.IServices.IOrganizationService,YunLanCrm.IServices.ICompanyService,YunLanCrm.IServices.IDepartmentService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.OrganizationInfo},YunLanCrm.IServices.IUserOrgService,YunLanCrm.Common.HttpContextUser.IUser,YunLanCrm.IServices.ICmUserCompanyService,YunLanCrm.IServices.ICmUserCompanyService,YunLanCrm.IRepositories.IOrganizationRepository,YunLanCrm.IServices.IUserService,YunLanCrm.IServices.ICmTicketsService)">
            <summary>
            
            </summary>
            <param name="organizationService"></param>
            <param name="companyService"></param>
            <param name="departmentService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.Tree(YunLanCrm.Dto.Organization.OrganizationTreeDto)">
            <summary>
            
            </summary>
            <param name="parentId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.Add(YunLanCrm.Dto.Organization.OrganizationAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.Update(YunLanCrm.Dto.Organization.OrganizationEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="orgId">orgId</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="orgId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="orgId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.List(YunLanCrm.Dto.Organization.OrganizationListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.GetEndUserOrganizationList(YunLanCrm.Dto.Organization.OrganizationListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.GetCompanyProjectTree(YunLanCrm.Dto.Organization.OrganizationListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.GetCompanyProjectTreeV2(YunLanCrm.Dto.Organization.OrganizationListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.GetEndUserOrganizationByUserId(YunLanCrm.Dto.Organization.OrganizationListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.Query(YunLanCrm.Dto.Organization.OrganizationPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.QueryPost(YunLanCrm.Dto.Organization.OrganizationPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.OrganizationController.PageQuery(YunLanCrm.Dto.Organization.OrganizationPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.PermissionController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.PermissionController.#ctor(YunLanCrm.IServices.IPermissionService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.PermissionInfo})">
            <summary>
            
            </summary>
            <param name="permissionService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.PermissionController.Add(YunLanCrm.Dto.Permission.PermissionAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.PermissionController.Update(YunLanCrm.Dto.Permission.PermissionEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.PermissionController.Delete(System.Int32)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.PermissionController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.PermissionController.Get(System.Int32)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.PermissionController.Detail(System.Int32)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.PermissionController.List(YunLanCrm.Dto.Permission.PermissionListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.PermissionController.Query(YunLanCrm.Dto.Permission.PermissionPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.PermissionController.QueryPost(YunLanCrm.Dto.Permission.PermissionPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.PermissionController.PageQuery(YunLanCrm.Dto.Permission.PermissionPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.RoleController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleController.#ctor(YunLanCrm.IServices.IRoleService,YunLanCrm.IServices.IRoleGroupService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.RoleInfo},YunLanCrm.IServices.IRoleModulePermissionService,YunLanCrm.Common.HttpContextUser.IUser)">
            <summary>
            
            </summary>
            <param name="roleService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleController.Add(YunLanCrm.Dto.Role.RoleAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleController.Update(YunLanCrm.Dto.Role.RoleEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleController.Delete(System.Int32)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleController.Get(System.Int32)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleController.Detail(System.Int32)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleController.GetRoleGroups(YunLanCrm.Dto.Role.RoleListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleController.List(YunLanCrm.Dto.Role.RoleListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleController.Query(YunLanCrm.Dto.Role.RolePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleController.QueryPost(YunLanCrm.Dto.Role.RolePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleController.PageQuery(YunLanCrm.Dto.Role.RolePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.RoleModulePermissionController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleModulePermissionController.#ctor(YunLanCrm.IServices.IRoleModulePermissionService,YunLanCrm.IServices.IMenuService,YunLanCrm.AuthHelper.PermissionRequirement,YunLanCrm.IServices.IMenuApiService,YunLanCrm.IRepository.UnitOfWork.IUnitOfWork,YunLanCrm.IServices.IUserService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.RoleModulePermissionInfo})">
            <summary>
            
            </summary>
            <param name="roleModulePermissionService"></param>
            <param name="menuService"></param>
            <param name="requirement"></param>
            <param name="menuApiService"></param>
            <param name="unitOfWork"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleModulePermissionController.Add(YunLanCrm.Dto.RoleModulePermission.RoleModulePermissionAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleModulePermissionController.Update(YunLanCrm.Dto.RoleModulePermission.RoleModulePermissionEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleModulePermissionController.Delete(System.Int32)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleModulePermissionController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleModulePermissionController.Get(System.Int32)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleModulePermissionController.Detail(System.Int32)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleModulePermissionController.List(YunLanCrm.Dto.RoleModulePermission.RoleModulePermissionListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleModulePermissionController.Query(YunLanCrm.Dto.RoleModulePermission.RoleModulePermissionPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleModulePermissionController.QueryPost(YunLanCrm.Dto.RoleModulePermission.RoleModulePermissionPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleModulePermissionController.PageQuery(YunLanCrm.Dto.RoleModulePermission.RoleModulePermissionPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleModulePermissionController.Assign(YunLanCrm.Dto.RoleModulePermission.RoleMenuInput)">
            <summary>
            保存菜单权限分配
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.RoleModulePermissionController.GetPermissionList(YunLanCrm.Model.Dto.RoleModulePermission.GetPermissionListInput)">
            <summary>
            
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.TaskQueueController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TaskQueueController.#ctor(YunLanCrm.IServices.ITaskQueueService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.TaskQueueInfo},YunLanCrm.Tasks.ISchedulerCenter,YunLanCrm.IRepository.UnitOfWork.IUnitOfWork,YunLanCrm.IServices.ILogEventService)">
            <summary>
            
            </summary>
            <param name="taskQueueService"></param>
            <param name="logger"></param>
            <param name="schedulerCenter"></param>
            <param name="unitOfWork"></param>
            <param name="logEventService"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TaskQueueController.Add(YunLanCrm.Dto.TaskQueue.TaskQueueAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TaskQueueController.Update(YunLanCrm.Dto.TaskQueue.TaskQueueEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TaskQueueController.Delete(System.Int32)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TaskQueueController.OperationTask(YunLanCrm.Dto.TaskQueue.OperationTaskDto)">
            <summary>
            开启、重启、暂停、恢复 等操作
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TaskQueueController.StartJob(System.Int32)">
            <summary>
            启动计划任务
            </summary>
            <param name="jobId"></param>
            <returns></returns>        
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TaskQueueController.StopJob(System.Int32)">
            <summary>
            停止一个计划任务
            </summary>
            <param name="jobId"></param>
            <returns></returns>        
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TaskQueueController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TaskQueueController.Get(System.Int32)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TaskQueueController.Detail(System.Int32)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TaskQueueController.List(YunLanCrm.Dto.TaskQueue.TaskQueueListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TaskQueueController.Query(YunLanCrm.Dto.TaskQueue.TaskQueuePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TaskQueueController.QueryPost(YunLanCrm.Dto.TaskQueue.TaskQueuePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TaskQueueController.PageQuery(YunLanCrm.Dto.TaskQueue.TaskQueuePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.TokenController">
            <summary>
            认证
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TokenController.#ctor(YunLanCrm.IServices.IUserService,YunLanCrm.IServices.IUserRoleService,YunLanCrm.Common.HttpContextUser.IUser,YunLanCrm.IServices.IRoleService,YunLanCrm.IServices.IRoleModulePermissionService,YunLanCrm.IServices.IMenuService,YunLanCrm.IServices.IOnlineUserService,YunLanCrm.IServices.ICmGroupsUsersService,YunLanCrm.IRepositories.ICmUserCompanyRepository,YunLanCrm.IRepositories.IUserRepository,YunLanCrm.IServices.ILogEventService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.WebApi.Controllers.TokenController},YunLanCrm.Common.Utils.SystemAppInfo,YunLanCrm.IServices.IOrganizationService,YunLanCrm.IServices.IDictItemService)">
            <summary>
            
            </summary>
            <param name="userService"></param>
            <param name="userRoleService"></param>
            <param name="user"></param>
            <param name="roleService"></param>
            <param name="roleModulePermissionService"></param>
            <param name="menuService"></param>
            <param name="onlineUserService"></param>
            <param name="cmGroupsUsersService"></param>
            <param name="cmUserCompanyRepository"></param>
            <param name="userRepository"></param>
            <param name="logEventService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TokenController.Login(YunLanCrm.Dto.Token.LoginInput)">
            <summary>
            
            </summary>
            <param name="req"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TokenController.GenerateTokenForExternal(System.String)">
            <summary>
            为外部系统提供的接口（使用API Key认证）
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TokenController.RefreshToken(YunLanCrm.Dto.Token.TokenInputDto)">
            <summary>
            
            </summary>
            <param name="req"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.TokenController.GetMyInfo">
            <summary>
            根据token 获取我的信息
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.UserController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.#ctor(YunLanCrm.IServices.IUserService,YunLanCrm.IServices.IEmailsService,YunLanCrm.IServices.IConfigService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.UserInfo},YunLanCrm.IServices.IOnlineUserService,YunLanCrm.IServices.ICmTicketsService)">
            <summary>
            
            </summary>
            <param name="userService"></param>
            <param name="emailsService"></param>
            <param name="configService"></param>
            <param name="onlineUserService"></param>
            <param name="logger"></param>
            <param name="cmTicketService"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.GetApproverUsers(System.Nullable{System.Int64})">
            <summary>
            获取 Approver 角色的用户
            </summary>
            <param name="companyId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.Add(YunLanCrm.Dto.User.UserAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.AddForExternal(YunLanCrm.Dto.User.UserAddOrUpdateDto)">
            <summary>
            为外部系统提供的接口（使用API Key认证）
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.Update(YunLanCrm.Dto.User.UserAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.UpdateForExternal(YunLanCrm.Dto.User.UserAddOrUpdateDto)">
            <summary>
            为外部系统提供的接口（使用API Key认证）
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.Delete(System.Int32)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.Get(System.Int32)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.GetList(YunLanCrm.Dto.User.UserListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.GetListByCategoryAndCustomer(YunLanCrm.Dto.User.UserListQueryDto)">
            <summary>
            根据Category和Customer筛选出符合条件的用户和组
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.GetAllGroupUserList(YunLanCrm.Dto.User.UserListQueryDto)">
            <summary>
            取出所有组下的用户
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.GetAllGroupUserListV2(YunLanCrm.Dto.User.UserListQueryDto)">
            <summary>
            取出所有组下的用户
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.GetStaffMemberList(YunLanCrm.Dto.User.UserListQueryDto)">
            <summary>
            取出所有内部用户
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.Detail(System.Int32)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.GetDetailByEmailForExternal(System.String)">
            <summary>
            为外部系统提供的用户详情查询接口（使用API Key认证）
            </summary>
            <param name="email">用户邮箱</param>
            <returns>用户详情</returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.DetailList(YunLanCrm.Dto.User.UserListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.Query(YunLanCrm.Dto.User.UserPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.QueryPost(YunLanCrm.Dto.User.UserPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserController.PageQuery(YunLanCrm.Dto.User.UserPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.UserDelegateAuditController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateAuditController.#ctor(YunLanCrm.IServices.IUserDelegateAuditService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.UserDelegateAuditInfo})">
            <summary>
            
            </summary>
            <param name="userDelegateAuditService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateAuditController.Add(YunLanCrm.Dto.UserDelegateAudit.UserDelegateAuditAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateAuditController.Update(YunLanCrm.Dto.UserDelegateAudit.UserDelegateAuditAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateAuditController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="auditId">auditId</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateAuditController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateAuditController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="auditId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateAuditController.GetList(YunLanCrm.Dto.UserDelegateAudit.UserDelegateAuditListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateAuditController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="auditId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateAuditController.DetailList(YunLanCrm.Dto.UserDelegateAudit.UserDelegateAuditListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateAuditController.Query(YunLanCrm.Dto.UserDelegateAudit.UserDelegateAuditPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateAuditController.QueryPost(YunLanCrm.Dto.UserDelegateAudit.UserDelegateAuditPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateAuditController.PageQuery(YunLanCrm.Dto.UserDelegateAudit.UserDelegateAuditPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.UserDelegateController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateController.#ctor(YunLanCrm.IServices.IUserDelegateService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.UserDelegateInfo})">
            <summary>
            
            </summary>
            <param name="userDelegateService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateController.Add(YunLanCrm.Dto.UserDelegate.UserDelegateAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateController.Update(YunLanCrm.Dto.UserDelegate.UserDelegateAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateController.GetList(YunLanCrm.Dto.UserDelegate.UserDelegateListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateController.DetailList(YunLanCrm.Dto.UserDelegate.UserDelegateListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateController.Query(YunLanCrm.Dto.UserDelegate.UserDelegatePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateController.QueryPost(YunLanCrm.Dto.UserDelegate.UserDelegatePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateController.PageQuery(YunLanCrm.Dto.UserDelegate.UserDelegatePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateController.GetSelectUserDelegate(YunLanCrm.Dto.UserDelegate.UserDelegateDto)">
            <summary>
            取出所有Delegate用户
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateController.UpdateIsApprove(YunLanCrm.Dto.UserDelegate.UserDelegateAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserDelegateController.UpdateIsApproveBatch(YunLanCrm.Model.Dto.UserDelegate.SelectUserDelegateOnOffDto)">
            <summary>
            批量OnOff
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.UserRoleController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserRoleController.#ctor(YunLanCrm.IServices.IUserRoleService,YunLanCrm.IServices.IUserService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.UserRoleInfo})">
            <summary>
            
            </summary>
            <param name="userRoleService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserRoleController.Add(YunLanCrm.Dto.UserRole.UserRoleAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserRoleController.Update(YunLanCrm.Dto.UserRole.UserRoleEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserRoleController.Delete(System.Int32)">
            <summary>
            删除
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserRoleController.DeleteMany(System.Object[])">
            <summary>
            
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserRoleController.Get(System.Int32)">
            <summary>
            获取一个信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserRoleController.Detail(System.Int32)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserRoleController.List(YunLanCrm.Dto.UserRole.UserRoleListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserRoleController.Query(YunLanCrm.Dto.UserRole.UserRolePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserRoleController.QueryPost(YunLanCrm.Dto.UserRole.UserRolePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserRoleController.PageQuery(YunLanCrm.Dto.UserRole.UserRolePageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserRoleController.GetRoleUsers(YunLanCrm.Dto.UserRole.UserRolePageQueryDto)">
            <summary>
            获取角色成员
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.UserRoleController.RemoveRoleUser(YunLanCrm.Dto.UserRole.RemoveRoleUserDto)">
            <summary>
            解除角色用户
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.WorkflowCrmController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.#ctor(YunLanCrm.IServices.IWorkflowCrmService,YunLanCrm.Common.HttpContextUser.IUser,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.WorkflowBusinessInfo},YunLanCrm.IRepositories.ICmTicketsRepository)">
            <summary>
            
            </summary>
            <param name="workflowCrmService"></param>
            <param name="user"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.GetEntityProperties(System.String)">
            <summary>
            获取实体类的属性
            </summary>
            <param name="className"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.GetWorkflowOperations(System.Int64,System.String)">
            <summary>
            获取流转记录
            </summary>
            <param name="crmWorkflowId"></param>
            <param name="workflowInstanceId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.WorkflowExecution(YunLanCrm.Dto.WorkflowExecutionLogRecords.WorkflowExecutionDto)">
            <summary>
            工作流执行
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.WorkflowDefinitions(YunLanCrm.WorkFlow.ScuiWorkflow)">
            <summary>
            获取自定义的工作流模型
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.GetActivityTypes">
            <summary>
            获取所有自定义活动的类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.GetReturnActivitys(System.String,System.String,System.Guid)">
            <summary>
            获取退回节点
            </summary>
            <param name="workflowInstanceId">实例</param>
            <param name="activityId">当前节点</param>
            <param name="taskId">当前任务</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.SaveWorkflowTemplate(YunLanCrm.Model.Dto.WorkflowCrm.WorkflowTemplateDto)">
            <summary>
            发布工作流
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.GetWorkflowPermissions(System.Int64,System.String)">
            <summary>
            获取流程操作权限
            </summary>
            <param name="crmWorkflowId"></param>
            <param name="workflowInstanceId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.GetLastActivity(System.String)">
            <summary>
            获取流程最后的执行节点
            </summary>
            <param name="workflowInstanceId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.GetCurrentApproval(System.String,System.Int32)">
            <summary>
            获取当前审批节点的相关信息
            </summary>
            <param name="workflowInstanceId"></param>
            <param name="userTrusteeId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.Add(YunLanCrm.Dto.WorkflowCrm.WorkflowCrmAddDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.Update(YunLanCrm.Dto.WorkflowCrm.WorkflowCrmEditDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.Delete(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="crmWorkflowId">crmWorkflowId</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.Get(System.Int64)">
            <summary>
            获取一个信息
            </summary>
            <param name="crmWorkflowId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.Detail(System.Int64)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="crmWorkflowId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.List(YunLanCrm.Dto.WorkflowCrm.WorkflowCrmListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.Query(YunLanCrm.Dto.WorkflowCrm.WorkflowCrmPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.QueryPost(YunLanCrm.Dto.WorkflowCrm.WorkflowCrmPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.PageQuery(YunLanCrm.Dto.WorkflowCrm.WorkflowCrmPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.WorkflowDone(YunLanCrm.Dto.WorkflowCrm.WorkflowCrmPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.WorkflowExecutionUserTask(System.String,System.String)">
            <summary>
            触发工作流 （测试）
            </summary>
            <param name="workflowInstanceId"></param>
            <param name="action"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowCrmController.TestWorkflow(System.String)">
            <summary>
            触发工作流（测试使用，signal模式）
            </summary>
            <param name="signalName"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.WebApi.Controllers.WorkflowDesignController">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowDesignController.#ctor(YunLanCrm.IServices.IWorkflowDesignService,Microsoft.Extensions.Logging.ILogger{YunLanCrm.Model.Models.WorkflowDesignInfo})">
            <summary>
            
            </summary>
            <param name="workflowDesignService"></param>
            <param name="logger"></param>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowDesignController.Add(YunLanCrm.Dto.WorkflowDesign.WorkflowDesignAddOrUpdateDto)">
            <summary>
            添加
            </summary>
            <param name="req">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowDesignController.Update(YunLanCrm.Dto.WorkflowDesign.WorkflowDesignAddOrUpdateDto)">
            <summary>
            修改
            </summary>
            <param name="req">信息</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowDesignController.Delete(System.Guid)">
            <summary>
            删除
            </summary>
            <param name="designId">designId</param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowDesignController.Delete(System.Object[])">
            <summary>
            批量删除
            </summary>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowDesignController.Get(System.Guid)">
            <summary>
            获取一个信息
            </summary>
            <param name="designId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowDesignController.WorkflowDesignSimples">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowDesignController.GetList(YunLanCrm.Dto.WorkflowDesign.WorkflowDesignListQueryDto)">
            <summary>
            获取列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowDesignController.Detail(System.Guid)">
            <summary>
            获取一个详细信息
            </summary>
            <param name="designId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowDesignController.DetailList(YunLanCrm.Dto.WorkflowDesign.WorkflowDesignListQueryDto)">
            <summary>
            获取详细列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowDesignController.Query(YunLanCrm.Dto.WorkflowDesign.WorkflowDesignPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowDesignController.QueryPost(YunLanCrm.Dto.WorkflowDesign.WorkflowDesignPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.WebApi.Controllers.WorkflowDesignController.PageQuery(YunLanCrm.Dto.WorkflowDesign.WorkflowDesignPageQueryDto)">
            <summary>
            获取列表，分页
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.Api.ElsaWorkflows.Activities.Approved.OnExecuteAsync(Elsa.Services.Models.ActivityExecutionContext)">
            <summary>
            第一次执行
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.Api.ElsaWorkflows.Activities.Approved.GetTaskCreator(Elsa.Services.Models.ActivityExecutionContext,System.Int32)">
            <summary>
            获取任务的创建人
            </summary>
            <param name="context"></param>
             <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.Api.ElsaWorkflows.Activities.Approved.OnResumeAsync(Elsa.Services.Models.ActivityExecutionContext)">
            <summary>
            活动恢复后执行的逻辑
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.Api.ElsaWorkflows.Activities.Approved.OnCanExecute(Elsa.Services.Models.ActivityExecutionContext)">
            <summary>
            判断操作是否可以执行，返回true 则触发 OnResumeAsync 时间
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.Api.ElsaWorkflows.Activities.Approved.GetTransferTypeName(Elsa.Services.Models.ActivityExecutionContext)">
            <summary>
            获取流转名称
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.Api.ElsaWorkflows.Activities.Approved.GetActivityBlueprintName(Elsa.Services.Models.IActivityBlueprint)">
            <summary>
            获取当前活动对应的名称
            </summary>
            <param name="activityBlueprint"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.Api.ElsaWorkflows.Activities.Approved.GetActivityName(Elsa.Models.ActivityDefinition)">
            <summary>
            获取当前活动对应的名称
            </summary>
            <param name="activity"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.Api.ElsaWorkflows.Activities.Approved.SetIsActionApprove(Elsa.Services.Models.ActivityExecutionContext)">
            <summary>
            判断是否是审批操作
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.Api.ElsaWorkflows.Activities.Approved.GetOperationType(Elsa.Services.Models.ActivityExecutionContext,YunLanCrm.Model.Dto.WorkflowCrm.DesignNodeProperty)">
            <summary>
            获取当前的操作类型
            </summary>
            <param name="context"></param>
            <param name="nodeConfig"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.Api.ElsaWorkflows.Activities.Approved.AllTaskUserFinished(System.String,System.Guid)">
            <summary>
            判断任务人是否都已经处理了
            </summary>
            <param name="workflowInstanceId"></param>
            <param name="taskId"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.Api.Filter.RESTfulResultProvider">
            <summary>
            RESTful 风格返回值
            </summary>
        </member>
        <member name="M:YunLanCrm.Api.Filter.RESTfulResultProvider.OnException(Microsoft.AspNetCore.Mvc.Filters.ExceptionContext,Furion.UnifyResult.Internal.ExceptionMetadata)">
            <summary>
            异常返回值
            </summary>
            <param name="context"></param>
            <param name="metadata"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.Api.Filter.RESTfulResultProvider.OnSucceeded(Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext,System.Object)">
            <summary>
            成功返回值
            </summary>
            <param name="context"></param>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.Api.Filter.RESTfulResultProvider.OnValidateFailed(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Furion.DataValidation.ValidationMetadata)">
            <summary>
            验证失败返回值
            </summary>
            <param name="context"></param>
            <param name="metadata"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.Api.Filter.RESTfulResultProvider.OnResponseStatusCodes(Microsoft.AspNetCore.Http.HttpContext,System.Int32,Furion.UnifyResult.UnifyResultSettingsOptions)">
            <summary>
            特定状态码返回值
            </summary>
            <param name="context"></param>
            <param name="statusCode"></param>
            <param name="unifyResultSettings"></param>
            <returns></returns>
        </member>
        <member name="M:YunLanCrm.Api.Filter.RESTfulResultProvider.RESTfulResult(System.Int32,System.Boolean,System.Object,System.Object)">
            <summary>
            返回 RESTful 风格结果集
            </summary>
            <param name="statusCode"></param>
            <param name="succeeded"></param>
            <param name="data"></param>
            <param name="errors"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.ElsaWorkflows.Activities.FinishActivity">
            <summary>
            流程结束节点
            </summary>
        </member>
        <member name="T:YunLanCrm.ElsaWorkflows.Activities.StartActivity">
            <summary>
            流程开始节点
            </summary>
        </member>
        <member name="M:YunLanCrm.ElsaWorkflows.Activities.StartActivity.GetActivityBlueprintName(Elsa.Services.Models.IActivityBlueprint)">
            <summary>
            获取当前活动对应的名称
            </summary>
            <param name="activityBlueprint"></param>
            <returns></returns>
        </member>
        <member name="T:YunLanCrm.SwaggerHelper.CustomRouteAttribute">
            <summary>
            自定义路由 /api/{version}/[controler]/[action]
            </summary>
        </member>
        <member name="P:YunLanCrm.SwaggerHelper.CustomRouteAttribute.GroupName">
            <summary>
            分组名称,是来实现接口 IApiDescriptionGroupNameProvider
            </summary>
        </member>
        <member name="M:YunLanCrm.SwaggerHelper.CustomRouteAttribute.#ctor(System.String)">
            <summary>
            自定义路由构造函数，继承基类路由
            </summary>
            <param name="actionName"></param>
        </member>
        <member name="M:YunLanCrm.SwaggerHelper.CustomRouteAttribute.#ctor(YunLanCrm.Extensions.CustomApiVersion.ApiVersions,System.String)">
            <summary>
            自定义版本+路由构造函数，继承基类路由
            </summary>
            <param name="actionName"></param>
            <param name="version"></param>
        </member>
        <member name="T:YunLanCrm.Filter.GlobalRouteAuthorizeConvention">
            <summary>
            Summary:全局路由权限公约
            Remarks:目的是针对不同的路由，采用不同的授权过滤器
            如果 controller 上不加 [Authorize] 特性，默认都是 Permission 策略
            否则，如果想特例其他授权机制的话，需要在 controller 上带上  [Authorize]，然后再action上自定义授权即可，比如 [Authorize(Roles = "Admin")]
            </summary>
        </member>
        <member name="T:YunLanCrm.Filter.GlobalAuthorizeFilter">
            <summary>
            全局权限过滤器【无效】
            </summary>
        </member>
        <member name="T:YunLanCrm.Filter.GlobalExceptionsFilter">
            <summary>
            全局异常错误日志
            </summary>
        </member>
        <member name="M:YunLanCrm.Filter.GlobalExceptionsFilter.WriteLog(System.String,System.Exception)">
            <summary>
            自定义返回格式
            </summary>
            <param name="throwMsg"></param>
            <param name="ex"></param>
            <returns></returns>
        </member>
        <member name="P:YunLanCrm.Filter.JsonErrorResponse.Message">
            <summary>
            生产环境的消息
            </summary>
        </member>
        <member name="P:YunLanCrm.Filter.JsonErrorResponse.DevelopmentMessage">
            <summary>
            开发环境的消息
            </summary>
        </member>
        <member name="T:YunLanCrm.Filter.GlobalRoutePrefixFilter">
            <summary>
            全局路由前缀公约
            </summary>
        </member>
        <member name="M:YunLanCrm.Program.SetLoggerConfiguration(Serilog.LoggerConfiguration)">
            <summary>
            
            </summary>
            <param name="config"></param>
        </member>
        <member name="M:YunLanCrm.Program.GetAppSettingsConfigName">
            <summary>
            根据环境变量定向配置文件名称
            </summary>
            <returns></returns>
        </member>
        <member name="P:YunLanCrm.Startup.Configuration">
            <summary>
            IConfiguration 加载配置值
            </summary>
        </member>
        <member name="P:YunLanCrm.Startup.Env">
            <summary>
            提供有关正在运行应用程序的Web托管环境的信息
            </summary>
        </member>
    </members>
</doc>
