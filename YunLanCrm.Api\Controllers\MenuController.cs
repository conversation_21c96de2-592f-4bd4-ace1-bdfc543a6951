﻿using Microsoft.AspNetCore.Mvc;
using LinqKit;
using Mapster;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.Menu;
using Microsoft.AspNetCore.Authorization;
using YunLanCrm.AuthHelper;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class MenuController : BaseApiController
    {
        private readonly ILogger<MenuInfo> _logger;

        private readonly IMenuService _menuService;

        private readonly IMenuApiService _menuApiService;

        private readonly PermissionRequirement _requirement;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="menuService"></param>
        /// <param name="menuApiService"></param>
        /// <param name="requirement"></param>
        /// <param name="logger"></param>
        public MenuController(IMenuService menuService, IMenuApiService menuApiService, PermissionRequirement requirement, ILogger<MenuInfo> logger)
        {
            this._menuService = menuService;
            this._logger = logger;
            this._menuApiService = menuApiService;
            _requirement = requirement;
        }

        /// <summary>
        /// 把前台写好的路由配置同步给后台数据库
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost("SynRoute")]
        [NonAction]
        public async Task<long> SynRoute(List<RouteTreeDto> req)
        {


            if (req == null || req.Count == 0)
            {
                return -1;
            }

            await GetSub(req, 0);

            return 0;

        }

        private async Task<int> GetSub(List<RouteTreeDto> data, int pid)
        {
            List<MenuInfo> list = new List<MenuInfo>();
            foreach (var item in data)
            {
                var obj = new MenuInfo();
                obj.ParentId = pid;
                if (pid == 0)
                {
                    obj.Component = "layout/routerView/parent";
                }
                else if (item.component != null && item.component.IndexOf("layout/routerView/parent") > 0)
                {
                    obj.Component = "layout/routerView/parent";
                }
                else
                {
                    if (item.component != null && item.component.StartsWith("() => import('/src/views/"))
                    {
                        obj.Component = item.component.Replace("() => import('/src/views/", "");
                        obj.Component = obj.Component.Substring(0, obj.Component.Length - 1);
                    }
                }

                obj.Name = item.name.Trim();
                obj.Path = item.path;
                obj.Icon = item.meta.icon;
                obj.IsAffix = item.meta.isAffix;
                obj.Title = item.meta.title;
                obj.IsHide = item.meta.isHide;
                obj.IsLink = item.meta.isLink;
                obj.IsIframe = item.meta.isIframe;
                obj.IsKeepAlive = item.meta.isKeepAlive;
                obj.MenuType = 1;
                if (item.children != null && item.children.Count > 0)
                {
                    obj.MenuType = 2;
                }



                if (!(await _menuService.AnyAsync(a => a.Name.ToLower() == obj.Name.ToLower())))
                {
                    obj.Id = (await _menuService.AddIdentity(obj)).ObjToInt();
                }
                if (item.children != null && item.children.Count > 0)
                {
                    await GetSub(item.children, obj.Id);
                }

            }

            return 0;
        }


        /// <summary>
        /// 获取用户的后台菜单
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("WebMenus")]
        public async Task<List<RouteTreeDto>> WebMenus([FromQuery] RouteTreeInput req)
        {
            //CurrentUser.UserId
            var list = await _menuService.GetUserMenus(CurrentUser.UserId);

            return list;
        }

        /// <summary>
        /// 获取功能模块对应的权限 Table
        /// </summary>
        /// <returns></returns>
        [HttpGet("PermissionTable")]
        public async Task<List<PermissionTreeDto>> PermissionTable()
        {
            List<PermissionTreeDto> list = new List<PermissionTreeDto>();
            var where = PredicateBuilder.New<MenuInfo>(true);

            //where.And(a => a.MenuType != 3);

            var orderBy = new OrderBy<MenuInfo>(a => a.Sort, SortDirection.Ascending);
            var data = await _menuService.QueryList(where, orderBy);
            var pages = data.Where(a => a.MenuType != 3);
            foreach (var item in pages)
            {
                var obj = item.Adapt<PermissionTreeDto>();
                obj.Children = new List<PermissionTreeDto>();

                var permissions = data.Where(a => a.ParentId == item.Id && a.MenuType == 3);

                foreach (var per in permissions)
                {
                    var item1 = per.Adapt<PermissionTreeDto>();

                    obj.Children.Add(item1);
                }
                list.Add(obj);
            }
            return list;
        }

        #region 不考虑这种方式 获取功能模块对应的权限Tree
        /// <summary>
        /// 获取功能模块对应的权限Tree
        /// </summary>
        /// <returns></returns>
        [HttpGet("PermissionTree")]
        public async Task<List<PermissionTreeDto>> PermissionTree()
        {

            var where = PredicateBuilder.New<MenuInfo>(true);
            var orderBy = new OrderBy<MenuInfo>(a => a.Sort, SortDirection.Ascending);
            var data = await _menuService.QueryList<PermissionTreeDto>(where, orderBy);

            var list = _menuService.ParsePermissionTree(data, 0);

            return list;
        }
        #endregion

        /// <summary>
        /// 
        /// </summary>
        /// <param name="parentId"></param>
        /// <param name="menuType"></param>
        /// <returns></returns>
        [HttpGet("Tree")]
        public async Task<List<MenuTreeDto>> Tree(long? parentId, int? menuType)
        {
            var where = PredicateBuilder.New<MenuInfo>(true);

            //where.And(a => a.MenuType != 3);

            if (parentId.HasValue)
            {
                //where.And(a => a.ParentId == parentId.Value);
            }
            if (menuType.HasValue)
            {
                //where.And(a => a.MenuType == menuType);
            }
            var orderBy = new OrderBy<MenuInfo>(a => a.Sort, SortDirection.Ascending);
            var data = await _menuService.QueryList<MenuTreeDto>(where, orderBy);

            return _menuService.ParseTree(data, 0);
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(MenuAddDto req)
        {
            if ((await _menuService.AnyAsync(a => a.ParentId == req.ParentId && (a.Name == req.Name || a.Title == req.Title))))
            {
                throw new Exception("菜单名称已存在");
            }
            long menuId = await _menuService.AddIdentity(req.Adapt<MenuInfo>());

            if (req.DefaultBtn && req.MenuType != 3)
            {
                var permissions = FunctionInfo.GetDefaultFunctions();
                foreach (var permission in permissions)
                {
                    MenuInfo menuInfo = new MenuInfo();
                    menuInfo.MenuType = 3;
                    menuInfo.Name = string.Format("{0}.{1}", req.Name, permission.Name);
                    menuInfo.Title = permission.Title;
                    menuInfo.ParentId = menuId.ObjToInt();
                    menuInfo.Identification = string.Format("{0}.{1}", req.Name, permission.Name);
                    menuInfo.CreateAt = DateTime.Now;
                    menuInfo.IsLink = "";
                    await _menuService.Add(menuInfo);
                }
            }

            if (menuId > 0 && req.Apis != null && req.Apis.Count > 0)
            {
                var list = new List<MenuApiInfo>();
                req.Apis.ForEach(item =>
                {
                    list.Add(new MenuApiInfo()
                    {
                        MenuId = menuId,
                        ApiId = item
                    });
                });
                //批量添加
                await _menuApiService.Add(list);

            }

            return menuId;
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(MenuEditDto req)
        {
            bool result = await _menuService.Update(req.Adapt<MenuInfo>());

            if (result)
            {
                await _menuApiService.Delete(a => a.MenuId == req.Id);

                if (req.Apis != null && req.Apis.Count > 0)
                {
                    var list = new List<MenuApiInfo>();
                    req.Apis.Distinct().ForEach(item =>
                    {
                        list.Add(new MenuApiInfo()
                        {
                            MenuId = req.Id,
                            ApiId = item
                        });
                    });
                    //批量添加
                    await _menuApiService.Add(list);
                }
                _requirement.Permissions.Clear();
            }

            return result;
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _menuService.Delete(a => a.Id == id);
            if (result > 0)
            {
                await _menuService.Delete(a => a.ParentId == id);
            }
            return result > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        [NonAction]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _menuService.DeleteByIds(items);

            return result;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<MenuDto> Get(long id)
        {
            return await _menuService.QueryInfo<MenuDto>(a => a.Id == id);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<MenuDetailDto> Detail(long id)
        {
            var obj = await _menuService.QueryInfo(a => a.Id == id);

            if (obj != null)
            {
                return _menuService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("List")]
        [NonAction]
        public async Task<List<MenuListDto>> List([FromQuery] MenuListQueryDto req)
        {
            var list = new List<MenuListDto>();
            var where = PredicateBuilder.New<MenuInfo>(true);
            if (req.parentId.HasValue)
            {
                where.And(a => a.ParentId == req.parentId.Value);
            }
            if (req.menuType.HasValue)
            {
                where.And(a => a.MenuType == req.menuType.Value);
            }
            var orderBy = new OrderBy<MenuInfo>(req.order, req.sort);
            var data = await _menuService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _menuService.Join(item);
                list.Add(detail.Adapt<MenuListDto>());
            }
            #endregion

            #region  生成测试数据

            /* foreach (var item in list)
             {
                 if (item.MenuType != 3)
                 {
                     var permissions = FunctionInfo.GetDefaultFunctions();
                     foreach (var permission in permissions)
                     {
                         MenuInfo menuInfo = new MenuInfo();
                         menuInfo.MenuType = 3;
                         menuInfo.Name = permission.Name;
                         menuInfo.Title = permission.Title;
                         menuInfo.ParentId = item.Id.ObjToInt();
                         menuInfo.Identification = string.Format("{0}.{1}", item.Name, permission.Name);
                         menuInfo.CreateAt = DateTime.Now;
                         menuInfo.IsLink = "";
                         menuInfo.Description = permission.Description;
                         await _menuService.Add(menuInfo);
                     }
                 }
             }*/

            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        [NonAction]
        public async Task<PageQueryResult<MenuListDto>> Query([FromQuery] MenuPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        [NonAction]
        public async Task<PageQueryResult<MenuListDto>> QueryPost(MenuPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<MenuListDto>> PageQuery([FromQuery] MenuPageQueryDto req)
        {
            var list = new List<MenuListDto>();
            var where = PredicateBuilder.New<MenuInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            var totalCount = await _menuService.CountAsync(where);
            var orderBy = new OrderBy<MenuInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _menuService.QueryList(where, orderBy, paging);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _menuService.Join(item);
                list.Add(detail.Adapt<MenuListDto>());
            }
            #endregion

            return new PageQueryResult<MenuListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

    }
}