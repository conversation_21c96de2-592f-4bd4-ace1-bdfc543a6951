﻿<template>
	<div class="roleModulePermission-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="500px">
			<el-form ref="ruleFormRef" label-width="90px" label-position="top">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="角色名称">
							<el-input v-model="roleObj.name" disabled></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="菜单权限" prop="createId">
							<div class="treeMain">
								<el-tree
									ref="treeRef"
									:data="treePermissionData"
									show-checkbox
									default-expand-all
									node-key="id"
									highlight-current
									:props="{ children: 'children', label: 'name' }"
									:default-checked-keys="treeCheckData"
								/>
							</div>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">Cannel</el-button>

					<el-button :loading="loading" type="primary" @click="onSubmit" size="small">Save</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, ref } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';

import roleModulePermissionApi from '/@/api/roleModulePermission';
import menuApi from '/@/api/menu/list';

export default {
	name: 'roleModulePermissionCreateOrEdit',
	components: {},
	setup() {
		const { proxy } = getCurrentInstance() as any;
		const treeRef = ref();
		const state = reactive({
			title: '角色权限设置',
			isShowDialog: false,
			saveLoading: false,
			roleObj: { id: 0 }, //角色信息
			treePermissionData: [],
			treeCheckData: [], //已勾选
		});
		// 打开弹窗
		const openDialog = (parmas: any) => {
			state.roleObj = parmas;
			state.isShowDialog = true;
			onInitForm();

			menuApi.Tree().then((rs) => {
				state.treePermissionData = rs.data;
			});

			roleModulePermissionApi.GetPermissionIdByRoleId(state.roleObj.id).then((rs) => {
				state.treeCheckData = rs.data;
			});
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			var item1 = treeRef.value.getHalfCheckedKeys();
			var item2 = treeRef.value!.getCheckedKeys();
			var items = [...item1, ...item2];
			roleModulePermissionApi
				.Assign({ menus: items, roleId: state.roleObj.id })
				.then(() => {
					ElMessage.success('Succeed');
					closeDialog();
				})
				.finally(() => {
					state.saveLoading = false;
				});
		};
		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			state.treeCheckData = [];
			state.treePermissionData = [];
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onInitForm,
			treeRef,
			...toRefs(state),
		};
	},
};
</script>
<style scoped>
.treeMain {
	height: 280px;
	overflow: auto;
	border: 1px solid #dcdfe6;
	margin-bottom: 10px;
	width: 500px;
}
</style>
