﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmUserCompany;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmUserCompanyController : ControllerBase
    {
        private readonly ILogger<CmUserCompanyInfo> _logger;
        private readonly ICmUserCompanyService _cmUserCompanyService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmUserCompanyService"></param>
        /// <param name="logger"></param>
        public CmUserCompanyController(ICmUserCompanyService cmUserCompanyService, ILogger<CmUserCompanyInfo> logger)
        {
            _logger = logger;
            _cmUserCompanyService = cmUserCompanyService;
            
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CmUserCompanyAddOrUpdateDto req)
        {
            return await _cmUserCompanyService.AddCmUserCompany(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmUserCompanyAddOrUpdateDto req)
        {
            return await _cmUserCompanyService.UpdateCmUserCompany(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _cmUserCompanyService.Delete(a => a.Id == id);

            return result > 0;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _cmUserCompanyService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmUserCompanyDto> Get(long id)
        {
            return await _cmUserCompanyService.QueryInfo<CmUserCompanyDto>(a => a.Id == id);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmUserCompanyDto>> GetList([FromQuery] CmUserCompanyListQueryDto req)
        {
            return await _cmUserCompanyService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<CmUserCompanyDetailDto> Detail(long id)
        {
            return await _cmUserCompanyService.Detail(id);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmUserCompanyDetailDto>> DetailList([FromQuery] CmUserCompanyListQueryDto req)
        {
            return await _cmUserCompanyService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmUserCompanyDetailDto>> Query([FromQuery] CmUserCompanyPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmUserCompanyDetailDto>> QueryPost(CmUserCompanyPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmUserCompanyDetailDto>> PageQuery([FromQuery] CmUserCompanyPageQueryDto req)
        {
            return await _cmUserCompanyService.PageQueryView(req);
        }

    }
}