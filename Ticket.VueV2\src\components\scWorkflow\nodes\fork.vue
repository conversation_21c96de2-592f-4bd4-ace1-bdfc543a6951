<template>
	<div class="branch-wrap">
		<div class="branch-box-wrap">
			<div class="branch-box">
				<el-button class="add-branch" type="success" plain round @click="addTerm">添加分支</el-button>
				<div class="col-box" v-for="(item, index) in nodeConfig.forkNodes" :key="index">
					<div class="condition-node">
						<div class="condition-node-box">
							<div class="auto-judge" @click="show(index)">
								<div class="sort-left" v-if="index != 0" @click.stop="arrTransfer(index, -1)">
									<el-icon><ele-ArrowLeft /></el-icon>
								</div>
								<div class="title">
									<span class="node-title">{{ item.nodeName }}</span>
									<span class="priority-title"></span>
									<el-icon class="close" @click.stop="delTerm(index)"><ele-Close /></el-icon>
								</div>
								<div class="content">
									<span v-if="toText(nodeConfig, index)">{{ toText(nodeConfig, index) }}</span>
									<span v-else class="placeholder">请设置审批人</span>
								</div>
								<div class="sort-right" v-if="index != nodeConfig.forkNodes.length - 1" @click.stop="arrTransfer(index)">
									<el-icon><ele-ArrowRight /></el-icon>
								</div>
							</div>
							<add-node v-model="item.childNode"></add-node>
						</div>
					</div>
					<slot v-if="item.childNode" :node="item"></slot>
					<div class="top-left-cover-line" v-if="index == 0"></div>
					<div class="bottom-left-cover-line" v-if="index == 0"></div>
					<div class="top-right-cover-line" v-if="index == nodeConfig.forkNodes.length - 1"></div>
					<div class="bottom-right-cover-line" v-if="index == nodeConfig.forkNodes.length - 1"></div>
				</div>
			</div>
			<add-node v-model="nodeConfig.childNode"></add-node>
		</div>
		<el-drawer title="审批人设置" v-model="drawer" destroy-on-close append-to-body :size="500">
			<template #header>
				<div class="node-wrap-drawer__title">
					<label @click="editTitle" v-if="!isEditTitle"
						>{{ form.nodeName }}<el-icon class="node-wrap-drawer__title-edit"><el-icon-edit /></el-icon
					></label>
					<el-input v-if="isEditTitle" ref="nodeTitle" v-model="form.nodeName" clearable @blur="saveTitle" @keyup.enter="saveTitle"></el-input>
				</div>
			</template>
			<el-container>
				<el-main style="padding: 0 20px 20px 20px">
					<el-form label-position="top">
						<el-form-item label="审批人员类型">
							<el-select v-model="form.setType">
								<el-option :value="1" label="指定成员"></el-option>
								<el-option :value="2" label="主管"></el-option>
								<el-option :value="3" label="角色"></el-option>
								<el-option :value="4" label="发起人自选"></el-option>
								<el-option :value="5" label="发起人自己"></el-option>
								<el-option :value="7" label="连续多级主管"></el-option>
							</el-select>
						</el-form-item>

						<el-form-item v-if="form.setType == 1" label="选择成员">
							<el-button type="primary" icon="el-icon-plus" round @click="selectHandle(1, form.nodeUserList)">选择人员</el-button>
							<div class="tags-list">
								<el-tag v-for="(user, index) in form.nodeUserList" :key="user.id" closable @close="delUser(index)">{{ user.name }}</el-tag>
							</div>
						</el-form-item>

						<el-form-item v-if="form.setType == 2" label="指定主管">
							发起人的第 <el-input-number v-model="form.examineLevel" :min="1" /> 级主管
						</el-form-item>

						<el-form-item v-if="form.setType == 3" label="选择角色">
							<el-button type="primary" icon="el-icon-plus" round @click="selectHandle(2, form.nodeRoleList)">选择角色</el-button>
							<div class="tags-list">
								<el-tag v-for="(role, index) in form.nodeRoleList" :key="role.id" type="info" closable @close="delRole(index)">{{
									role.name
								}}</el-tag>
							</div>
						</el-form-item>

						<el-form-item v-if="form.setType == 4" label="发起人自选">
							<el-radio-group v-model="form.selectMode">
								<el-radio :label="1">自选一个人</el-radio>
								<el-radio :label="2">自选多个人</el-radio>
							</el-radio-group>
						</el-form-item>

						<el-form-item v-if="form.setType == 7" label="连续主管审批终点">
							<el-radio-group v-model="form.directorMode">
								<el-radio :label="0">直到最上层主管</el-radio>
								<el-radio :label="1">自定义审批终点</el-radio>
							</el-radio-group>
							<p v-if="form.directorMode == 1">直到发起人的第 <el-input-number v-model="form.directorLevel" :min="1" /> 级主管</p>
						</el-form-item>

						<el-divider></el-divider>
						<el-form-item label="">
							<el-checkbox v-model="form.termAuto" label="超时自动审批"></el-checkbox>
						</el-form-item>
						<template v-if="form.termAuto">
							<el-form-item label="审批期限（为 0 则不生效）"> <el-input-number v-model="form.term" :min="0" /> 小时 </el-form-item>
							<el-form-item label="审批期限超时后执行">
								<el-radio-group v-model="form.termMode">
									<el-radio :label="0">自动通过</el-radio>
									<el-radio :label="1">自动拒绝</el-radio>
								</el-radio-group>
							</el-form-item>
						</template>
						<el-divider></el-divider>
						<el-form-item label="多人审批时审批方式">
							<el-radio-group v-model="form.examineMode">
								<p style="width: 100%"><el-radio :label="1">按顺序依次审批</el-radio></p>
								<p style="width: 100%"><el-radio :label="2">会签 (可同时审批，每个人必须审批通过)</el-radio></p>
								<p style="width: 100%"><el-radio :label="3">或签 (有一人审批通过即可)</el-radio></p>
							</el-radio-group>
						</el-form-item>
						<el-divider></el-divider>
						<el-form-item label="节点类型">
							<el-select v-model="form.typeName" placeholder="请选择" clearable class="w100">
								<el-option label="InitWorkflow" value="InitWorkflow"></el-option>
								<el-option label="Approved" value="Approved"></el-option>
								<el-option label="InvoiceIndexingCancelled" value="InvoiceIndexingCancelled"></el-option>
								<el-option label="Rejected" value="Rejected"></el-option>
								<el-option label="Transferred" value="Transferred"></el-option>
								<el-option label="InvoiceIndexingClosed" value="InvoiceIndexingClosed"></el-option>
								<el-option label="InvoiceIndexingDraft" value="InvoiceIndexingDraft"></el-option>
								<el-option label="InvoiceIndexingInitWorkflow" value="InvoiceIndexingInitWorkflow"></el-option>
								<el-option label="InvoiceIndexingPendingApproval" value="InvoiceIndexingPendingApproval"></el-option>
								<el-option label="InvoiceIndexingReadyToAudit" value="InvoiceIndexingReadyToAudit"></el-option>
								<el-option label="InvoiceIndexingReadyToPay" value="InvoiceIndexingReadyToPay"></el-option>
								<el-option label="InvoiceIndexingReadyToPost" value="InvoiceIndexingReadyToPost"></el-option>
								<el-option label="InvoiceIndexingRejected" value="InvoiceIndexingRejected"></el-option>
							</el-select>
						</el-form-item>
						<el-divider></el-divider>
						<el-form-item label="触发器名称">
							<el-input placeholder="Please input name" v-model="form.execute.name"></el-input>
						</el-form-item>
						<el-form-item label="触发器显示名称">
							<el-input placeholder="Please input display name" v-model="form.execute.displayName"></el-input>
						</el-form-item>
					</el-form>
				</el-main>
				<el-footer>
					<el-button type="primary" @click="save">保存</el-button>
					<el-button @click="drawer = false">取消</el-button>
				</el-footer>
			</el-container>
		</el-drawer>
	</div>
</template>

<script>
import addNode from './addNode.vue';
import { v4 as uuidv4 } from 'uuid';
export default {
	props: {
		modelValue: { type: Object, default: () => {} },
	},
	components: {
		addNode,
	},
	data() {
		return {
			nodeConfig: {},
			drawer: false,
			isEditTitle: false,
			index: 0,
			form: {},
		};
	},
	watch: {
		modelValue() {
			this.nodeConfig = this.modelValue;
		},
	},
	mounted() {
		this.nodeConfig = this.modelValue;
	},
	methods: {
		show(index) {
			this.index = index;
			this.form = {};
			this.form = JSON.parse(JSON.stringify(this.nodeConfig.forkNodes[index]));
			this.drawer = true;
		},
		editTitle() {
			this.isEditTitle = true;
			this.$nextTick(() => {
				this.$refs.nodeTitle.focus();
			});
		},
		saveTitle() {
			this.isEditTitle = false;
		},
		save() {
			this.nodeConfig.forkNodes[this.index] = this.form;
			this.$emit('update:modelValue', this.nodeConfig);
			this.drawer = false;
		},
		addTerm() {
			let len = this.nodeConfig.forkNodes.length + 1;
			this.nodeConfig.forkNodes.push({
				nodeId: uuidv4(),
				nodeName: '审批节点' + len,
				displayName: '审核人',
				control:'Fork',
				type: 1, //节点类型
				typeName: '',
				setType: 1, //审核人类型
				nodeUserList: [], //审核人成员
				nodeRoleList: [], //审核角色
				examineLevel: 1, //指定主管层级
				directorLevel: 1, //自定义连续主管审批层级
				selectMode: 1, //发起人自选类型
				termAuto: false, //审批期限超时自动审批
				term: 0, //审批期限
				termMode: 1, //审批期限超时后执行类型
				examineMode: 1, //多人审批时审批方式
				directorMode: 0, //连续主管审批方式
				childNode: null,
				execute: { activityId: uuidv4(), name: '', displayName: '', type: 'SignalReceived' },
			});
		},
		delTerm(index) {
			this.nodeConfig.forkNodes.splice(index, 1);
			if (this.nodeConfig.forkNodes.length == 1) {
				if (this.nodeConfig.childNode) {
					if (this.nodeConfig.forkNodes[0].childNode) {
						this.reData(this.nodeConfig.forkNodes[0].childNode, this.nodeConfig.childNode);
					} else {
						this.nodeConfig.forkNodes[0].childNode = this.nodeConfig.childNode;
					}
				}
				this.$emit('update:modelValue', this.nodeConfig.forkNodes[0].childNode);
			}
		},
		reData(data, addData) {
			if (!data.childNode) {
				data.childNode = addData;
			} else {
				this.reData(data.childNode, addData);
			}
		},
		arrTransfer(index, type = 1) {
			this.nodeConfig.conditionNodes[index] = this.nodeConfig.conditionNodes.splice(index + type, 1, this.nodeConfig.conditionNodes[index])[0];
			this.nodeConfig.conditionNodes.map((item, index) => {
				item.priorityLevel = index + 1;
			});
			this.$emit('update:modelValue', this.nodeConfig);
		},
		addConditionList() {
			this.form.conditionList.push({
				label: '',
				field: '',
				operator: '=',
				value: '',
			});
		},
		deleteConditionList(index) {
			this.form.conditionList.splice(index, 1);
		},
		toText(nodeConfig, index) {
			var node = nodeConfig.forkNodes[index];
			if (node.setType == 1) {
				if (node.nodeUserList && node.nodeUserList.length > 0) {
					const users = node.nodeUserList.map((item) => item.name).join('、');
					return users;
				} else {
					return false;
				}
			} else if (node.setType == 2) {
				return node.examineLevel == 1 ? '直接主管' : `发起人的第${node.examineLevel}级主管`;
			} else if (node.setType == 3) {
				if (node.nodeRoleList && node.nodeRoleList.length > 0) {
					const roles = node.nodeRoleList.map((item) => item.name).join('、');
					return '角色-' + roles;
				} else {
					return false;
				}
			} else if (node.setType == 4) {
				return '发起人自选';
			} else if (node.setType == 5) {
				return '发起人自己';
			} else if (node.setType == 7) {
				return '连续多级主管';
			}
		},
	},
};
</script>

<style>
</style>
