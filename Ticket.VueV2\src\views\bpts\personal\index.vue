<template>
	<div class="personal" style="height: 100%">
		<el-row :gutter="10" style="height: 100%">
			<!-- 左边 -->
			<el-col :span="24" :sm="6" class="personal-info">
				<el-card shadow="hover" header="Personal info" v-if="false">
					<div class="personal-user">
						<div class="personal-user-left">
							<!-- <el-upload class="h100 personal-user-left-upload"
								action="https://jsonplaceholder.typicode.com/posts/" multiple :limit="1">
								<img
									src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201512%2F26%2F20151226131916_Qhu5t.jpeg&refer=http%3A%2F%2Fb-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1696144339&t=f342ec4d0eb8fbf0f32b6f0f7bf4aef1" />
							</el-upload> -->
							<div class="h100 personal-user-left-upload">
								<img
									src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201512%2F26%2F20151226131916_Qhu5t.jpeg&refer=http%3A%2F%2Fb-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1696144339&t=f342ec4d0eb8fbf0f32b6f0f7bf4aef1" />
							</div>
						</div>
						<div class="personal-user-right">
							<el-row>
								<el-col :span="24" class="personal-title mb6">{{ personalForm.realName }} </el-col>
								<el-col :span="24" class="personal-item mb6">
									<div class="personal-item-label">
										<el-text>{{ $t('message.userFields.userName') }}:</el-text>
									</div>
									<div class="personal-item-value">{{ personalForm.userName }}</div>
								</el-col>
								<el-col :span="24" class="personal-item mb6">
									<div class="personal-item-label">
										<el-text>{{ $t('message.userFields.lastLoginIp') }}:</el-text>
									</div>
									<div class="personal-item-value">{{ personalForm.lastLoginIp }}</div>
								</el-col>
								<el-col :span="24" class="personal-item mb6">
									<div class="personal-item-label">
										<el-text>{{ $t('message.userFields.lastLoginTime') }}:</el-text>
									</div>
									<div class="personal-item-value">{{ personalForm.lastLoginTime }}</div>
								</el-col>
							</el-row>
						</div>
					</div>
				</el-card>
				<!-- 消息通知 -->
				<!-- <el-card shadow="hover">
					<template #header>
						<span>消息通知</span>
						<span class="personal-info-more">更多</span>
					</template>
<div class="personal-info-box">
	<ul class="personal-info-ul">
		<li v-for="(v, k) in newsInfoList" :key="k" class="personal-info-li">
			<a :href="v.link" target="_block" class="personal-info-li-title">{{ v.title }}</a>
		</li>
	</ul>
</div>
</el-card> -->

				<el-card shadow="hover" class="personal-edit" header="Personal info"
					style="height: 100%; margin-right: -8px">
					<div class="personal-edit-safe-box">
						<div class="personal-edit-safe-item">
							<div class="personal-edit-safe-item-left">
								<div class="personal-edit-safe-item-left-label">
									<el-text>{{ $t('message.userFields.userName') }}</el-text>
								</div>
								<div class="personal-edit-safe-item-left-value">
									<el-input v-model="personalForm.userName" disabled="disabled" />
								</div>
							</div>
							<div class="personal-edit-safe-item-right">
								<el-button type="text"
									disabled="disabled">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</el-button>
							</div>
						</div>
					</div>
					<div class="personal-edit-safe-box">
						<div class="personal-edit-safe-item">
							<div class="personal-edit-safe-item-left">
								<div class="personal-edit-safe-item-left-label">
									<el-text>{{ $t('message.userFields.realName') }}</el-text>
								</div>
								<div class="personal-edit-safe-item-left-value">
									<el-text class="mx-1">
										<el-form ref="ruleFormRefrealName" :rules="personalForm.rules"
											:model="personalForm">
											<el-form-item prop="realName">
												<el-input v-model="personalForm.realName" placeholder="Please input" />
											</el-form-item>
										</el-form>
									</el-text>
								</div>
							</div>
							<div class="personal-edit-safe-item-right">
								<el-button type="text" @click="onchangeFirstName(personalForm.realName)">{{
									$t('message.userButtons.Save') }}</el-button>
							</div>
						</div>
					</div>
					<div class="personal-edit-safe-box">
						<div class="personal-edit-safe-item">
							<div class="personal-edit-safe-item-left">
								<div class="personal-edit-safe-item-left-label">
									<el-text>{{ $t('message.userFields.lastName') }}</el-text>
								</div>
								<div class="personal-edit-safe-item-left-value">
									<el-text class="mx-1">
										<el-form ref="ruleFormReflastName" :rules="personalForm.rules"
											:model="personalForm">
											<el-form-item prop="lastName">
												<el-input v-model="personalForm.lastName" placeholder="Please input" />
											</el-form-item>
										</el-form>
									</el-text>
								</div>
							</div>
							<div class="personal-edit-safe-item-right">
								<el-button type="text" @click="onchangeLastName(personalForm.lastName)">{{
									$t('message.userButtons.Save') }}</el-button>
							</div>
						</div>
					</div>
					<div class="personal-edit-safe-box">
						<div class="personal-edit-safe-item">
							<div class="personal-edit-safe-item-left">
								<div class="personal-edit-safe-item-left-label">
									<el-text>{{ $t('message.userFields.switchLanguage') }}</el-text>
								</div>
								<div class="personal-edit-safe-item-left-value">
									<el-text class="mx-1">
										<el-form :model="personalForm">
											<el-form-item prop="language">
												<el-select v-model="personalForm.language" class="m-2"
													placeholder="Select">
													<el-option label="简体中文" value="zh-cn"></el-option>
													<el-option label="English" value="en"></el-option>
												</el-select>
											</el-form-item>
										</el-form>
									</el-text>
								</div>
							</div>
							<div class="personal-edit-safe-item-right">
								<el-button type="text" @click="onchangeLanguage(personalForm.language)">{{
									$t('message.userButtons.Save') }}</el-button>
							</div>
						</div>
					</div>
					<div class="personal-edit-safe-box">
						<div class="personal-edit-safe-item">
							<div class="personal-edit-safe-item-left">
								<div class="personal-edit-safe-item-left-label">Security Settings</div>
								<div class="personal-edit-safe-item-left-value">
									<el-text class="mx-1">password update</el-text>
								</div>
							</div>
							<el-dialog v-model="changePwdRef" :title="$t('message.page.changePwd')" :width="500"
								@close="handleClose">
								<el-form ref="changePwdFormRef" :rules="changePwdRules" :model="form"
									label-position="left" label-width="140px" style="height: 100px">
									<el-row>
										<el-col>
											<el-form-item :label="$t('message.page.resetPwd')" prop="pwd">
												<el-input v-model="form.pwd" type="password" show-password clearable
													style="width: 300px" />
											</el-form-item>
										</el-col>
										<el-col style="margin-top: 20px">
											<el-form-item :label="$t('message.page.confirmPwd')" prop="confirmPwd">
												<el-input v-model="form.confirmPwd" type="password" show-password
													clearable style="width: 300px" />
											</el-form-item>
										</el-col>
									</el-row>
								</el-form>
								<div style="color: red; size: 12; margin-top: 20px">
									<p>Minimum of 8 characters in length,must contain a combination of at least any two
										of:</p>
									<p>1: upper/lower case mix.</p>
									<p>2: special character (~ ! @ # $ % ^ & * ( ) _ +).</p>
									<p>3: numeric</p>
								</div>
								<template #footer>
									<span class="dialog-footer">
										<el-button @click="handleClose">{{ $t('message.page.buttonCancel')
											}}</el-button>
										<el-button type="primary" @click="onSubmitChangePwd">
											{{ $t('message.page.changePwd') }}
										</el-button>
									</span>
								</template>
							</el-dialog>
							<div class="personal-edit-safe-item-right">
								<el-button type="text" @click="onChangePwd">{{ $t('message.userButtons.Modify')
									}}</el-button>
							</div>
						</div>
					</div>

					<div class="personal-edit-safe-box" v-if="personalForm.hasDeleteList">
						<div class="personal-edit-safe-item">
							<div class="personal-edit-safe-item-left">
								<div class="personal-edit-safe-item-left-label">Delegate Settings</div>
								<div class="personal-edit-safe-item-left-value">
									<el-text class="mx-1">Delegate</el-text>
								</div>
							</div>
							<div class="personal-edit-safe-item-right">
								<el-button type="text" @click="onShowDelegate">{{ $t('message.userButtons.Modify')
									}}</el-button>
							</div>
							
						</div>
					</div>
					<delegateList ref="delegateUserRef" />
					<div class="personal-edit-safe-box">
						<div class="personal-edit-safe-item">
							<div class="personal-edit-safe-item-left">
								<div class="personal-edit-safe-item-left-label">
									<el-text>{{ $t('message.PersonalTitle.MyEmailAddress') }}</el-text>
								</div>
								<div class="personal-edit-safe-item-left-value">
									<el-text class="mx-1">
										<el-form ref="ruleFormReflastEmail" :rules="personalForm.rules"
											:model="personalForm">
											<el-form-item prop="email">
												<el-input v-model="personalForm.email" placeholder="Please input" />
											</el-form-item>
										</el-form>
									</el-text>
								</div>
							</div>
							<div class="personal-edit-safe-item-right">
								<el-button type="text" @click="onchangeEmail(personalForm.email)">{{
									$t('message.userButtons.Save')
								}}</el-button>
							</div>
						</div>
					</div>
					<div class="personal-edit-safe-box">
						<div class="personal-edit-safe-item">
							<div class="personal-edit-safe-item-left">
								<div class="personal-edit-safe-item-left-label">Preferences</div>
								<div class="personal-edit-safe-item-left-value">Incident Email Notfications</div>
							</div>
							<div class="personal-edit-safe-item-right">
								<el-button type="text"><el-switch v-model="personalForm.isEmail" class="ml-2"
										:label="$t('message.groupFields.Default')"
										@change="onSubmitDefalutEmail" /></el-button>
							</div>
						</div>
					</div>
					<div class="personal-edit-safe-box">
						<div class="personal-edit-safe-item">
							<div class="personal-edit-safe-item-left">
								<div class="personal-edit-safe-item-left-label">{{ $t('message.userFields.orgName') }}
								</div>
								<div class="personal-edit-safe-item-left-value">
									<el-form-item prop="orgName">
										<el-select v-model="personalForm.orgName"
											:placeholder="$t('message.page.selectKeyPlaceholder')" clearable
											disabled="disabled" class="w100">
											<el-option v-for="item in personalForm.orgNameData" :label="item.label"
												:value="item.value" :key="item.orgId"></el-option></el-select>
									</el-form-item>
								</div>
							</div>
							<div class="personal-edit-safe-item-right">
								<!-- <el-button type="text">立即设置</el-button> -->
							</div>
						</div>
					</div>
					<div class="personal-edit-safe-box">
						<div class="personal-edit-safe-item">
							<div class="personal-edit-safe-item-left">
								<div class="personal-edit-safe-item-left-label">{{ $t('message.userFields.userType') }}
								</div>
								<div class="personal-edit-safe-item-left-value">
									<el-form-item prop="userType">
										<el-select v-model="personalForm.userType"
											:placeholder="$t('message.page.selectKeyPlaceholder')" clearable
											disabled="disabled" class="w100">
											<el-option v-for="item in personalForm.userTypeData" :label="item.itemName"
												:value="item.itemName" :key="item.itemId"></el-option></el-select>
									</el-form-item>
								</div>
							</div>
							<div class="personal-edit-safe-item-right">
								<!-- <el-button type="text">立即设置</el-button> -->
							</div>
						</div>
					</div>
					<div class="personal-edit-safe-box">
						<div class="personal-edit-safe-item">
							<div class="personal-edit-safe-item-left">
								<div class="personal-edit-safe-item-left-label">Role</div>
								<div class="personal-edit-safe-item-left-value">
									<el-form-item prop="roleArr">
										<el-select v-model="personalForm.roleArr" multiple
											:placeholder="$t('message.page.selectKeyPlaceholder')" disabled="disabled"
											style="width: 100%">
											<el-option v-for="item in personalForm.roleData" :key="item.id"
												:label="item.name" :value="item.id" />
										</el-select>
									</el-form-item>
								</div>
							</div>
							<div class="personal-edit-safe-item-right">
								<!-- <el-button type="text">立即设置</el-button> -->
							</div>
						</div>
					</div>
					<div class="personal-edit-safe-box">
						<div class="personal-edit-safe-item">
							<div class="personal-edit-safe-item-left">
								<div class="personal-edit-safe-item-left-label">Groups</div>
								<div class="personal-edit-safe-item-left-value">
									<el-form-item prop="groups">
										<el-select v-model="personalForm.groups" multiple
											:placeholder="$t('message.page.selectKeyPlaceholder')" disabled="disabled"
											style="width: 100%">
											<el-option v-for="item in personalForm.groupsData" :key="item.id"
												:label="item.group_Name" :value="item.id" />
										</el-select>
									</el-form-item>
								</div>
							</div>
							<div class="personal-edit-safe-item-right">
								<!-- <el-button type="text">立即设置</el-button> -->
							</div>
						</div>
					</div>
				</el-card>
			</el-col>

			<!--右边 -->
			<el-col :xs="24" :sm="18">
				<el-row>
					<el-col :xs="24">
						<el-card class="list-search-card mb1" shadow="never" header="Ticket Search"
							:body-style="{ paddingTop: '6px', paddingBottom: '1px' }">
							<div class="list-index-search">
								<el-form label-width="100px" @keyup.enter="onSearch">
									<el-row :gutter="35">
										<el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" class="mb6">
											<el-form-item label="Status">
												<el-tree-select v-model="searchData.params.ticket_Status_List"
													:data="searchData.statusOpts" multiple :render-after-expand="false"
													filterable
													:filter-method="(val) => filterNodeMethod(val, searchData.statusOpts)"
													clearable show-checkbox class="w100" collapse-tags />
											</el-form-item>
										</el-col>
										<el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" class="mb6">
											<el-form-item label="Priority">
												<el-tree-select v-model="searchData.params.ticket_Priority_List"
													:data="searchData.priorityData" multiple
													:render-after-expand="false" filterable
													:filter-method="(val) => filterNodeMethod(val, searchData.statusOpts)"
													clearable show-checkbox class="w100" collapse-tags />
											</el-form-item>
										</el-col>
										<el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" class="mb6">
											<el-form-item label="Category">
												<el-tree-select v-model="searchData.params.ticket_Category_List"
													:data="searchData.categoryOpts" multiple
													:render-after-expand="false" filterable
													:filter-method="(val) => filterNodeMethod(val, searchData.statusOpts)"
													clearable show-checkbox class="w100" collapse-tags />
											</el-form-item>
										</el-col>

										<el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" class="mb6">
											<el-form-item label="Projects">
												<el-tree-select v-model="searchData.params.ticket_Customer_List"
													:data="searchData.customerOpts" multiple
													:render-after-expand="false" filterable
													:filter-method="(val) => filterNodeMethod(val, searchData.statusOpts)"
													clearable show-checkbox :check-strictly="true"
													:default-expand-all="true" class="w-20 tag-select-input"
													collapse-tags />
											</el-form-item>
										</el-col>
										<el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" class="mb6">
											<el-form-item label="Ticket Number">
												<el-input v-model="searchData.params.ticket_Number"
													placeholder="Please input" clearable></el-input>
											</el-form-item>
										</el-col>
										<el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" class="mb6" v-if="false">
											<el-form-item label="Delegate To">
												<el-tree-select ref="localTree" v-model="searchData.params.assigToId"
													:data="searchData.assigneeOpts" :render-after-expand="false"
													node-key="value" show-checkbox check-strictly check-on-click-node
													style="width: 100%" />
											</el-form-item>
										</el-col>
										<el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" class="mb6">
											<el-form-item label="Create Date">
												<el-row style="width: 100%">
													<el-col>
														<el-date-picker v-model="searchData.params.startTime"
															type="date" format="MM/DD/YYYY" style="width: 130px"
															:locale="locale" />
														&nbsp;TO&nbsp;
														<el-date-picker flex v-model="searchData.params.endTime"
															type="date" format="MM/DD/YYYY" width="80"
															style="width: 130px" />
													</el-col>
												</el-row>
											</el-form-item>
										</el-col>

										<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
											<el-form-item>
												<el-button size="small" type="primary" @click="onSearch"> {{
													$t('message.page.buttonSearch') }} </el-button>
												<el-button size="small" type="danger" @click="onSearchReSet"> {{
													$t('message.page.buttonReset') }} </el-button>
											</el-form-item>
										</el-col>
									</el-row>
								</el-form>
							</div>
						</el-card>
					</el-col>
				</el-row>
				<el-row style="height: 80.4%">
					<!-- Tikets List -->
					<el-col :span="24">
						<el-card style="height: 100%" :body-style="{ padding: '0px' }">
							<el-tabs type="border-card" class="tikets_list" @tab-change="onTicetUser">
								<el-tab-pane label="Requested tickets">
									<ticketUser ref="tiketCreateUserRef" @onChildSearch="onSearchTicket" />
								</el-tab-pane>
								<!-- <el-tab-pane label="Assigned tickets">
								<ticketUser ref="tiketAssignedRef" @onChildSearch="onSearchTicket" />
							</el-tab-pane> -->
							</el-tabs>
						</el-card>
						<!-- <el-row :gutter="15" class="personal-recommend-row">
								<el-col :sm="6" v-for="(v, k) in recommendList" :key="k" class="personal-recommend-col">
									<div class="personal-recommend" :style="{ 'background-color': v.bg }">
										<SvgIcon :name="v.icon" :style="{ color: v.iconColor }" />
										<div class="personal-recommend-auto">
											<div>{{ v.title }}</div>
											<div class="personal-recommend-msg">{{ v.msg }}</div>
										</div>
									</div>
								</el-col>
							</el-row> -->
					</el-col>
				</el-row>
			</el-col>
		</el-row>
	</div>
</template>

<script lang="ts">
import { useRouter } from 'vue-router';
import { ref, toRefs, reactive, computed, defineComponent, getCurrentInstance, onMounted, onBeforeMount, watch } from 'vue';
import { formatAxis } from '/@/utils/formatTime';
//import { newsInfoList, recommendList } from './mock';
import { FormInstance, ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import { useThemeConfig } from '/@/stores/themeConfig';
import dictItemApi from '/@/api/dictItem/index';
import userApi from '/@/api/user/index';
import roleApi from '/@/api/role';
import groupsApi from '/@/api/cmGroups/index';
import cmCategoryApi from '/@/api/cmCategory/index';
import organizationApi from '/@/api/organization/index';
import { checkEmail } from '/@/utils/toolsValidate';
//import ticketSearch from '/@/views/personal/components/ticketSearch.vue'
import ticketUser from '/@/views/bpts/personal/components/ticketUser.vue';
//import { connect } from 'http2';
//import Contextmenu from '/@/layout/navBars/tagsView/contextmenu.vue';
import delegateList from '/@/views/bpts/userDelegate/index.vue';
//import delegateList from '/@/views/bpts/userDelegate/indexDialog.vue';
import { RoleName } from '/@/types/role';
import other from '/@/utils/other';
import { Local } from '/@/utils/storage';
import mittBus from '/@/utils/mitt';
import { VxeUI } from 'vxe-table';

// 定义接口来定义对象的类型
interface PersonalState {
	//newsInfoList: any;
	//recommendList: any;
	personalForm: any;
	searchData: any;
}

export default defineComponent({
	name: 'personal',
	components: {
		ticketUser,
		delegateList,
	},
	setup() {
		const delegateUserRef = ref();
		const tiketAssignedRef = ref();
		const tiketCreateUserRef = ref();
		const router = useRouter();

		const stores = useUserInfo();
		const { userInfos } = storeToRefs(stores);
		const storesThemeConfig = useThemeConfig();
		const { themeConfig } = storeToRefs(storesThemeConfig);

		const { t } = useI18n();
		const changePwdRef = ref(false);

		const { proxy } = getCurrentInstance() as any;

		const state = reactive<PersonalState>({
			searchData: {
				params: {
					groupId: '' as any,
					ticket_Parent_Id: 0,
					ticket_Status: '' as any,
					ticket_Number: '',
					ticket_Priority: '',
					ticket_Status_List: [],
					ticket_Priority_List: [],
					ticket_Category_List: [],
					ticket_Customer_List: [],
					ticket_From: '',
					ticket_Category: '',
					ticket_Customer: '',
					pageIndex: 1,
					pageSize: 10,
					searchKey: '',
					startTime: '',
					endTime: '',
					assigToId: '' as any,
					createdById: '' as any,
					ticketTab: '0', //tab
					fromPage: 'personal',
					order: 'CreatedAt',
					sort: 'desc', // asc or desc
				},
				priorityData: [],
				pickerDate: [],
				categoryOpts: [],
				customerOpts: [],
				statusOpts: [],
			},
			//newsInfoList,
			//recommendList,

			personalForm: {
				userId: '',
				realName: '',
				lastName: '',
				userName: '',
				lastLoginTime: '',
				lastLoginIp: '',
				pwd: '',
				confirmPwd: '',
				isEmail: false,
				email: '',
				autograph: '',
				occupation: '',
				phone: '',
				sex: '',
				orgName: '',
				orgNameData: [],
				userType: '',
				userTypeData: {},
				roleArr: [],
				roleData: {},
				groups: [],
				groupsData: {},
				hasDeleteList: false,
				language: '',
				rules: {
					pwd: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
					confirmPwd: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
					realName: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
					lastName: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
					email: [
						{ required: true, message: 'Please input', trigger: 'blur' },
						{
							validator: checkEmail,
							min: 9,
							max: 18,
							message: t('message.formats.email'),
							trigger: 'blur',
						},
					],
				},
			},
		});

		const changePwdRules = {
			pwd: [
				{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' },
				{
					pattern:
						/^(?=.*[a-z])(?=.*[A-Z])(?=.*(\d|[~!@#$%^&*()_+,]))[A-Za-z\d~!@#$%^&*()_+,]{7,20}$|^(?=.*\d)(?=.*[~!@#$%^&*()_+,])[A-Za-z\d~!@#$%^&*()_+,]{7,20}$/,
					message: 'Password is Invalid.',
					trigger: ['blur'],
				},
			],
			confirmPwd: [
				{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' },
				{
					pattern:
						/^(?=.*[a-z])(?=.*[A-Z])(?=.*(\d|[~!@#$%^&*()_+,]))[A-Za-z\d~!@#$%^&*()_+,]{7,20}$|^(?=.*\d)(?=.*[~!@#$%^&*()_+,])[A-Za-z\d~!@#$%^&*()_+,]{7,20}$/,
					message: 'Password is Invalid.',
					trigger: ['blur'],
				},
			],
		};

		const form = reactive({
			userId: '',
		});

		const changePwdFormRef = ref<FormInstance>();

		// 当前时间提示语
		const currentTime = computed(() => {
			return formatAxis(new Date());
		});

		// 获取用户信息 vuex
		const currentUser = computed(() => {
			return userInfos.value;
		});

		const filterNodeMethod = (value, data) => {
			data.value = [...data].filter((item) => {
				console.log('filterNodeMethod', item.label.toLowerCase());
				return item.label.toLowerCase().includes(value.toLowerCase());
			});
		};

		const currentData = onMounted(async () => {
			await roleApi.GetList().then((rs) => {
				state.personalForm.roleData = rs.data;
			});

			let userId = currentUser.value.userId.toString();
			await userApi
				.Detail(userId)
				.then((rs) => {
					state.personalForm.userName = rs.data.userName;
					state.personalForm.realName = rs.data.realName;
					state.personalForm.lastName = rs.data.lastName;
					state.personalForm.userId = rs.data.id;
					state.personalForm.lastLoginTime = rs.data.lastLoginTime;
					state.personalForm.lastLoginIp = rs.data.lastLoginIp;
					state.personalForm.isEmail = rs.data.isEmail;
					state.personalForm.userType = rs.data.userType;
					state.personalForm.roleArr = rs.data.roles;
					state.personalForm.groups = rs.data.groups;
					state.personalForm.email = rs.data.email;
					state.personalForm.orgName = rs.data.orgName;
					state.personalForm.language = rs.data.language;
				})
				.catch((rs) => {
					ElMessage.error(rs.resultMsg || rs.toString());
				})
				.finally(() => { });
			getBaseData();
		});

		const getBaseData = async () => {
			var dictArr = ['Priority', 'TicketStatus'];
			dictItemApi.Many(dictArr).then((rs: any) => {
				let priorityArr = rs.data?.find((a: any) => {
					return a.dictValue === 'Priority';
				})?.items;
				let statusArr = rs.data?.find((a: any) => {
					return a.dictValue === 'TicketStatus';
				})?.items;

				for (let item of statusArr) {
					state.searchData.statusOpts.push({ value: item.itemValue, label: item.itemName });
				}

				for (let item of priorityArr) {
					state.searchData.priorityData.push({ value: item.itemValue, label: item.itemName });
				}
			});

			//类别
			cmCategoryApi.GetListByUserId({}).then((rs) => {
				let categoryArr = rs.data;

				for (let item of categoryArr) {
					state.searchData.categoryOpts.push({ value: item['category_Code'], label: item['category_Code'] + ' - ' + item['category_Description'] });
				}
			});

			organizationApi.GetEndUserOrganization({}).then((rs) => {
				let customerArr = rs.data;

				for (let item of customerArr) {
					// state.searchData.customerOpts.push({ value: item['orgId'], label: item['name'] });
					state.personalForm.orgNameData.push({ value: item['orgId'], label: item['name'] });
				}
			});

			if (currentUser.value.roles.indexOf('Super Admin') > -1) {
				organizationApi.GetCompanyProjectTree({}).then((rs) => {
					state.searchData.customerOpts = rs.data.treeNodeList;
				});
			} else {
				// organizationApi.GetCompanyProjectTreeByUserIdForListSearch({}).then((rs) => {
				organizationApi.GetCompanyProjectTreeByUserId({ MasterUserTypeFlag: 1 }).then((rs) => {
					state.searchData.customerOpts = rs.data.treeNodeList;
				});
			}

			var dictArr = ['UserType'];
			dictItemApi.Many(dictArr).then((rs: any) => {
				state.personalForm.userTypeData = rs.data?.find((a: any) => {
					return a.dictValue === 'UserType';
				})?.items;
			});

			groupsApi.GetList().then((rs) => {
				state.personalForm.groupsData = rs.data;
			});
		};

		const onSubmitDefalutEmail = (isEmail: boolean) => {
			var obj = {
				id: currentUser.value.userId,
				isEmail: isEmail,
			};
			userApi
				.SetUserDefalutEmail(obj)
				.then((rs) => {
					ElMessage.success('Succeed');
				})
				.catch((rs) => {
					ElMessage.error(rs.resultMsg || rs.toString());
				})
				.finally(() => { });
		};
		const onchangeEmail = (email: string) => {
			proxy.$refs.ruleFormReflastEmail.validate((valid: any) => {
				if (!valid) {
					return;
				}

				var obj = {
					id: currentUser.value.userId,
					email: email,
				};

				userApi
					.UpdateUserEmail(obj)
					.then(() => {
						ElMessage.success('Succeed');
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => { });
			});
		};
		const onchangeFirstName = (name: string) => {
			proxy.$refs.ruleFormRefrealName.validate((valid: any) => {
				if (!valid) {
					return;
				}

				var obj = {
					id: currentUser.value.userId,
					RealName: name,
				};

				userApi
					.UpdateUserName(obj)
					.then(() => {
						ElMessage.success('Succeed');
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => { });
			});
		};

		const onchangeLanguage = (val: string) => {
			var obj = {
				id: currentUser.value.userId,
				Language: val,
			};
			userApi
				.UpdateUserLanguage(obj)
				.then(() => {
					Local.remove('themeConfig');
					getThemeConfig.value.globalI18n = val;
					Local.set('themeConfig', getThemeConfig.value);
					proxy.$i18n.locale = val;
					console.log('val', val);
					if(val == 'zh-cn'){
						VxeUI.setLanguage('zh-CN')
					}else{
						// 切换指定语言
						VxeUI.setLanguage('en-US')
					}
					// initI18n();
					other.useTitle();
					ElMessage.success('Succeed');
				})
				.catch((rs) => {
					ElMessage.error(rs.resultMsg || rs.toString());
				})
				.finally(() => { });
		};

		const onchangeLastName = (name: string) => {
			proxy.$refs.ruleFormReflastName.validate((valid: any) => {
				if (!valid) {
					return;
				}
				var obj = {
					id: currentUser.value.userId,
					LastName: name,
				};

				userApi
					.UpdateUserName(obj)
					.then(() => {
						ElMessage.success('Succeed');
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => { });
			});
		};

		// 获取布局配置信息
		const getThemeConfig = computed(() => {
			return themeConfig.value;
		});

		// 设置 element plus 组件的国际化
		const setI18nConfig = (locale: string) => {
			mittBus.emit('getI18nConfig', proxy.$i18n.messages[locale]);
		};

		const initI18n = () => {
			switch (Local.get('themeConfig').globalI18n) {
				case 'zh-cn':
					setI18nConfig('zh-cn');
					break;
				case 'en':
					setI18nConfig('en');
					break;
				case 'zhtw':
					setI18nConfig('zh-tw');
					break;
			}
		};

		const onChangePwd = () => {
			state.personalForm.pwd = '';
			state.personalForm.confirmPwd = '';
			changePwdRef.value = true;
		};
		const onTicetUser = (tab: string) => {
			state.searchData.params.ticketTab = tab;
			if (tab == '0') {
				tiketCreateUserRef.value.onInit(state.searchData.params);
			} else {
				//tiketAssignedRef.value.onInit(state.searchData.params);
			}
		};
		const onSubmitChangePwd = () => {
			if (form.pwd != form.confirmPwd) {
				ElMessage.error(t('message.page.pwdMatch'));
				return;
			}
			proxy.$refs.changePwdFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}

				if (form.pwd != form.confirmPwd) {
					ElMessage.error(t('message.page.pwdMatch'));
					return;
				}

				form.userId = currentUser.value.userId.toString();

				var obj = { userId: form.userId, pwd: form.confirmPwd };
				state.searchData.loading = true;
				userApi
					.UserResetPassword(obj)
					.then(() => {
						ElMessage.success('Succeed');
						changePwdRef.value = false;
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.searchData.loading = false;
					});
			});
		};
		// 添加
		const onAdd = () => {
			router.push('/dashboard/ticket/create');
		};
		const onSearch = () => {
			//ticketSearch.tiketCreateUserRef.OnTest();

			tiketCreateUserRef.value.onInit(state.searchData.params);
			//tiketAssignedRef.value.onInit(state.searchData.params);
			//proxy.$refs.OnTest();
		};
		//列表调搜索
		const onSearchTicket = (val: any) => {
			tiketCreateUserRef.value.onInit(state.searchData.params);
			//.value.onInit(state.searchData.params);
		};
		const onSearchReSet = () => {
			const userAssigToId = state.searchData.params.assigToId;

			initFormData();
			if (userAssigToId == '') {
				state.searchData.params.createdById = currentUser.value.userId;
				state.searchData.params.assigToId = '';
				state.searchData.params.ticketTab = '0';
				tiketCreateUserRef.value.onInit(state.searchData.params);
			} else {
				state.searchData.params.createdById = '';
				state.searchData.params.assigToId = currentUser.value.userId;
				state.searchData.params.ticketTab = '1';
				//tiketAssignedRef.value.onInit(state.searchData.params);
			}
		};
		const initFormData = () => {
			state.searchData.params = {
				groupId: '' as any,
				ticket_Parent_Id: 0,
				ticket_Status: '' as any,
				ticket_Number: '',
				ticket_Priority: '',
				ticket_From: '',
				ticket_Category: '',
				ticket_Customer: '',
				ticket_Status_List: [],
				ticket_Priority_List: [],
				ticket_Category_List: [],
				ticket_Customer_List: [],
				pageIndex: 1,
				pageSize: 10,
				searchKey: '',
				startTime: '',
				endTime: '',
				assigToId: '' as any,
				createdById: '' as any,
				ticketTab: '0', //tab
				fromPage: 'personal',
				order: 'CreatedAt',
				sort: 'desc', // asc or desc
			} as any;
		};

		const handleClose = () => {
			proxy.$refs.changePwdFormRef.resetFields();
			changePwdRef.value = false;
		};
		const onShowDelegate = () => {
			var userId = currentUser.value.userId;
			delegateUserRef.value.openDialog({ action: 'Edit', id: userId });
		};

		watch(
			() => state.personalForm.roleArr,
			(oldValue, newValue) => {
				state.personalForm.roleData.forEach((element: any) => {
					if (oldValue.includes(element.id)) {
						if (element.name == RoleName.Admin || element.name == RoleName.SuperAdmin || element.name == RoleName.Employee) {
							state.personalForm.hasDeleteList = true;
						}
					}
				});
			}
		);

		return {
			currentTime,
			...toRefs(state),
			tiketCreateUserRef,
			changePwdRef,
			onChangePwd,
			onSubmitChangePwd,
			onSubmitDefalutEmail,
			onchangeEmail,
			onchangeFirstName,
			onchangeLastName,
			getBaseData,
			currentData,
			onTicetUser,
			onSearch,
			tiketAssignedRef,
			onSearchTicket,
			onSearchReSet,
			onAdd,
			filterNodeMethod,
			handleClose,
			changePwdRules,
			form,
			changePwdFormRef,
			onShowDelegate,
			delegateUserRef,
			onchangeLanguage,
		};
	},
});
</script>

<style scoped lang="scss">
@import '../../../theme/mixins/index.scss';

.tikets_list {
	// min-height: 600px;
}

.personal {
	padding: 5px;

	.personal-user {
		height: 158px;
		display: flex;
		align-items: center;

		.personal-user-left {
			width: 100px;
			height: 130px;
			border-radius: 3px;

			::v-deep(.el-upload) {
				height: 100%;
			}

			.personal-user-left-upload {
				img {
					width: 100%;
					height: 100%;
					border-radius: 3px;
				}

				&:hover {
					img {
						animation: logoAnimation 0.3s ease-in-out;
					}
				}
			}
		}

		.personal-user-right {
			flex: 1;
			padding: 0 15px;

			.personal-title {
				font-size: 18px;
				@include text-ellipsis(1);
			}

			.personal-item {
				display: flex;
				align-items: center;
				font-size: 13px;

				.personal-item-label {
					color: var(--el-text-color-secondary);
					@include text-ellipsis(1);
				}

				.personal-item-value {
					@include text-ellipsis(1);
				}
			}
		}
	}

	.personal-info {
		.personal-info-more {
			float: right;
			color: var(--el-text-color-secondary);
			font-size: 13px;

			&:hover {
				color: var(--color-primary);
				cursor: pointer;
			}
		}

		.personal-info-box {
			height: 130px;
			overflow: hidden;

			.personal-info-ul {
				list-style: none;

				.personal-info-li {
					font-size: 13px;
					padding-bottom: 10px;

					.personal-info-li-title {
						display: inline-block;
						@include text-ellipsis(1);
						color: var(--el-text-color-secondary);
						text-decoration: none;
					}

					& a:hover {
						color: var(--color-primary);
						cursor: pointer;
					}
				}
			}
		}
	}

	.personal-recommend-row {
		.personal-recommend-col {
			.personal-recommend {
				position: relative;
				height: 100px;
				color: var(--color-whites);
				border-radius: 3px;
				overflow: hidden;
				cursor: pointer;

				&:hover {
					i {
						right: 0px !important;
						bottom: 0px !important;
						transition: all ease 0.3s;
					}
				}

				i {
					position: absolute;
					right: -10px;
					bottom: -10px;
					font-size: 70px;
					transform: rotate(-30deg);
					transition: all ease 0.3s;
				}

				.personal-recommend-auto {
					padding: 15px;
					position: absolute;
					left: 0;
					top: 5%;

					.personal-recommend-msg {
						font-size: 12px;
						margin-top: 10px;
					}
				}
			}
		}
	}

	.personal-edit {
		// margin-top: 10px;
		// min-height: 600px;

		.personal-edit-title {
			position: relative;
			padding-left: 10px;
			color: var(--el-text-color-regular);

			&::after {
				content: '';
				width: 2px;
				height: 10px;
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				background: var(--color-primary);
			}
		}

		.personal-edit-safe-box {
			border-bottom: 1px solid var(--el-border-color-light, #ebeef5);
			padding: 7px 0;

			.personal-edit-safe-item {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.personal-edit-safe-item-left {
					flex: 1;
					overflow: hidden;

					.personal-edit-safe-item-left-label {
						color: var(--el-text-color-regular);
						margin-bottom: 5px;
					}

					.personal-edit-safe-item-left-value {
						color: var(--el-text-color-secondary);
						@include text-ellipsis(1);
						margin-right: 15px;
					}
				}
			}

			&:last-of-type {
				padding-bottom: 0;
				border-bottom: none;
			}
		}
	}
}
</style>
