﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.Company;
using YunLanCrm.Dto.Organization;
using Furion.DatabaseAccessor;
using YunLanCrm.IRepositories;
using System.Data;
using YunLanCrm.Dto.DictItem;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CompanyController : ControllerBase
    {
        private readonly ILogger<CompanyInfo> _logger;

        private readonly ICompanyService _companyService;

        readonly ICompanyRepository _companyRepository;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="companyService"></param>
        /// <param name="logger"></param>
        public CompanyController(ICompanyService companyService, ILogger<CompanyInfo> logger, ICompanyRepository companyRepository)
        {
            this._companyService = companyService;
            this._logger = logger;
            _companyRepository = companyRepository;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CompanyAddDto req)
        {
            var result = await _companyService.AddCompany(req);

            return result;
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CompanyEditDto req)
        {
            var result = await _companyService.UpdateCompany(req);

            return result;
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("UpdateCompanyIndustry")]
        public async Task<bool> UpdateCompanyIndustry(DictItemValueDto req)
        {
            return await _companyService.UpdateCompanyIndustry(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="companyId">companyId</param>
        /// <returns></returns>
        [HttpPost("Delete/{companyId}")]
        public async Task<bool> Delete(long companyId)
        {
            var result = await _companyService.DeleteCompany(companyId);

            return result > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _companyService.DeleteByIds(items);

            return result;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        [HttpGet("Get/{companyId}")]
        public async Task<CompanyDto> Get(long companyId)
        {
            return await _companyService.QueryInfo<CompanyDto>(a => a.CompanyId == companyId);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        [HttpGet("Detail/{companyId}/{companyType}")]
        public async Task<CompanyDetailDto> Detail(long companyId, string companyType)
        {
            CompanyInfo obj = null;

            if (companyType == "orgId")
            {
                obj = await _companyService.QueryInfo(a => a.OrgId == companyId);
            }
            else if (companyId > 0)
            {
                obj = await _companyService.QueryInfo(a => a.CompanyId == companyId);
            }

            //if (obj != null)
            //{
            return await _companyService.JoinV2(obj, companyType);
            //}

            //return null;
        }

        /// <summary>
        /// 根据组织架构ID
        /// 获取一个详细信息
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        [HttpGet("DetailByOrgId/{orgId}")]
        public async Task<CompanyDetailDto> DetailByOrgId(int orgId)
        {
            var obj = await _companyService.QueryInfo(a => a.OrgId == orgId);

            if (obj != null)
            {
                return _companyService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CompanyListDto>> List([FromQuery] CompanyListQueryDto req)
        {
            var list = new List<CompanyListDto>();
            var where = PredicateBuilder.New<CompanyInfo>(true);
            var orderBy = new OrderBy<CompanyInfo>(req.order, req.sort);
            var data = await _companyService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _companyService.Join(item);
                list.Add(detail.Adapt<CompanyListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CompanyListDto>> Query([FromQuery] CompanyPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CompanyListDto>> QueryPost(CompanyPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CompanyListDto>> PageQuery([FromQuery] CompanyPageQueryDto req)
        {
            var list = new List<CompanyListDto>();
            var where = PredicateBuilder.New<CompanyInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            if (!string.IsNullOrEmpty(req.Name))
            {
                where.And(a => a.Name.Contains(req.Name));
            }


            var totalCount = await _companyService.CountAsync(where);
            var orderBy = new OrderBy<CompanyInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _companyService.QueryList(where, orderBy, paging);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _companyService.Join(item);
                list.Add(detail.Adapt<CompanyListDto>());
            }
            #endregion

            return new PageQueryResult<CompanyListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

    }
}