<?xml version="1.0" encoding="UTF-8"?>
				<configuration>
					<system.webServer>
						<rewrite>
							<rules>
								<rule name="HTTP TO HTTPS" stopProcessing="true">
									<match url="(.*)" />
									<conditions>
										<add input="{HTTPS}" pattern="^OFF$" />
										<add input="{REMOTE_ADDR}" pattern="^192.168." negate="true" />
									</conditions>
									<action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="SeeOther" />
								</rule>
							</rules>
						</rewrite>
					</system.webServer>

					<location path="index.html">
						<system.webServer>
							<httpProtocol>
								<customHeaders>
								<add name="Cache-Control" value="no-store, max-age=0" />
								</customHeaders>
							</httpProtocol>
						</system.webServer>
					</location>
				</configuration>
								