﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class emailsApi extends BaseApi {
    //SendEmail
    
    // 创建记录
    SendEmail(data: any) {
        return request({
            url: this.baseurl + 'SendEmail',
            method: 'post',
            data,
        });
    }

    GetEmails(params?: any) {
        return request({
            url: this.baseurl + 'GetEmails',
            method: 'get',
            params,
        });
    }

    Autocomplete(queryStr?: any) {
        return request({
            url: this.baseurl + 'Autocomplete/' + queryStr,
            method: 'get',
        });
    }
    Export(data?: any){
        return request({
            url: this.baseurl + 'Export',
            method: 'post',
            data,
            responseType: 'blob'
        });
    }
}

export default new emailsApi('/api/emails/', 'id');
