﻿<template>
	<div class="list-page-layout">
		<el-container class="invoice-index-container" style="height: 100%">
			<el-header style="height: auto">
				<el-card class="list-search-card" shadow="never" style="width: 100%" :body-style="{ paddingTop: '10px' }">
					<div class="list-index-search">
						<el-form >
							<el-form-item>
								<!-- <div class="modules-index-search"> -->
								<el-input
									v-model="tableData.param.searchKey"
									:placeholder="$t('message.page.searchKeyPlaceholder')"
									clearable
									style="max-width: 180px"
								>
								</el-input>
								<el-button size="small" type="primary" class="ml10" @click="onSearch">
									<el-icon>
										<ele-Search />
									</el-icon>
									{{ $t('message.page.buttonSearch') }}
								</el-button>
								<!-- </div> -->
							</el-form-item>
						</el-form>
					</div>
				</el-card>
			</el-header>
			<el-header class="table_header mt5" style="display: flex">
				<div class="left-panel">
					<el-button v-auth="'systemApi.Edit'" size="small" type="primary" class="ml10" @click="onUpdate">
						{{ $t('message.page.buttonUpdate') }}
					</el-button>
				</div>
				<div class="right-panel" v-if="false">
					<el-button size="small" class="ml10" @click="onInit">
						<el-icon>
							<ele-Refresh />
						</el-icon>
						Refresh
					</el-button>
					<el-button size="small" class="ml10" @click="onPrint">
						<el-icon>
							<ele-Printer />
						</el-icon>
						Print
					</el-button>
				</div>
			</el-header>
			<el-main class="nopadding" ref="printMain">
				<div class="scTable" style="height: 100%" ref="scTableMain">
					<div class="scTable-table">
						<el-table
							:data="tableData.data"
							v-loading="tableData.loading"
							height="calc(100%)"
							@row-dblclick="rowClick"
							@selection-change="selectionChange"
							stripe border
						>
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')" :image-size="100"></el-empty>
							</template>
							<el-table-column type="index" />

							<el-table-column :label="$t('message.apiFields.code')" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.code }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.apiFields.name')" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.name }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.apiFields.controller')" width="300px" show-overflow-tooltip sortable prop="controller">
								<template #default="{ row }">
									<span>{{ row.controller }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.apiFields.method')" width="200">
								<template #default="{ row }">
									<el-tag v-if="row.action.indexOf('get') > -1" type="success" class="action-tag">GET </el-tag>
									<el-tag v-if="row.action.indexOf('post') > -1" type="danger" class="action-tag">POST </el-tag>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.emailFields.emailStatus')" width="80">
								<template #default="{ row }">
									<el-icon v-if="row.enabled" style="color: #67c23a">
										<ele-SuccessFilled />
									</el-icon>
									<el-icon v-if="!row.enabled" style="color: red">
										<ele-SuccessFilled />
									</el-icon>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.page.createAt')" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.createTime }}</span>
								</template>
							</el-table-column>
							<el-table-column label="描述" show-overflow-tooltip v-if="false">
								<template #default="{ row }">
									<span>{{ row.description }}</span>
								</template>
							</el-table-column>
							<el-table-column fixed="right" align="center" :label="$t('message.page.Action')" width="100" v-if="false">
								<template #default="{ row }">
									<el-button size="mini" type="text" @click="onTest(row)">{{ $t('message.page.actionsTest') }}</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div class="scTable-page">
						<el-pagination
							@size-change="onSizechange"
							@current-change="onCurrentChange"
							:pager-count="5"
							:page-sizes="[10, 20, 30]"
							v-model:current-page="tableData.param.pageIndex"
							background
							v-model:page-size="tableData.param.pageSize"
							layout="total, sizes, prev, pager, next, jumper"
							:total="tableData.total"
							small
						>
						</el-pagination>
					</div>
				</div>

				<CreateOrEdit ref="createOrEditRef"></CreateOrEdit>
				<el-drawer v-model="infoDrawer" title="接口详情" :size="600" destroy-on-close>
					<Detail ref="detailRef" :info="detailObj"></Detail>
				</el-drawer>
			</el-main>
		</el-container>
	</div>
</template>

<script lang="ts">
//import { useRouter } from 'vue-router';
import { toRefs, reactive, onMounted, ref, defineComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate } from '/@/utils/formatTime';
import print from '/@/utils/print.js';

import apiApi from '/@/api/api/index';
import Detail from './components/detail.vue';
import CreateOrEdit from './components/createOrEdit.vue';

export default defineComponent({
	name: 'systemApi',
	components: { Detail, CreateOrEdit },
	setup() {
		//const router = useRouter();
		const createOrEditRef = ref();
		const printMain = ref(null);
		const state: any = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'id',
					sort: 'desc', // asc or desc
				},
			},
			infoDrawer: false,
			detailObj: {},
		});

		const onTest = () => {
			ElMessage.info('Not implemented');
		};

		//初始化
		const onInit = () => {
			state.tableData.loading = true;
			apiApi
				.Query(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
				})
				.catch((rs) => {
					ElMessage.error(rs.resultMsg || rs.toString());
					state.tableData.loading = false;
				})
				.finally(() => (state.tableData.loading = false));
		};
		//搜索
		const onSearch = () => {
			onInit();
		};

		// 添加
		const onAdd = () => {
			createOrEditRef.value.openDialog({ action: 'Create' });
			//router.push('/api/Create');
		};
		// 修改
		const onEdit = (row: Object) => {
			createOrEditRef.value.openDialog({ action: 'Edit', id: row.id });
			//router.push('/api/edit/' + row.id);
		};
		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(`此操作将永久当前纪录，是否继续?`, '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				apiApi
					.DeleteByKey(row.id)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((rs) => {
						ElMessage.error(rs.ResultMsg || rs.toString());
					});
			});
		};

		const onUpdate = () => {
			state.tableData.loading = true;
			apiApi
				.LoadApi()
				.then((rs) => {
					ElMessage.success(`Completed updating ${rs.data} records`);
					onInit();
				})
				.catch((rs) => {
					ElMessage.error(rs.resultMsg);
					state.tableData.loading = false;
				})
				.finally(() => {
					state.tableData.loading = false;
				});
		};

		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection;
		};
		const onDeleteByList = () => {
			ElMessageBox.confirm(`确定删除选中的 ${state.tableData.selection.length} 项吗？`, '提示', {
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				apiApi.Delete({ keys: state.tableData.selection.join(',') }).then((rs) => {
					if (rs.data == 0) {
						ElMessage.error(rs.resultMsg);
						return;
					}
					onInit();
				});
			});
		};

		const rowClick = (row: any) => {
			state.detailObj = {};

			apiApi.Detail(row.id).then((rs) => {
				state.detailObj = rs.data;
				state.infoDrawer = true;
			});
		};
		const onPrint = () => {
			print(printMain.value);
		};
		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};
		// 页面加载时
		onMounted(() => {
			onInit();
		});
		return {
			onTest,
			printMain,
			onUpdate,
			formatStrDate,
			onPrint,
			createOrEditRef,
			selectionChange,
			rowClick,
			onInit,
			onAdd,
			onEdit,
			onDelete,
			onDeleteByList,
			onSizechange,
			onCurrentChange,
			onSearch,
			...toRefs(state),
		};
	},
});
</script>

<style scoped>
.action-tag {
	margin-left: 10px;
}
</style>
