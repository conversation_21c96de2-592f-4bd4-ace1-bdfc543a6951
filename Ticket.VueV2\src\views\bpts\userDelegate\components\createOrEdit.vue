﻿<template>
	<div class="userDelegate-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="550px" :close-on-click-modal="false" draggable>
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="170px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6 w100">
						<el-form-item :label="$t('message.userFields.approverType')" prop="approverType">
							<el-select v-model="ruleForm.approverType"
								:placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100"
								:disabled="isShowfield">
								<el-option v-for="item in approverTypeData" :label="item.itemName"
									:value="item.itemName" :key="item.itemId"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.userFields.delegateBy')" prop="userTrusteeId">
							<el-select v-model="ruleForm.userTrusteeId" clearable filterable
								:placeholder="$t('message.page.selectKeyPlaceholder')" class="w100"
								:disabled="isShowfield">
								<el-option v-for="item in userTrusteeData" :key="parseInt(item.nodeId)"
									:label="item.label" :value="parseInt(item.nodeId)" />
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.userFields.selectApprover')" prop="userDelegateId">
							<el-select v-model="ruleForm.userDelegateId" clearable filterable
								:placeholder="$t('message.page.selectKeyPlaceholder')" class="w100"
								:disabled="isShowfield">
								<el-option v-for="item in userDelegateData" :key="item.userId" :label="item.userName"
									:value="item.userId" />
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.userFields.startDate')" prop="dateStart">
							<MyDate v-model:input="ruleForm.dateStart" :placeholder="'Pick a day'" class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.userFields.endDate')" prop="dateEnd">
							<MyDate v-model:input="ruleForm.dateEnd" :placeholder="'Pick a day'" class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.userFields.email')" prop="isEmail">
							<el-switch v-model="ruleForm.isEmail" class="ml-2"
								:label="$t('message.groupFields.Default')" />
						</el-form-item>
					</el-col>
					<!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.userFields.approve')" prop="isEmail">
							<el-switch v-model="ruleForm.isApprove" class="ml-2" :label="$t('message.groupFields.Default')" />
						</el-form-item>
					</el-col> -->
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.userFields.projects')" prop="projectIds">
							<el-tree-select ref="localCompanyTree" v-model="ruleForm.projectIds" :data="customerOpts"
								:render-after-expand="false" node-key="value" show-checkbox :check-strictly="true"
								check-on-click-node style="width: 100%" multiple @check-change="checkChange"
								@current-change="onProjectCurrentChange" :default-expanded-keys="ruleForm.projectIds"
								:auto-expand-parent="true" :props="{ value: 'value', label: 'label' }"
								@node-expand="ClickExpandNode" :disabled="isShowfield" filterable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">{{ $t('message.page.buttonClose') }}</el-button>
					<el-button v-if="dialogParams.action === 'Create'" @click="onInitForm" size="small" type="danger">{{
						$t('message.page.buttonReset')
					}}</el-button>
					<el-button v-auth="'delegateUser.Delete'" v-if="dialogParams.action === 'Edit' && !isShowfield"
						@click="onDelete" type="danger" size="small">{{ $t('message.page.buttonDelete') }}</el-button>
					<el-button :loading="saveLoading" type="primary" @click="onSubmit" size="small">{{
						$t('message.page.buttonSave') }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent, ref, watch } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import dictItemApi from '/@/api/dictItem/index';
import userDelegateApi from '/@/api/userDelegate';
import userInfoApi from '/@/api/user/index';
import organizationApi from '/@/api/organization/index';
import MyDate from '/@/components/ticket/ticketDate.vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';

interface DialogParams {
	action: string;
	userTrusteeId: string;
	id: number;
	showfield: boolean;
}

export default defineComponent({
	name: 'userDelegateCreateOrEdit',
	components: { MyDate },
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const localCompanyTree = ref();

		const state = reactive({
			title: 'Create UserDelegate',
			isShowDialog: false,
			saveLoading: false,
			deleteLoading: false,
			isShowfield: false,
			dialogParams: {
				action: '',
				userTrusteeId: -1,
				id: -1,
			},

			ruleForm: {
				id: 0, //
				isDeleted: false, //
				createdById: 0, //
				createdAt: new Date(), //
				modifiedById: 0, //
				modifiedAt: new Date(), //
				userDelegateId: '',
				userTrusteeId: '', //
				dateStart: '', //
				dateEnd: '', //
				isApprove: false, //
				isEmail: false, //
				approverType: '', //
				projectIds: [] as any,
				delegateProjectId: [] as any,
			},
			rules: {
				id: [{ required: true, message: 'Please input', trigger: 'blur' }],
				dateStart: [{ type: 'date', required: false, message: 'Pick date', trigger: 'change' }],
				dateEnd: [{ type: 'date', required: false, message: 'Pick date', trigger: 'change' }],
				userDelegateId: [{ required: true, message: 'Please input', trigger: 'change' }],
				approverType: [{ required: true, message: 'Please input', trigger: 'blur' }],
				projectIds: [{ required: true, message: 'Please input', trigger: 'blur' }],
				userTrusteeId: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
			approverTypeData: [],
			userDelegateData: {},
			userTrusteeData: {},
			customerOpts: [] as any,
		});
		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			state.dialogParams = parmas;
			state.isShowfield = parmas.showfield;

			onInitForm();
			getBaseData();
			state.isShowDialog = true;
			if (parmas.action == 'Create') {
				state.title = 'Add Delegate';
			} else if (parmas.action == 'Edit') {
				state.title = 'Edit Delegate';
				getData(parmas.id);
			} else {
				ElMessage.error('Parameter action cannot be empty.');
			}
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}

				var isOk = onCheckTime('');

				if (!isOk) {
					return;
				}

				//state.ruleForm.userTrusteeId = state.dialogParams.userTrusteeId;
				var obj = state.ruleForm;

				state.saveLoading = true;
				userDelegateApi
					.Save(obj)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};
		//根据id获取完整的信息
		const getData = (id: any) => {
			//userDelegateApi.GetByKey(id).then((rs) => {
			userDelegateApi.Detail(id).then((rs) => {
				state.ruleForm = Object.assign({}, rs.data);
				state.ruleForm.dateStart = getDate(state.ruleForm.dateStart);
				state.ruleForm.dateEnd = getDate(state.ruleForm.dateEnd);
				const dataProjectIds = [] as any[];
				const delegateProjectIds = [] as any[];
				rs.data.deletgateProjects.map(function (value: any, index: any, array: any) {
					dataProjectIds.push(value.cmProjectId + '');
					delegateProjectIds.push(value.id);
				});
				state.ruleForm.projectIds = dataProjectIds;
				state.ruleForm.delegateProjectId = delegateProjectIds;
			});
		};

		const getBaseData = () => {
			var dictArr = ['ApproverType'];
			dictItemApi.Many(dictArr).then((rs: any) => {
				state.approverTypeData = rs.data?.find((a: any) => {
					return a.dictValue === 'ApproverType';
				})?.items;
			});

			userInfoApi.GetStaffMemberList().then((rs) => {
				state.userTrusteeData = rs.data;
			});

			// userDelegateApi.GetSelectUserDelegate({ userTrusteeId: state.dialogParams.userTrusteeId }).then((rs) => {
			// 	state.userDelegateData = rs.data;
			// });

			// organizationApi.Tree({ IncludeProject: 1 }).then((rs) => {
			// 	state.customerOpts = rs.data;
			// });
		};

		const getUserDelegateData = (usr: any) => {
			userDelegateApi.GetSelectUserDelegate(usr).then((rs) => {
				state.userDelegateData = rs.data;
			});
		};
		const GetCompanyProjectTreeByUserId = (usr: any) => {
			organizationApi.GetCompanyProjectTreeByUserId(usr).then((rs) => {
				state.customerOpts = rs.data.treeNodeList;
			});
		};

		//删除纪录
		const onDelete = () => {
			ElMessageBox.confirm(`Are you sure to delete this?`, 'Tips', {
				confirmButtonText: 'OK',
				cancelButtonText: 'No,Thanks',
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				state.deleteLoading = true;
				userDelegateApi
					.DeleteByKey(state.ruleForm.id)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.deleteLoading = false;
					});
			});
		};
		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.ruleForm = {
				id: 0, //
				isDeleted: false, //
				createdById: 0, //
				createdAt: new Date(), //
				modifiedById: 0, //
				modifiedAt: new Date(), //
				userDelegateId: '',
				userTrusteeId: '', //
				dateStart: '', //
				dateEnd: '', //
				isApprove: false, //
				isEmail: false, //
				approverType: '', //
				projectIds: [] as any,
				delegateProjectId: [] as any,
			};
		};

		watch(
			() => state.ruleForm.userTrusteeId,
			() => {
				state.userDelegateData = {};
				if (parseThanZero(state.ruleForm.userTrusteeId)) {
					getUserDelegateData({ userTrusteeId: state.ruleForm.userTrusteeId });

					GetCompanyProjectTreeByUserId({ MasterUserTypeFlag: 1, userTrusteeId: state.ruleForm.userTrusteeId });
				} else {
				}
			},
			{ immediate: false }
		);

		const getDate = (strDate: string) => {
			if (strDate != null && strDate != '') {
				var nowDate = new Date(strDate);

				const date = {
					year: nowDate.getFullYear(),
					month: nowDate.getMonth() + 1,
					date: nowDate.getDate(),
				};

				const newmonth = date.month >= 10 ? date.month : '0' + date.month;
				const day = date.date >= 10 ? date.date : '0' + date.date;
				return newmonth + '/' + day + '/' + date.year;
			} else {
				return '';
			}
		};

		const onCheckTime = (name: string) => {
			var dateStart = state.ruleForm.dateStart;
			var dateEnd = state.ruleForm.dateEnd;

			var isOk = true;
			if (dateStart != '' && dateStart != null && dateEnd != '' && dateEnd != null) {
				var start = new Date(state.ruleForm.dateStart).getTime();
				var end = new Date(state.ruleForm.dateEnd).getTime();
				if (start > end) {
					// if (name == 'Start') {
					// 	state.ruleForm.dateStart = '';
					// }
					// else {
					// 	state.ruleForm.dateEnd = '';
					// }
					ElMessage.error('[Approve Start Date] cannot be greater than [Approve End Date]');
					isOk = false;
				}
			} else if ((dateStart == '' && dateEnd == '') || (dateStart == null && dateEnd == null)) {
				if (state.ruleForm.approverType == 'Backup Approver') {
					isOk = true;
				} else {
					if (state.isShowfield) {
						ElMessage.error('Please enter the approve date.');
						isOk = false;
					}
				}
			} else if (
				(dateStart == '' && dateEnd != '') ||
				(dateStart != '' && dateEnd == '') ||
				(dateStart == null && dateEnd != null) ||
				(dateStart != null && dateEnd == null)
			) {
				if (state.isShowfield) {
					ElMessage.error('Please enter the approve date.');
					//ElMessage.error('[Approve Start Date] cannot be greater than [Approve End Date]');
					isOk = false;
				}
			}
			return isOk;
		};

		const onProjectCurrentChange = (data: any) => {
			if (data) {
				state.ruleForm.projectIds.push(data.orgId);
				//根据key拿到Tree组件中的node节点
				const node = localCompanyTree.value.getNode(data.orgId);
				//调用节点处理方法
				setChildNodeExpand(node);
			}
		};

		const ClickExpandNode = (data: any) => {
			//根据key拿到Tree组件中的node节点

			const node = localCompanyTree.value.getNode(data.orgId);

			//调用节点处理方法
			setChildNodeExpand(node);
		};

		//递归设置子节点全部展开
		const setChildNodeExpand = (node: any) => {
			if (node != null && node.childNodes && node.childNodes.length) {
				node.childNodes.forEach((item: any) => {
					item.expanded = true;

					//递归调用相应子节点处理函数
					setChildNodeExpand(item);
				});
			}
		};
		// 通过check的回调里面获取节点id,再获取节点的node对象
		const checkChange = (data: any) => {
			//根据key拿到Tree组件中的node节点
			const node = localCompanyTree.value.getNode(data.nodeId);
			//调用节点处理方法
			setNode(node);
		};

		//递归设置子节点和父节点
		const setNode = (node: any) => {
			if (node.checked) {
				setChildNode(node);
				setParentNode(node);
			}
		};

		//递归设置父节点全部取消选中
		const setParentNode = (node: any) => {
			if (node.parent) {
				for (const key in node) {
					if (key === 'parent') {
						node[key].checked = false;
						//递归调用相应父节点处理函数
						setParentNode(node[key]);
					}
				}
			}
		};
		//递归设置子节点全部取消选中
		const setChildNode = (node: any) => {
			if (node.childNodes && node.childNodes.length) {
				node.childNodes.forEach((item: any) => {
					item.checked = false;
					item.expanded = true;

					//递归调用相应子节点处理函数
					setChildNode(item);
				});
			}
		};

		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			onCheckTime,
			...toRefs(state),
			onProjectCurrentChange,
			ClickExpandNode,
			localCompanyTree,
			checkChange,
		};
	},
});
</script>
