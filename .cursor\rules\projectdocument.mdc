---
description: 
globs: 
---
# 云岚CRM工单系统项目规范

## 1. 项目结构说明

### 根目录
├── .dockerignore - Docker忽略文件
├── .editorconfig - 编辑器配置
├── .gitignore - Git忽略规则
├── codecov.yml - 代码覆盖率配置
├── CreateYourProject.bat - 项目创建脚本
├── DockerBuild.bat - Docker构建脚本
├── Dockerfile - Docker构建配置
├── README.md - 项目说明文档
├── Ticket.sln - Visual Studio解决方案文件
├── YunLanCrm.Build.bat - 构建脚本
├── YunLanCrm.Publish.* - 发布相关脚本

### 前端项目完整结构 (Ticket.VueV2 - Vue 3.x)

#### 根目录文件
├── .env.* - 环境变量配置
├── .eslint* - 代码检查配置
├── .prettierrc.js - 代码格式化
├── index.html - 主入口文件
├── package.json - 项目依赖
├── tsconfig.json - TypeScript配置
├── vite.config.ts - Vite构建配置

#### public目录
├── favicon.ico - 网站图标
├── config.js - 前端配置
├── version.json - 版本信息
└── img/ - 图片资源
    ├── avatar.png
    ├── bpts.png
    └── logo-mini.png

#### src目录
├── App.vue - 根组件
├── main.ts - 应用入口
├── api/ - API请求
│   ├── BaseApi.ts - 基础API
│   └── ticket/ - 工单相关API
├── assets/ - 静态资源
│   ├── images/
│   └── styles/
├── components/ - 公共组件
│   ├── auth/ - 认证组件
│   ├── editor/ - 编辑器
│   └── ticket/ - 工单组件
├── config/ - 配置
│   ├── fileSelect.js
│   └── upload.js
├── router/ - 路由
│   ├── backEnd.ts
│   └── frontEnd.ts
├── stores/ - 状态管理
│   ├── routesList.ts
│   └── userInfo.ts
├── views/ - 页面
│   ├── home/
│   └── ticket/
└── types/ - 类型定义
    ├── global.d.ts
    └── ticketDtos.ts

#### Document目录
├── 项目设计文档.md
└── img/ - 设计图
    ├── module-structure.svg
    └── system-architecture.svg

### 后端核心
#### YunLanCrm.Api (主API)
├── appsettings.*.json - 环境配置
├── Program.cs - 应用入口
├── Startup.cs - 启动配置
├── web.config - IIS配置
├── Controllers/ - API控制器
├── Filter/ - 过滤器
├── Pages/ - Razor页面
├── PendingProcess/ - 待处理流程
├── Properties/ - 程序集属性
├── wwwroot/ - 静态文件
└── bin/, obj/ - 编译输出

#### YunLanCrm.Gateway (API网关)
├── ocelot.*.json - 路由配置
├── Program.cs - 入口
├── Startup.cs - 启动配置
├── Controllers/ - 网关控制器
├── Extensions/ - 扩展方法
└── Helper/ - 工具类

### 公共组件
#### YunLanCrm.Common
├── StringHelper.cs - 字符串工具
├── UploadHelper.cs - 上传工具
├── Attribute/ - 自定义特性
├── Authorizations/ - 授权相关
├── DB/ - 数据库相关
├── Extensions/ - 扩展方法
├── Helper/ - 工具类
└── LogHelper/ - 日志工具

#### YunLanCrm.Extensions
├── MyBookmarkProvider.cs - 书签提供
├── AOP/ - 切面编程
├── Apollo/ - 配置中心
├── AutoMapper/ - 对象映射
└── Middlewares/ - 中间件

### 基础设施
#### Ocelot.Provider.Nacos
├── Nacos.cs - Nacos集成
├── NacosMiddleware/ - 中间件
└── OcelotBuilderExtensions.cs - Ocelot扩展

### 数据层
#### YunLanCrm.IServices
├── I*.cs - 服务接口定义
└── BASE/ - 基础接口

#### YunLanCrm.Repository
└── 数据访问实现

#### YunLanCrm.Model
└── 数据模型定义

### 其他模块
#### YunLanCrm.EventBus
├── Eventbus/ - 事件总线
└── RabbitMQPersistent/ - RabbitMQ实现

#### YunLanCrm.WorkFlow
└── 工作流引擎实现

## 2. 代码规范

### 前端规范
- 使用ESLint + Prettier进行代码格式化
- 组件使用PascalCase命名
- 使用Composition API (Vue 3)
- 严格TypeScript类型检查

### 后端规范
- 遵循.NET Core最佳实践
- 控制器方法使用异步模式
- 服务层与仓储层分离
- 使用NLog/Serilog记录日志

## 3. 开发流程

1. 从`develop`分支创建特性分支
2. 提交前运行单元测试
3. 代码审查通过后合并到`develop`
4. 每日构建验证

## 4. 分支策略
- `main` - 生产环境分支
- `develop` - 开发主干分支
- `feature/*` - 特性开发分支
- `hotfix/*` - 紧急修复分支

## 5. 部署规范
- 使用Docker容器化部署
- 开发环境使用`DockerBuild.bat`构建
- 生产环境使用Jenkins流水线
- 配置分离(通过Nacos管理)

## 6. 测试要求
- 前端单元测试覆盖率≥80%
- 后端单元测试覆盖率≥70%
- API集成测试覆盖核心流程
- 重要功能需有E2E测试

## 7. 文档要求
- 所有公共API需有Swagger文档
- 复杂业务逻辑需有代码注释
- 数据库变更需记录迁移脚本
- 重大架构变更需更新本规范
