﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class workflowDefinitionsApi extends BaseApi {

    GetWorkflowRegistrys(params: any) {
        return request({
            url: this.baseurl + 'workflow-registry',
            method: 'get',
            params,
        });
    }

   

}

export default new workflowDefinitionsApi('/v1/','');




                        
        
        