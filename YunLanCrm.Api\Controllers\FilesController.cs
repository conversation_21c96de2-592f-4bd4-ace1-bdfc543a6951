﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.Files;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using System.DirectoryServices.Protocols;
using Newtonsoft.Json;
using Elsa.Models;
using SqlSugar;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class FilesController : ControllerBase
    {
        private readonly ILogger<FilesInfo> _logger;

        private readonly IFilesService _filesService;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="filesService"></param>
        /// <param name="logger"></param>
        public FilesController(IFilesService filesService, ILogger<FilesInfo> logger)
        {
            _filesService = filesService;
            _logger = logger;
        }

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="environment"></param>
        /// <param name="id">文件id</param>
        /// <param name="name">文件名称</param>
        /// <returns></returns>
        [HttpGet]
        [Route("/file/down")]
        public FileStreamResult FileDown([FromServices] IWebHostEnvironment environment, long? id, string name)
        {
            return null;
        }

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="name">文件名称（新的名称）</param>
        /// <returns></returns>
        [HttpGet]
        [Route("/file/{name}")]
        public async Task<FileStreamResult> FileDownByName(string name)
        {
            return await _filesService.FileDown(default, name);
        }

        [HttpGet("ticketFile/{id}")]
        //[Route("/ticketFile/{id}")]
        public async Task<FileStreamResult> TicketFileDown(long? id)
        {
            return await _filesService.TicketFileDown(id);
        }

        /// <summary>
        /// 多文件上传（包含其他参数）
        /// </summary>
        /// <param name="environment"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        [HttpPost("UpLoadFrom")]
        public async Task<List<FilesDto>> UpLoadFrom([FromServices] IWebHostEnvironment environment)
        {

            List<IFormFile> files = Request.Form.Files?.ToList();

            if (files == null || files.Count == 0)
            {
                throw new Exception("Please select files.");
            }

            string type = "Invoice";
            string relativePath = string.Empty;
            if (type == "Invoice")
            {
                string priority = Request.Form["priority"].ObjToString();
                string receivingType = Request.Form["receivingType"].ObjToString();

                if (string.IsNullOrWhiteSpace(priority))
                {
                    throw new Exception("Incorrect parameter priority.");
                }


                if (string.IsNullOrWhiteSpace(receivingType))
                {
                    throw new Exception("Incorrect parameter priority.");
                }

                var config = await _filesService.GetAppConfig();

                relativePath = Path.Combine(config.Invoice.PendingProcessDir, priority, receivingType);

                if (files == null || files.Count == 0)
                {
                    throw new ArgumentNullException(nameof(files));
                }
            }
            List<FilesDto> list = await _filesService.UpLoadFile(files, type, relativePath);

            return list;
        }

        /// <summary>
        /// 文件上传
        /// </summary>
        /// <param name="files"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        [HttpPost("Upload")]
        public async Task<List<FilesDto>> UpLoadFile(List<IFormFile> files)
        {

            if (files == null || files.Count == 0)
            {
                throw new ArgumentNullException(nameof(files));
            }


            List<FilesDto> list = await _filesService.UpLoadFile(files);


            return list;
        }

        /// <summary>
        /// 文件上传
        /// </summary>
        /// <param name="files"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        [HttpPost("UploadV2")]
        public async Task<List<FilesDto>> UpLoadFileV2(IFormCollection files)
        {

            if (files == null || files.Files.Count == 0)
            {
                throw new ArgumentNullException(nameof(files));
            }

            List<IFormFile> formFiles = (List<IFormFile>)files.Files;

            List<FilesDto> list = await _filesService.UpLoadFile(formFiles);


            return list;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        [NonAction]
        public async Task<long> Add(FilesAddDto req)
        {
            return await _filesService.AddIdentity(req.Adapt<FilesInfo>());
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        [NonAction]
        public async Task<bool> Update(FilesEditDto req)
        {
            return await _filesService.Update(req.Adapt<FilesInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="fileId">fileId</param>
        /// <returns></returns>
        [HttpPost("Delete/{fileId}")]
        [NonAction]
        public async Task<bool> Delete(long fileId)
        {
            int result = await _filesService.Delete(a => a.FileId == fileId);

            return result > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        [NonAction]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _filesService.DeleteByIds(items);

            return result;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="fileId"></param>
        /// <returns></returns>
        [HttpGet("Get/{fileId}")]
        [NonAction]
        public async Task<FilesDto> Get(long fileId)
        {
            return await _filesService.QueryInfo<FilesDto>(a => a.FileId == fileId);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="fileId"></param>
        /// <returns></returns>
        [HttpGet("Detail/{fileId}")]
        [NonAction]
        public async Task<FilesDetailDto> Detail(long fileId)
        {
            var obj = await _filesService.QueryInfo(a => a.FileId == fileId);

            if (obj != null)
            {
                return _filesService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        [NonAction]
        public async Task<List<FilesListDto>> List([FromQuery] FilesListQueryDto req)
        {
            var list = new List<FilesListDto>();
            var where = PredicateBuilder.New<FilesInfo>(true);
            var orderBy = new OrderBy<FilesInfo>(req.order, req.sort);
            var data = await _filesService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _filesService.Join(item);
                list.Add(detail.Adapt<FilesListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        [NonAction]
        public async Task<PageQueryResult<FilesListDto>> Query([FromQuery] FilesPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        [NonAction]
        public async Task<PageQueryResult<FilesListDto>> QueryPost(FilesPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<FilesListDto>> PageQuery([FromQuery] FilesPageQueryDto req)
        {
            var list = new List<FilesListDto>();
            var where = PredicateBuilder.New<FilesInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            var totalCount = await _filesService.CountAsync(where);
            var orderBy = new OrderBy<FilesInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _filesService.QueryList(where, orderBy, paging);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _filesService.Join(item);
                list.Add(detail.Adapt<FilesListDto>());
            }
            #endregion

            return new PageQueryResult<FilesListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }


        #region 编辑器里的图片上传
        /// <summary>
        /// 文件上传
        /// </summary>
        /// <param name="files"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        [HttpPost("UploadFile")]
        public async Task<IActionResult> UploadFile(IFormCollection files)
        {

            if (files == null || files.Files.Count == 0)
            {
                throw new ArgumentNullException(nameof(files));
            }

            List<IFormFile> formFiles = (List<IFormFile>)files.Files;

            List<FilesDto> list = await _filesService.UpLoadFile(formFiles);

            var filesDtoInfo=list.FirstOrDefault();

            if (filesDtoInfo != null)
            {

                var config = await _filesService.GetAppConfig();
                string host = config?.Upload.ImgServerHost;

                var resultImg = new ImgResult();
                var resultData = new
                {
                    url = host+filesDtoInfo.FileUrl,
                    fileName = filesDtoInfo.Name,
                    fileId = filesDtoInfo.FileId.ToString()
                };
                resultImg.Code = 200;
                resultImg.Msg = "success";
                resultImg.Data = resultData;
                var  result=JsonConvert.SerializeObject(resultImg);
                var resultJson = Content(result, "application/json");
                return resultJson;
            }
            return null;
        }

        #endregion
    }

    public class ImgResult
    {
        public int Code { get; set; }
        public string Msg { get; set; }
        public object Data { get; set; }
    }
}