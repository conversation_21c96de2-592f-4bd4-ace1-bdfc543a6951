<template>
	<div class="list-page-layout">
		<el-container style="height: 100%">
			<el-container >
				<el-header class="table_header" style="display: flex">
					<div class="left-panel"></div>
					<div class="right-panel">
						<el-dropdown>
							<el-button v-auth="'delUserList.Export'" type="primary" size="small" class="ml10">
								<el-icon>
									<ele-ArrowDownBold />
								</el-icon>
								{{ $t('message.page.buttonExport') }}
							</el-button>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item @click="onExportAllRecord(0)">{{ $t('message.page.buttonExportEntireList') }}</el-dropdown-item>
									<el-dropdown-item @click="onExportAllRecord(1)">{{ $t('message.page.buttonExportSelectedRecords') }}</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
					</div>
				</el-header>
				<el-main class="nopadding">
					<div class="scTable" style="height: 100%" ref="scTableMain">
						<div class="scTable-table">
							<el-table
								ref="tableRef"
								:data="tableData.data"
								v-loading="tableData.loading"
								height="calc(100%)"
								@selection-change="selectionChange"
								highlight-current-row
								:row-style="{ height: '40px' }"
								:cell-style="{ padding: '0px' }"
								stripe border
							>
								<template #empty>
									<el-empty :description="$t('message.page.emptyDescription')" :image-size="100"></el-empty>
								</template>
								<el-table-column type="selection" />

								<el-table-column :label="$t('message.userFields.userName')" show-overflow-tooltip>
									<template #default="{ row }">
										<span>{{ row.userName }}</span>
									</template>
								</el-table-column>

								<el-table-column :label="$t('message.userFields.realName')" show-overflow-tooltip>
									<template #default="{ row }">
										<span>{{ row.realName }}</span>
									</template>
								</el-table-column>

								<el-table-column :label="$t('message.userFields.userType')" show-overflow-tooltip>
									<template #default="{ row }">
										<span>{{ row.userType }}</span>
									</template>
								</el-table-column>

								<el-table-column :label="$t('message.userFields.email')" show-overflow-tooltip>
									<template #default="{ row }">
										<span>{{ row.email }}</span>
									</template>
								</el-table-column>

								<el-table-column :label="$t('message.userFields.status')">
									<template #default="{ row }">
										<el-tag type="danger" v-if="row.status == '1'">{{ $t('message.userFields.Inactive') }}</el-tag>
										<el-tag type="success" v-if="row.status == '0'">{{ $t('message.userFields.Active') }}</el-tag>
									</template>
								</el-table-column>

								<el-table-column :label="$t('message.userFields.orgName')" show-overflow-tooltip>
									<template #default="{ row }">
										<span>{{ row.orgName }}</span>
									</template>
								</el-table-column>

								<el-table-column :label="$t('message.userFields.createTime')">
									<template #default="{ row }">
										<span>{{ row.createTime }}</span>
									</template>
								</el-table-column>
								<el-table-column fixed="right" align="left" :label="$t('message.page.actions')">
									<template #default="{ row }">
										<el-button v-if="0 > 1" size="mini" type="text" @click="onDetail(row)">
											{{ $t('message.page.actionsView') }}
										</el-button>
										<el-button v-auth="'delUserList.Restore'" size="mini" type="text" @click="onReserve(row)"> Restore </el-button>
										<el-button v-auth="'delUserList.Delete'" size="mini" type="text" style="color: #ff3a3a" @click="onDelete(row)">
											{{ $t('message.page.actionsDelete') }}
										</el-button>
									</template>
								</el-table-column>
							</el-table>
						</div>
						<div class="scTable-page">
							<el-pagination
								@size-change="onSizechange"
								@current-change="onCurrentChange"
								:pager-count="5"
								:page-sizes="[10, 20, 30]"
								v-model:current-page="tableData.param.pageIndex"
								background
								v-model:page-size="tableData.param.pageSize"
								layout="total, sizes, prev, pager, next, jumper"
								:total="tableData.total"
								small
							>
							</el-pagination>
						</div>
					</div>
					<ResetPassword ref="resetPasswordRef" />
					<CreateOrEdit ref="createOrEditRef" @fetchData="onInit" />
					<el-drawer v-model="infoDrawer" :title="$t('message.page.detailTitle')" :size="600" destroy-on-close>
						<Detail ref="detailRef" :info="detailObj"></Detail>
					</el-drawer>
				</el-main>
			</el-container>
		</el-container>
	</div>
</template>

<script lang="ts">
//import { useRouter } from 'vue-router';
import { toRefs, reactive, onMounted, ref, defineComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate, formatDateTime } from '/@/utils/formatTime';
import print from '/@/utils/print.js';

import userApi from '/@/api/user/index';
import organizationApi from '/@/api/organization';

import Detail from './components/detail.vue';
import CreateOrEdit from './components/createOrEdit.vue';
import ResetPassword from './components/resetPassword.vue';
import { useI18n } from 'vue-i18n';

import dictItemApi from '/@/api/dictItem/index';

export default defineComponent({
	name: 'deleteUserList',
	components: { Detail, CreateOrEdit, ResetPassword },
	setup() {
		const { t } = useI18n();
		//const router = useRouter();
		const createOrEditRef = ref();
		const resetPasswordRef = ref();
		const printMain = ref(null);
		const tableRef = ref();

		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					status: '',
					email: '',
					userName: '',
					orgId: undefined,
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'id',
					sort: 'desc', // asc or desc
					isDeleted: true,
					ids: [],
					ischeckType: 0,
					userTypeList: [],
				},
			},
			orgTreeData: [],
			infoDrawer: false,
			detailObj: {},
			statusOpts: [],
			priorityData: [],
			countData: [],
			pickerDate: [],
			categoryOpts: [],
			customerOpts: [],
		});

		//初始化
		const onInit = () => {
			organizationApi.Tree().then((rs) => {
				state.orgTreeData = rs.data;
			});

			state.tableData.loading = true;
			userApi
				.Query(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
				})
				.catch((rs) => {})
				.finally(() => (state.tableData.loading = false));
		};

		const convertStatusVal = (val: any) => {
			if (val === 0) {
				return 'Active';
			} else {
				return 'InActive';
			}
		};
		const onExportAllRecord = (selectAll: number) => {
			let currPageSize = state.tableData.param.pageSize;
			state.tableData.param.pageSize = 10000;

			var checkSelection = state.tableData.selection;
			let ids_arr = [] as any;

			checkSelection.forEach(function (item, index) {
				let idjson = {} as any;
				idjson = item.id;
				ids_arr.push(idjson);
			});
			state.tableData.param.ischeckType = selectAll;
			if (selectAll == 1 && ids_arr.length == 0) {
				ElMessage.error(t('message.userFields.SelectTheDataYouWantToExport'));
				return;
			}
			if (selectAll == 0) {
				state.tableData.param.ids = [];
			} else {
				state.tableData.param.ids = ids_arr;
			}

			userApi
				.Export(state.tableData.param)
				.then((rs) => {
					downloadCallback(rs);
				})
				.catch((rs) => {})
				.finally(() => (state.tableData.loading = false));

			state.tableData.param.pageSize = currPageSize;
		};

		const downloadCallback = (rs) => {
			let data = rs;
			var newBlob = new Blob([data], { type: 'text/plain;charset=UTF-8' });
			var anchor = document.createElement('a');
			anchor.download = 'UsersDelete_' + formatDateTime() + '.xlsx';
			anchor.href = window.URL.createObjectURL(newBlob);
			anchor.click();
		};

		// 添加
		const onAdd = () => {
			createOrEditRef.value.openDialog({ action: 'Create' });
			//router.push('/user/Create');
		};
		// 还原
		const onReserve = (row: Object) => {
			ElMessageBox.confirm(t('message.page.dlgReserveText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				console.log('reserve id : ' + row.id);
				userApi
					.ReserveDelete(row.id)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((rs) => {});
			});
		};

		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				console.log('delete id : ' + row.id);
				userApi
					.PermanentlyDelete(row.id)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((rs) => {});
			});
		};
		//批量删除
		const onDeleteByList = () => {
			ElMessageBox.confirm(
				t('message.page.dlgDeleteSelectText1') + ` ${state.tableData.selection.length} ` + t('message.page.dlgDeleteSelectText2'),
				t('message.page.dlgTip'),
				{
					type: 'warning',
					closeOnClickModal:false,
				}
			).then(() => {
				var items = state.tableData.selection.map((a) => {
					return a.id;
				});
				userApi.DeleteMany(items).then((rs) => {
					if (rs.data == 0) {
						ElMessage.error(rs.resultMsg);
						return;
					}
					onInit();
				});
			});
		};
		// 密码重置
		const onPasswordReset = () => {
			var len = state.tableData.selection.length;
			if (len == 0) {
				ElMessage.error('Select at least one item');
				return;
			}
			if (len > 1) {
				ElMessage.error('Select at most one item');
				return;
			}
			const row = tableRef.value.getSelectionRows();
			resetPasswordRef.value.openDialog(row[0]);
		};
		//详细信息
		const onDetail = (row: any) => {
			state.detailObj = {};

			userApi.Detail(row.id).then((rs) => {
				state.detailObj = rs.data;
				state.infoDrawer = true;
			});
		};
		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection;
		};
		//打印
		const onPrint = () => {
			print(printMain.value);
		};
		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			if (state.tableData.param.pageIndex != val) {
				state.tableData.param.pageIndex = val;
				onInit();
			}
		};
		// 页面加载时
		onMounted(() => {
			onInit();
			var dictArr = ['TicketStatus'];
			dictItemApi.Many(dictArr).then((rs: any) => {
				state.statusOpts = rs.data?.find((a: any) => {
					return a.dictValue === 'TicketStatus';
				})?.items;
			});
		});
		return {
			printMain,
			tableRef,
			onPasswordReset,
			formatStrDate,

			formatDateTime,
			onPrint,
			createOrEditRef,
			resetPasswordRef,
			selectionChange,
			onInit,
			onAdd,
			onReserve,
			onDetail,
			onDelete,
			onDeleteByList,
			onSizechange,
			onCurrentChange,
			onExportAllRecord,
			...toRefs(state),
		};
	},
});
</script>
