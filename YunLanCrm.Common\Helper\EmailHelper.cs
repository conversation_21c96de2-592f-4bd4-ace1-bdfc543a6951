﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace YunLanCrm.Common.Helper
{
    public class EmailHelper
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="subject"></param>
        /// <param name="body"></param>
        /// <param name="entity"></param>
        /// <returns></returns>
        public static (string subject, string body) ProcessTemplate(string subject, string body, object entity)
        {
            if (entity == null)
            {
                return (subject, body);
            }
            // 匹配 {@...} 格式的动态参数
            Regex regex = new Regex("{@([^}]+)}");
            // 获取实体类的类型
            Type entityType = entity.GetType();
            // 处理 Subject
            foreach (Match match in regex.Matches(subject))
            {
                // 获取属性名称
                string propertyName = match.Groups[1].Value;
                // 获取属性信息
                PropertyInfo propertyInfo = entityType.GetProperty(propertyName);
                if (propertyInfo != null)
                {
                    // 获取属性值
                    object value = propertyInfo.GetValue(entity);
                    // 替换动态参数
                    subject = subject.Replace(match.Value, value.ToString());
                }
            }
            // 处理 Body
            foreach (Match match in regex.Matches(body))
            {
                // 获取属性名称
                string propertyName = match.Groups[1].Value;
                // 获取属性信息
                PropertyInfo propertyInfo = entityType.GetProperty(propertyName);
                if (propertyInfo != null)
                {
                    // 获取属性值
                    object value = propertyInfo.GetValue(entity);

                    if (value != null)
                    {
                        // 替换动态参数
                        //body = body.Replace(match.Value, value.ToString());

                        var typeValue = string.Empty;
                        var typeName = propertyInfo.PropertyType.Name;
                        if (typeName == "Nullable`1")//日期
                            typeValue = value.ObjToDate().ToString("MM/dd/yyyy");

                        if (typeName== "String")
                            typeValue = value.ToString();
                        
                        body = body.Replace(match.Value, typeValue);
                    }
                }
            }
            return (subject, body);
        }

        /// <summary>
        /// 获取动态表单对应属性的值
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="propertyName"></param>
        /// <returns></returns>
        public static object GetValueByPropertyName(dynamic obj, string propertyName)
        {
            Type type = obj.GetType();
            PropertyInfo propertyInfo = type.GetProperty(propertyName);
            if (propertyInfo != null)
            {
                return propertyInfo.GetValue(obj, null);
            }
            else
            {
                return null;
            }
        }

    }
}
