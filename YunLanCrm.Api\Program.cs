﻿using Autofac.Extensions.DependencyInjection;
using Serilog;
using Serilog.Filters;
using Serilog.Sinks.MSSqlServer;
using System.Data;
using System.Collections.ObjectModel;
using Serilog.Events;
using YunLanCrm.Common;
using System.Reflection;

//这是asp.net5.0的写法，如果用6 .0，请用本文件代码替换Program.cs代码
namespace YunLanCrm
{
    public class Program
    {
        static string serilogDebug = "Debug.log";
        static string serilogInformation = "Information.log";
        static string serilogWarning = "Warning.log";
        static string serilogError = "Error.log";
        static string serilogFatal = "Fatal.log";

        readonly static string SerilogOutputTemplate =
            "{NewLine}时间：{Timestamp:yyyy-MM-dd HH:mm:ss.fff}{NewLine}日志等级：{Level}{NewLine}所在类：{SourceContext}{NewLine}日志信息：{NewLine}{Message}{NewLine}{Exception}"
            + new string('-', 200);

        [Obsolete]
        public static void Main(string[] args)
        {
            Log.Logger = new LoggerConfiguration()
                //最小的记录等级
                .MinimumLevel.Debug()
                //如果类别是Microsoft开头的话，最小输出级别是information
                .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
                .Enrich.FromLogContext()
                .WriteTo.Console()
                .CreateLogger();

            try
            {
                Host.CreateDefaultBuilder(args)
                    .UseWindowsService()
                    .UseServiceProviderFactory(new AutofacServiceProviderFactory())
                    .ConfigureWebHostDefaults(webBuilder =>
                    {
                        webBuilder
                            .Inject()
                            .UseStartup<Startup>()
                            .ConfigureAppConfiguration((hostingContext, config) =>
                            {
                                config.Sources.Clear();
                                config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: false);
                                //config.AddConfigurationApollo("appsettings.apollo.json");
                            })
                            .ConfigureLogging((hostingContext, builder) =>
                            {

                            })
                            .UseSerilogDefault(config => SetLoggerConfiguration(config))
                            ;
                    })
                    .Build()
                    .Run();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Host terminated unexpectedly");
                Console.ReadLine();
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="config"></param>
        public static void SetLoggerConfiguration(LoggerConfiguration config)
        {
            var sinkOpts = new MSSqlServerSinkOptions();
            sinkOpts.TableName = "LogEvent";
            sinkOpts.AutoCreateSqlTable = true;

            var columnOpts = new ColumnOptions();

            // 去掉属性列
            columnOpts.Store.Remove(StandardColumn.Properties);

            //添加LogEvent列
            columnOpts.Store.Add(StandardColumn.LogEvent);

            // 指定列的长度
            columnOpts.LogEvent.DataLength = 2048;

            // 去掉时间戳的聚集索引
            columnOpts.TimeStamp.NonClusteredIndex = true;

            columnOpts.AdditionalDataColumns = new Collection<DataColumn>
            {
                new DataColumn {DataType = typeof (string), ColumnName = "LogType"},
                new DataColumn {DataType = typeof (string), ColumnName = "DataKey"},
                 new DataColumn {DataType = typeof (string), ColumnName = "DataMessage"},
            };

            config
                .MinimumLevel.Debug()
                .MinimumLevel.Override("Quartz", LogEventLevel.Warning)
                .Filter.ByExcluding(Matching.FromSource("Quartz"))
                .Enrich.FromLogContext()
                .WriteTo.Logger(lc => lc.Filter.ByIncludingOnly(Matching.FromSource("Microsoft.Hosting.Lifetime")).WriteTo.Console())
                .WriteTo.Logger(lc => ConfigureDbLogger(lc, sinkOpts, columnOpts))
                .WriteTo.Logger(lg => ConfigureFileLogger(lg, LogEventLevel.Debug, serilogDebug, SerilogOutputTemplate))
                .WriteTo.Logger(lg => ConfigureFileLogger(lg, LogEventLevel.Information, serilogInformation, SerilogOutputTemplate))
                .WriteTo.Logger(lg => ConfigureFileLogger(lg, LogEventLevel.Warning, serilogWarning, SerilogOutputTemplate))
                .WriteTo.Logger(lg => ConfigureFileLogger(lg, LogEventLevel.Error, serilogError, SerilogOutputTemplate))
                .WriteTo.Logger(lg => ConfigureFileLogger(lg, LogEventLevel.Fatal, serilogFatal, SerilogOutputTemplate))
                //.WriteTo.File($"log/other.log");
            ;
        }

        private static LoggerConfiguration ConfigureDbLogger(LoggerConfiguration lc, MSSqlServerSinkOptions sinkOpts, ColumnOptions columnOpts)
        {
            return lc.Filter.ByIncludingOnly(Matching.WithProperty("WriteToType", "Database"))
                .WriteTo.MSSqlServer(connectionString: Appsettings.GetValue("Serilog:ConnectionString"), sinkOptions: sinkOpts, columnOptions: columnOpts);
        }

        private static LoggerConfiguration ConfigureFileLogger(LoggerConfiguration lg, LogEventLevel level, string defaultFileName, string outputTemplate)
        {
            string assemblyLocation = Assembly.GetExecutingAssembly().Location;
            string projectDirectory = Path.GetDirectoryName(assemblyLocation);

            return lg.Filter.ByIncludingOnly(p => p.Level == level && Matching.WithProperty("WriteToType", "File")(p))
                .WriteTo.Map(
                    "FileName",
                    defaultFileName,
                    (fileName, wt) => wt.Async(a => a.File(
                        Path.Combine(projectDirectory, $"log/{level}/{fileName}"),
                        retainedFileCountLimit: 365,
                        rollingInterval: RollingInterval.Day,
                        shared: true,
                        outputTemplate: outputTemplate
                    )),
                    sinkMapCountLimit: 100
                );
        }

        /// <summary>
        /// 根据环境变量定向配置文件名称
        /// </summary>
        /// <returns></returns>
        private static string GetAppSettingsConfigName()
        {
            if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") != null
                      && Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") != "")
            {
                return $".{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}.";
            }
            else
            {
                return ".";
            }
        }
    }
}
