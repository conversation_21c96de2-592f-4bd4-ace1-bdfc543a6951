﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class cmProjectsApi extends BaseApi {

    // 更新记录
    UpdateBusinessUnit(data?: any) {
        return request({
            url: this.baseurl + 'UpdateBusinessUnit',
            method: 'post',
            data,
        });
    }

    DetailV2(key: any, key1: any) {
        return request({
            url: this.baseurl + 'Detail/' + key + '/' + key1,
            method: 'get',
            params: {},
        });
    }
    GetProjectID(key: any) {
        return request({
            url: this.baseurl + 'GetProjectID/' + key ,
            method: 'get',
            params: {},
        });
    }
}

export default new cmProjectsApi('/api/cmProjects/', 'id');






