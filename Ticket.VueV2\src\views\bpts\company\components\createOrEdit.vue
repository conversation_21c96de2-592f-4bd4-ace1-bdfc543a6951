<template>
	<div class="merchant-detail-container">
		<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="140px" >
			<el-row :gutter="35">
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
					<div class="form-header clearfix">
						<h4><a href="JavaScript:;" class="header-anchor">¶</a>{{
							$t('message.companyBlock.BasicInformation') }}</h4>
					</div>
				</el-col>

				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
					<el-form-item :label="$t('message.companyFields.name')" prop="name">
						<el-input v-model="ruleForm.name" clearable></el-input>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
					<el-form-item :label="$t('message.organizationTable.code')" prop="code" >
						<el-input v-model="ruleForm.code" clearable></el-input>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
					<el-form-item :label="$t('message.organizationTable.parentCompany')">
						<el-tree-select v-model="ruleForm.parentOrgId"
							:placeholder="$t('message.page.selectKeyPlaceholder')" :data="orgTreeData"
							:default-expand-all="true" checkStrictly style="width: 100%" clearable
							:disabled="parseThanZero(ruleForm.companyId) && ruleForm.parentOrgId == ''"
							:props="{ value: 'orgId', label: 'name', disabled: 'disabled' }" filterable
							@change="ChangeParentCompany">
							<template #default="{ data }">
								<SvgIcon name="fa fa-sitemap ti" :size="12"></SvgIcon>
								<span>{{ data.name }}</span>
							</template>
						</el-tree-select>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
					<el-form-item :label="$t('message.companyFields.companyType')">
						<el-select v-model="ruleForm.companyType" filterable clearable style="width: 100%"
							:placeholder="$t('message.page.selectKeyPlaceholder')">
							<el-option v-for="item in companyTypeData" :label="item.itemValue" :value="item.itemValue"
								:key="item.itemId" :description="item.description" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
					<el-form-item :label="$t('message.companyFields.userType')" prop="organizationType">
						<el-select v-model="ruleForm.organizationType"
							:placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100"
							:disabled="disabledCompanyType">
							<el-option v-for="item in userTypeData" :label="item.itemName" :value="item.itemName"
								:key="item.itemId"></el-option></el-select>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
					<el-form-item :label="$t('message.companyFields.phone')">
						<el-input v-model="ruleForm.phone" clearable></el-input>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
					<el-form-item :label="$t('message.companyFields.email')" prop="email">
						<el-input v-model="ruleForm.email" clearable></el-input>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6" v-if="false">
					<el-form-item :label="$t('message.companyFields.fax')">
						<el-input v-model="ruleForm.fax" clearable></el-input>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
					<el-form-item :label="$t('message.companyFields.status')" prop="Status">
						<el-select v-model="ruleForm.status" :placeholder="$t('message.page.selectKeyPlaceholder')"
							clearable class="w100">
							<el-option label="Active" :value="0"></el-option>
							<el-option label="Inactive" :value="1"></el-option>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
					<el-form-item :label="$t('message.cmProjectsFields.businessUnit')">
						<el-select v-model="ruleForm.businessUnit" filterable clearable
							:placeholder="$t('message.page.selectKeyPlaceholder')" style="width: 100%">
							<el-option v-for="item in businessUnitData"
								:label="item.description ? `${item.itemValue} - ${item.description}` : item.itemValue"
								:value="item.itemValue" :key="item.itemId" :description="item.description" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
					<el-form-item :label="$t('message.companyFields.address')">
						<el-input v-model="ruleForm.address" clearable></el-input>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
					<el-form-item :label="$t('message.companyFields.description')">
						<el-input v-model="ruleForm.description" type="textarea" maxlength="150"></el-input>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
					<div class="form-header clearfix">
						<h4><a href="JavaScript:;" class="header-anchor">¶</a>{{
							$t('message.companyBlock.ContactInformation') }}</h4>
					</div>
				</el-col>

				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
					<div style="margin-left:125px;">

						<el-table :data="ruleForm.tableContactData" style="width: 100%">
							<el-table-column prop="contactName" label="Contact Name" class="w100">
								<template #default="scope">
									<el-input v-model="scope.row.contactName" clearable></el-input>
								</template>
							</el-table-column>

							<el-table-column prop="contactCustomerTitle" label="Customer Title" class="w100">
								<template #default="scope">
									<el-input v-model="scope.row.contactCustomerTitle" clearable></el-input>
								</template>
							</el-table-column>

							<el-table-column prop="contactEmailAddress" label="Email Address" class="w100">
								<template #default="scope">
									<el-form-item :prop="`tableContactData.${scope.$index}.contactEmailAddress`"
										:rules="rules.email" label-width="0px">
										<el-input v-model="scope.row.contactEmailAddress" clearable></el-input>
									</el-form-item>
								</template>
							</el-table-column>

							<el-table-column prop="contactTel" label="Contact Tel" class="w100">
								<template #default="scope">
									<el-input v-model="scope.row.contactTel" clearable></el-input>
								</template>
							</el-table-column>

							<el-table-column prop="contactPostCode" label="Post Code" class="w100" v-if="false">
								<template #default="scope">
									<el-input v-model="scope.row.contactPostCode" clearable></el-input>
								</template>
							</el-table-column>

							<el-table-column fixed="right" label="Actions" min-width="120">
								<template #default="scope">
									<el-button link type="primary" size="small"
										@click.prevent="onAddContactItem(scope.$index)">
										Add Item
									</el-button>
									<el-button v-if="scope.$index > 0" link type="danger" size="small"
										@click.prevent="deleteContactRow(scope.$index)">
										Remove
									</el-button>
								</template>
							</el-table-column>
						</el-table>

					</div>
				</el-col>

				<!-- <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
					<el-form-item :label="$t('message.companyFields.linkMan')">
						<el-input v-model="ruleForm.linkMan" clearable></el-input>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
					<el-form-item :label="$t('message.companyFields.linkTel')" prop="linkTel">
						<el-input v-model="ruleForm.linkTel" clearable></el-input>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
					<el-form-item :label="$t('message.companyFields.postCode')" prop="postCode">
						<el-input v-model="ruleForm.postCode" clearable></el-input>
					</el-form-item>
				</el-col> -->

				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" v-if="false">
					<div class="form-header clearfix">
						<h4><a href="JavaScript:;" class="header-anchor">¶</a>{{
							$t('message.companyBlock.GeneralSettings') }}</h4>
					</div>
				</el-col>

				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6" v-if="false">
					<el-form-item :label="$t('message.companyFields.createAt')">
						<MyDate v-model:input="ruleForm.createAt" class="w100" disabled />
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
					<div class="form-header clearfix">
						<h4><a href="JavaScript:;" class="header-anchor">¶</a>
							{{ $t('message.companyBlock.CompanyCategory') }}
						</h4>

					</div>
				</el-col>

				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
					<div style="margin-left:125px;">

						<el-table :data="tableCategoryData" style="width: 100%">
							<el-table-column prop="category" label="Category" class="w100">
								<template #default="scope">
									<el-select v-model="scope.row.category" filterable clearable
										:placeholder="$t('message.page.selectKeyPlaceholder')">
										<el-option v-for="item in CategoryData"
											:label="item.itemValue + ' - ' + item.description" :value="item.itemValue"
											:key="item.itemId" :description="item.description" />
									</el-select>
								</template>
							</el-table-column>

							<el-table-column prop="assignee" label="Default Assignee" class="w100">
								<template #default="scope">
									<el-tree-select :placeholder="$t('message.page.selectKeyPlaceholder')"
										ref="localTree" v-model="scope.row.assignee" :data="assigneeData"
										:render-after-expand="false" node-key="value" show-checkbox check-strictly
										check-on-click-node :auto-expand-parent="true" filterable clearable />
								</template>
							</el-table-column>

							<el-table-column fixed="right" label="Actions" min-width="120">
								<template #default="scope">
									<el-button link type="primary" size="small"
										@click.prevent="onAddCategoryItem(scope.$index)">
										Add Item
									</el-button>
									<el-button v-if="scope.$index > 0" link type="danger" size="small"
										@click.prevent="deleteCategoryRow(scope.$index)">
										Remove
									</el-button>
								</template>
							</el-table-column>
						</el-table>

					</div>
				</el-col>

				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
					<el-form-item>
						<div class="footer-btn">
							<el-button @click="onBack" size="small">
								{{ $t('message.page.buttonCancel') }}
							</el-button>

							<el-button :loading="loading" type="primary" @click="onSubmit" size="small">
								{{ $t('message.page.buttonSave') }}
							</el-button>
						</div>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
	</div>
</template>

<script lang="ts">
import { defineComponent, toRefs, reactive, computed, onMounted, getCurrentInstance, watch, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import { getElcascaderSingle, parseThanZero, isNotEmptyOrNull } from '/@/utils';
import { ElMessage } from 'element-plus';
import { checkEmail } from '/@/utils/toolsValidate';

import organizationApi from '/@/api/organization';
import companyApi from '/@/api/company/index';

import regionApi from '/@/api/region/index';
import dictItemApi from '/@/api/dictItem/index';
import { json } from 'stream/consumers';
import { useI18n } from 'vue-i18n';
import MyDate from '/@/components/ticket/ticketDate.vue';
import Auth from '/@/components/auth/auth.vue';
import mittBus from '/@/utils/mitt';

export default defineComponent({
	name: 'createCompany',
	components: { MyDate, Auth },
	setup() {
		const { t } = useI18n();
		const route = useRoute();
		const router = useRouter();
		const storesUserInfo = useUserInfo();
		const { userInfos } = storeToRefs(storesUserInfo);
		const { proxy } = getCurrentInstance() as any;

		const state = reactive({
			loading: false,
			params: { companyId: 0 },
			ruleForm: {
				sort: 0,
				OrganizationType: '',
				tableContactData: ref([]) as any,
			} as any,
			rules: {
				name: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
				postCode: [{ required: false, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
				address: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
				linkMan: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
				phone: [{ required: false, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
				code: [
					{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' },
					{
						validator: (rule: any, value: any, callback: any) => {
							if (/^(?![0-9]{3})[a-zA-Z0-9!@#$%^&*()]{3}[0-9]{2}$/.test(value) == false ) {
								callback(ElMessage.error({message:"Company code doesn't meet the requirement. <br>Please follow the format of three character + two numeric to set company code.<br>Special character only accepts !@#$%^&*().", dangerouslyUseHTMLString: true}));
								  

							} else {
								callback();
							}
						},
						trigger:  ['blur'],
					},
				],
				organizationType: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
				email: [
					{ required: false, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' },
					{ pattern: /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, message: 'Invalid email', trigger: ['blur', 'change'] },
				],
			},
			orgTreeData: [],
			industryData: [],
			industryArr: [],
			regionData: [],
			regionArr: [],
			userTypeData: [] as any,
			disabledCompanyType: false,
			companyTypeData: [] as any,
			tableCategoryData: ref([]) as any,

			CategoryData: [] as any,
			assigneeData: [] as any,
			businessUnitData: [] as any,
		});

		const currentUser = computed(() => {
			return userInfos.value;
		});

		const onBack = () => {
			mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));
		};

		const onSubmit = () => {
			if (!parseThanZero(state.ruleForm.parentOrgId)) {
				state.ruleForm.parentOrgId = 0;
			}

			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					if (!parseThanZero(state.ruleForm.parentOrgId)) {
						state.ruleForm.parentOrgId = '';
					}

					return;
				}

				var obj = state.ruleForm;

				obj.TableCategoryData = state.tableCategoryData

				state.loading = true;

				companyApi
					.Save(obj)
					.then((rs) => {
						ElMessage.success(t('message.page.saveSuccess'));

						let closePath = '/permission/company/create';
						if (isNotEmptyOrNull(route.params.companyId)) {
							closePath = '/permission/company/edit/:companyId';
						}

						mittBus.emit<any>('RefreshCompanyList', {});

						mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg);
					})
					.finally(() => {
						state.loading = false;
					});
			});
		};

		const getTreeName = (treeList: any, id: any) => {
			for (let i = 0; i < treeList.length; i++) {
				let treeItem = treeList[i];
				if (treeItem.orgId === id) {
					return treeItem;
				} else {
					if (treeItem.children && treeItem.children.length > 0) {
						let res: any = getTreeName(treeItem.children, id);
						if (res) {
							return res;
						}
					}
				}
			}
		};

		watch(
			() => state.ruleForm.parentOrgId,
			async (newValue, oldValue) => {
				let data = getTreeName(state.orgTreeData, newValue);

				state.disabledCompanyType = false;

				if (data != undefined) {
					state.ruleForm.organizationType = data.organizationType;

					if (!isNotEmptyOrNull(route.params.companyId)) {
						state.ruleForm.businessUnit = data.businessUnit;
					}

					if (isNotEmptyOrNull(state.ruleForm.organizationType)) {
						state.disabledCompanyType = true;
					}
				}

				if (!isNotEmptyOrNull(route.params.companyId)) {
					await companyApi.DetailV2(isNotEmptyOrNull(newValue) ? newValue : 0, 'orgId').then((rs) => {
						state.CategoryData = rs.data?.dictItemList.find((a: any) => {
							return a.dictValue === 'Category';
						})?.items;
					});
				}
			}
		);

		onMounted(async () => {
			var dictArr = ['UserType', 'Industry', 'BusinessUnit'];

			dictItemApi.Many(dictArr).then((rs: any) => {
				state.userTypeData = rs.data?.find((a: any) => {
					return a.dictValue === 'UserType';
				})?.items;

				state.companyTypeData = rs.data?.find((a: any) => {
					return a.dictValue === 'Industry';
				})?.items;

				state.businessUnitData = rs.data?.find((a: any) => {
					return a.dictValue === 'BusinessUnit';
				})?.items;
			});

			await organizationApi.Tree({ editCompanyId: route.params.companyId }).then((rs) => {
				state.orgTreeData = rs.data;
			});

			await companyApi.DetailV2(route.params.companyId ?? 0, 'companyId').then((rs) => {
				state.ruleForm = rs.data;

				state.CategoryData = rs.data?.dictItemList.find((a: any) => {
					return a.dictValue === 'Category';
				})?.items;

				state.assigneeData = rs.data?.dictItemList.find((a: any) => {
					return a.dictValue === 'Assignee';
				})?.objectItems.groupList;

				const foundCompanyCategoryAssigneeItem = rs.data?.dictItemList.find((a: any) => {
					return a.dictValue === 'CompanyCategoryAssignee';
				});

				if (foundCompanyCategoryAssigneeItem && foundCompanyCategoryAssigneeItem.objectItems && foundCompanyCategoryAssigneeItem.objectItems.length > 0) {
					state.tableCategoryData = foundCompanyCategoryAssigneeItem.objectItems;
				} else {
					state.tableCategoryData = [
						{
							category: '',
							assignee: '',
						},
						{
							category: '',
							assignee: '',
						},
						{
							category: '',
							assignee: '',
						},
					];
				}

				const foundCompanyContactItem = rs.data?.dictItemList.find((a: any) => {
					return a.dictValue === 'CompanyContact';
				});

				if (foundCompanyContactItem && foundCompanyContactItem.objectItems && foundCompanyContactItem.objectItems.length > 0) {
					state.ruleForm.tableContactData = foundCompanyContactItem.objectItems;
				} else {
					state.ruleForm.tableContactData = [
						{
							contactName: '',
							contactCustomerTitle: '',
							contactEmailAddress: '',
							contactTel: '',
							contactPostCode: '',
						},
					];
				}

				if (!parseThanZero(state.ruleForm.parentOrgId)) {
					state.ruleForm.parentOrgId = '';
				}
			});
		});

		const deleteContactRow = (index: number) => {
			if (state.ruleForm.tableContactData.length > 1) {
				state.ruleForm.tableContactData.splice(index, 1)
			} else {
				ElMessage({
					message: 'At least one row must be present.',
					type: 'warning',
				})
			}
		}

		const onAddContactItem = (index: number) => {
			state.ruleForm.tableContactData.splice(index + 1, 0, {
				contactName: '',
				contactCustomerTitle: '',
				contactEmailAddress: '',
				contactTel: '',
				contactPostCode: '',
			})
		}

		const deleteCategoryRow = (index: number) => {
			if (state.tableCategoryData.length > 1) {
				state.tableCategoryData.splice(index, 1)
			} else {
				ElMessage({
					message: 'At least one row must be present.',
					type: 'warning',
				})
			}
		}

		const onAddCategoryItem = (index: number) => {
			state.tableCategoryData.splice(index + 1, 0, {
				category: '',
				assignee: '',
			})
		}

		const onAddCategoryItemFirst = () => {
			state.tableCategoryData.push({
				category: '',
				assignee: '',
			})
		}

		const ChangeParentCompany = (value: any) => {
			let data = getTreeName(state.orgTreeData, value);

			if (data != undefined) {
				state.ruleForm.businessUnit = data.businessUnit;
			}
		};

		return {
			onSubmit,
			onBack,
			...toRefs(state),
			parseThanZero,
			deleteCategoryRow,
			onAddCategoryItem,
			onAddCategoryItemFirst,
			deleteContactRow,
			onAddContactItem,
			ChangeParentCompany
		};
	},
});
</script>

<style scoped lang="scss">
.merchant-detail-container {
	padding: 10px;
	background-color: #ffffff;
	overflow-y: auto;
	overflow-x: hidden;

	.form-header {
		margin-left: 20px;
		margin-bottom: 20px;
		margin-top: 40px;
	}

	.form-header h4 {
		font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑';
		font-weight: 700;
		font-style: normal;
		text-align: left;
		line-height: 28px;
		font-size: 14px;
		color: #666666;
	}

	.form-header a {
		color: #007bfe;
		margin-right: 5px;
	}

	.footer-btn {
		text-align: center;
		margin-top: 30px;
	}
}
</style>
