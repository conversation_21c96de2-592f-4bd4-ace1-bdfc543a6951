<template>
	<div class="layout-logo" v-if="setShowLogo" @click="onThemeConfigChange">
		<!-- <img :src="logoMini" class="layout-logo-medium-img" /> -->
		<img v-if="isImageVisible" :src="state.info.logoInLeft" :width="state.info.logoInLeftWidth"
			:height="state.info.logoInLeftHeight" />
		<!-- <span>{{ themeConfig.globalTitle }}</span> -->
	</div>
	<div class="layout-logo-size" v-else @click="onThemeConfigChange">
		<img v-if="isMiniImageVisible" :src="state.info.logoInLeftMini" class="layout-logo-size-img"
			:width="state.info.logoInLeftMiniWidth" :height="state.info.logoInLeftMiniHeight"
			 />
	</div>
</template>

<script setup lang="ts" name="layoutLogo">
import { computed, onMounted, reactive, toRef, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import configApi from '/@/api/config/index';
import { Siteinfo } from '/@/types/siteConfig';
// import logoMini from '/@/assets/logo-mini.svg';
//import logoMini from '/img/logo-mini.png';

// 定义变量内容
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const state = reactive({
			info: {}as Siteinfo,
		});

// 设置 logo 的显示。classic 经典布局默认显示 logo
const setShowLogo = computed(() => {
	let { isCollapse, layout } = themeConfig.value;
	return !isCollapse || layout === 'classic' || document.body.clientWidth < 1000;
});
// logo 点击实现菜单展开/收起
const onThemeConfigChange = () => {
	if (themeConfig.value.layout === 'transverse') return false;
	themeConfig.value.isCollapse = !themeConfig.value.isCollapse;
};
onMounted(async() => {
	await configApi.GetAppConfig().then((rs) => {
		state.info = rs.data;
		loadImage(state.info.logoInLeft).then(img => {
			isImageVisible.value = true
		}).catch(err => {
			isImageVisible.value = false
		});

		loadImage(state.info.logoInLeftMini).then(img => {
			isMiniImageVisible.value = true
		}).catch(err => {
			isMiniImageVisible.value = false
		});
	});
});

const isImageVisible = ref(true)


const isMiniImageVisible = ref(true)


const loadImage = (src: any) => {
	return new Promise((resolve, reject) => {
		let img = new Image();
		img.onload = () => resolve(img);
		img.onerror = () => reject(new Error('Image load failed'));
		img.src = src;
	});
}
</script>

<style scoped lang="scss">
.layout-logo {
	padding-left: 20px;
	height: 60px;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: rgb(0 21 41 / 2%) 0px 1px 4px;
	color: var(--el-color-primary);
	font-size: 20px;
	font-weight: bold;
	cursor: pointer;
	animation: logoAnimation 0.3s ease-in-out;

	&:hover {
		span {
			color: var(--color-primary-light-2);
		}
	}

	&-medium-img {
		width: 20px;
		margin-right: 5px;
	}

	.layout-logo-medium-img {
		width: 35px;
		height: 35px;
		margin-right: 10px;
	}
}

.layout-logo-size {
	width: 100%;
	height: 50px;
	display: flex;
	cursor: pointer;
	animation: logoAnimation 0.3s ease-in-out;

	&-img {
		margin: auto;
		width: 45px;
	}

	&:hover {
		img {
			animation: logoAnimation 0.3s ease-in-out;
		}
	}
}
</style>
