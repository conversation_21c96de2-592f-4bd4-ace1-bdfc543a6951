﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.IServices;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.EmailTemplate;
using YunLanCrm.Common.HttpContextUser;
using Microsoft.AspNetCore.Authorization;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class EmailTemplateController : ControllerBase
    {
        private readonly ILogger<EmailTemplateInfo> _logger;
        private readonly IEmailTemplateService _emailTemplateService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="emailTemplateService"></param>
        /// <param name="user"></param>
        /// <param name="logger"></param>
        public EmailTemplateController(IEmailTemplateService emailTemplateService, ILogger<EmailTemplateInfo> logger)
        {
            _logger = logger;
            _emailTemplateService = emailTemplateService;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(EmailTemplateDto req)
        {
            return await _emailTemplateService.AddIdentity(req.Adapt<EmailTemplateInfo>());
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(EmailTemplateDto req)
        {
            return await _emailTemplateService.UpdateAsync(req.Adapt<EmailTemplateInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="templateId">templateId</param>
        /// <returns></returns>
        [HttpPost("Delete/{templateId}")]
        public async Task<bool> Delete(long templateId)
        {
            int result = await _emailTemplateService.Delete(a => a.TemplateId == templateId);

            return result > 0;
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _emailTemplateService.Delete(a => items.Contains(a.TemplateId));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="templateId"></param>
        /// <returns></returns>
        [HttpGet("Get/{templateId}")]
        public async Task<EmailTemplateDto> Get(long templateId)
        {
            return await _emailTemplateService.QueryInfo<EmailTemplateDto>(a => a.TemplateId == templateId);
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<EmailTemplateDto>> GetList([FromQuery] EmailTemplateListQueryDto req)
        {
            return await _emailTemplateService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="templateId"></param>
        /// <returns></returns>
        [HttpGet("Detail/{templateId}")]
        public async Task<EmailTemplateDetailDto> Detail(long templateId)
        {
            return await _emailTemplateService.Detail(templateId);
        }

        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<EmailTemplateDetailDto>> DetailList([FromQuery] EmailTemplateListQueryDto req)
        {
            return await _emailTemplateService.DetailList(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<EmailTemplateDetailDto>> Query([FromQuery] EmailTemplatePageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<EmailTemplateDetailDto>> QueryPost(EmailTemplatePageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<EmailTemplateDetailDto>> PageQuery([FromQuery] EmailTemplatePageQueryDto req)
        {
            return await _emailTemplateService.PageQueryView(req);
        }

    }
}