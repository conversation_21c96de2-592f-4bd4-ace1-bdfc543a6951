﻿using Microsoft.AspNetCore.Mvc;

using LinqKit;
using Mapster;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.WorkflowCrm;
using YunLanCrm.Model.Dto.WorkflowCrm;
using YunLanCrm.Common.HttpContextUser;
using YunLanCrm.Model.Views;
using YunLanCrm.WorkFlow;
using Microsoft.AspNetCore.Authorization;
using YunLanCrm.Dto.WorkflowExecutionLogRecords;
using YunLanCrm.Model.Dto.WorkflowTask;
using YunLanCrm.Model.Dto;
using Newtonsoft.Json;
using YunLanCrm.Common.Helper;
using YunLanCrm.Dto.CmTickets;
using YunLanCrm.IRepositories;
using YunLanCrm.Repositories;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class WorkflowCrmController : ControllerBase
    {
        private readonly ILogger<WorkflowBusinessInfo> _logger;
        private readonly IWorkflowCrmService _workflowCrmService;
        private readonly IUser _user;
        private readonly ICmTicketsRepository _cmTicketsRepository;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="workflowCrmService"></param>
        /// <param name="user"></param>
        /// <param name="logger"></param>
        public WorkflowCrmController(IWorkflowCrmService workflowCrmService, IUser user, ILogger<WorkflowBusinessInfo> logger,ICmTicketsRepository cmTicketsRepository)
        {
            _workflowCrmService = workflowCrmService;
            _logger = logger;
            _user = user;
            _cmTicketsRepository = cmTicketsRepository;
        }

        /// <summary>
        /// 获取实体类的属性
        /// </summary>
        /// <param name="className"></param>
        /// <returns></returns>
        [HttpGet("GetEntityProperties")]
        public List<EntityPropertyInfo> GetEntityProperties(string className)
        {
            return _workflowCrmService.GetEntityProperties(className);
        }

        /// <summary>
        /// 获取流转记录
        /// </summary>
        /// <param name="crmWorkflowId"></param>
        /// <param name="workflowInstanceId"></param>
        /// <returns></returns>
        [HttpGet("GetWorkflowOperations")]
        public async Task<List<WorkflowNodeDataDto>> GetWorkflowOperations(long crmWorkflowId, string workflowInstanceId)
        {
            return await _workflowCrmService.GetWorkflowOperations(crmWorkflowId, workflowInstanceId);
        }

        /// <summary>
        /// 工作流执行
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("WorkflowExecution")]
        public async Task<bool> WorkflowExecution(WorkflowExecutionDto req)
        {
            if (req.Data != null)
            {
               
                var data =JsonHelper.ObjToJson(req.Data);
                

                var mainTicket =JsonHelper.JsonToObj<CmTicketsAddOrUpdateDto>(data);

                //if (string.IsNullOrWhiteSpace(mainTicket.TicketContentsInfo.Ticket_Content))
                //{
                //    throw new Exception("Please input ticket content.");
                //}
                if (string.IsNullOrWhiteSpace(mainTicket.Ticket_Note))
                {
                    throw new Exception("Please input ticket content.");
                }
                #region 如果子工单存在没有solved的，则无法关闭主工单

                //var tickets = await _cmTicketsRepository.Db.Queryable<CmTicketsInfo>().Where(a => a.IsDeleted == false && a.Ticket_Parent_Id != 0)
                //.Select(a => new CmTicktsDataInfo()
                //{
                //    Id = a.Id,
                //    Ticket_Parent_Id = a.Ticket_Parent_Id,
                //    Ticket_Status = a.Ticket_Status,
                //}).ToListAsync();

                //var isChildren = GetChildrenTickets(tickets, ticket.Id);
                //if (!isChildren)
                //{
                //    throw new Exception("Please close the sub ticket.");
                //}

                if (mainTicket.Ticket_Status == Consts.Ticket_Status_Solved)
                {
                    var childTickets = await _cmTicketsRepository.Db.Queryable<CmTicketsInfo>().Where(a =>
                        a.IsDeleted == false && a.Ticket_Parent_Id == mainTicket.Id &&
                        a.Ticket_Status != Consts.Ticket_Status_Solved && a.Ticket_Status != Consts.Ticket_Status_Cancelled).ToListAsync();

                    if (childTickets.Count > 0)
                    {
                        throw new Exception("Please close the sub ticket.");
                    }
                }

                if (mainTicket.Ticket_Status == Consts.Ticket_Status_Cancelled)
                {
                    var childTickets = await _cmTicketsRepository.Db.Queryable<CmTicketsInfo>().Where(a =>
                        a.IsDeleted == false && a.Ticket_Parent_Id == mainTicket.Id &&
                        a.Ticket_Status != Consts.Ticket_Status_Cancelled && a.Ticket_Status != Consts.Ticket_Status_Solved).ToListAsync();

                    if (childTickets.Count > 0)
                    {
                        throw new Exception("Please close the sub ticket.");
                    }
                }


                #endregion

            }

            return await _workflowCrmService.WorkflowExecution(req);
        }

        /// <summary>
        /// 获取自定义的工作流模型
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("WorkflowDefinitions")]
        public async Task<bool> WorkflowDefinitions(ScuiWorkflow req)
        {
            await _workflowCrmService.WorkflowDefinitions(req);

            //await _workflowCrmService.PublishWorkflowDefinition(req);

            return true;
        }

        /// <summary>
        /// 获取所有自定义活动的类型
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetActivityTypes")]
        public async Task<List<string>> GetActivityTypes()
        {
            return await _workflowCrmService.GetActivityTypes();
        }

        /// <summary>
        /// 获取退回节点
        /// </summary>
        /// <param name="workflowInstanceId">实例</param>
        /// <param name="activityId">当前节点</param>
        /// <param name="taskId">当前任务</param>
        /// <returns></returns>
        [HttpGet("GetReturnActivitys")]
        public async Task<List<SelectNodeDto>> GetReturnActivitys(string workflowInstanceId, string activityId, Guid taskId)
        {
            return await _workflowCrmService.GetReturnActivitys(workflowInstanceId, activityId, taskId);
        }

        /// <summary>
        /// 发布工作流
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("SaveWorkflowTemplate")]
        public async Task<bool> SaveWorkflowTemplate(WorkflowTemplateDto req)
        {
            return await _workflowCrmService.SaveWorkflowTemplate(req);
        }

        /// <summary>
        /// 获取流程操作权限
        /// </summary>
        /// <param name="crmWorkflowId"></param>
        /// <param name="workflowInstanceId"></param>
        /// <returns></returns>
        [HttpGet("GetWorkflowPermissions")]
        public async Task<List<WorkflowPermissionDto>> GetWorkflowPermissions(long crmWorkflowId, string workflowInstanceId)
        {
            return await _workflowCrmService.GetWorkflowPermissions(crmWorkflowId, workflowInstanceId, _user.UserId);
        }

        /// <summary>
        /// 获取流程最后的执行节点
        /// </summary>
        /// <param name="workflowInstanceId"></param>
        /// <returns></returns>
        [HttpGet("GetLastActivity")]
        public async Task<LastActivityDto> GetLastActivity(string workflowInstanceId)
        {
            return await _workflowCrmService.GetLastActivity(workflowInstanceId, _user.UserId);
        }

        /// <summary>
        /// 获取当前审批节点的相关信息
        /// </summary>
        /// <param name="workflowInstanceId"></param>
        /// <param name="userTrusteeId"></param>
        /// <returns></returns>
        [HttpGet("GetCurrentApproval")]
        public async Task<CurrentApprovalDto> GetCurrentApproval(string workflowInstanceId,int userTrusteeId)
        {
            return await _workflowCrmService.GetCurrentApproval(workflowInstanceId, userTrusteeId);
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(WorkflowCrmAddDto req)
        {
            return await _workflowCrmService.AddIdentity(req.Adapt<WorkflowBusinessInfo>());
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(WorkflowCrmEditDto req)
        {
            return await _workflowCrmService.Update(req.Adapt<WorkflowBusinessInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="crmWorkflowId">crmWorkflowId</param>
        /// <returns></returns>
        [HttpPost("Delete/{crmWorkflowId}")]
        public async Task<bool> Delete(long crmWorkflowId)
        {
            int result = await _workflowCrmService.Delete(a => a.Id == crmWorkflowId);

            return result > 0;
        }

        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="crmWorkflowId"></param>
        /// <returns></returns>
        [HttpGet("Get/{crmWorkflowId}")]
        public async Task<WorkflowCrmDto> Get(long crmWorkflowId)
        {
            return await _workflowCrmService.QueryInfo<WorkflowCrmDto>(a => a.Id == crmWorkflowId);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="crmWorkflowId"></param>
        /// <returns></returns>
        [HttpGet("Detail/{crmWorkflowId}")]
        public async Task<WorkflowCrmDetailDto> Detail(long crmWorkflowId)
        {
            var obj = await _workflowCrmService.QueryInfo(a => a.Id == crmWorkflowId);

            if (obj != null)
            {
                return await _workflowCrmService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<WorkflowCrmListDto>> List([FromQuery] WorkflowCrmListQueryDto req)
        {
            var list = new List<WorkflowCrmListDto>();
            var where = PredicateBuilder.New<WorkflowBusinessInfo>(true);
            var orderBy = new OrderBy<WorkflowBusinessInfo>(req.order, req.sort);
            var data = await _workflowCrmService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _workflowCrmService.Join(item);
                list.Add(detail.Adapt<WorkflowCrmListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<WorkflowCrmListDto>> Query([FromQuery] WorkflowCrmPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<WorkflowCrmListDto>> QueryPost(WorkflowCrmPageQueryDto req)
        {
            return await PageQuery(req);
        }

        [HttpPost("WorkflowDoneQuery")]
        public async Task<PageQueryResult<WorkflowDoneView>> WorkflowDoneQuery(WorkflowCrmPageQueryDto req)
        {
            return await WorkflowDone(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<WorkflowCrmListDto>> PageQuery([FromQuery] WorkflowCrmPageQueryDto req)
        {
            var list = new List<WorkflowCrmListDto>();
            var where = PredicateBuilder.New<WorkflowBusinessInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            if (req.statusType == "workflowLaunch")
            {
                where.And(a => a.CreatedById == req.userId);
            }

            if (req.statusType == "workflowTodo")
            {
                string str = $",{req.userId},";
                // where.And(a => a.NextHandleValue.Contains(str));
            }

            if (req.statusType == "workflowDone")
            {
                //where.And(a=>a.)
            }

            var totalCount = await _workflowCrmService.CountAsync(where);
            var orderBy = new OrderBy<WorkflowBusinessInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _workflowCrmService.QueryList(where, orderBy, paging);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = await _workflowCrmService.Join(item);
                list.Add(detail.Adapt<WorkflowCrmListDto>());
            }
            #endregion

            return new PageQueryResult<WorkflowCrmListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };

        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<WorkflowDoneView>> WorkflowDone([FromQuery] WorkflowCrmPageQueryDto req)
        {
            var where = PredicateBuilder.New<WorkflowDoneView>(true);


            string[] acTypes = new string[] { "Transfer", "Approve", };

            where.And(a => acTypes.Contains(a.ApprovalResult));

            if (req.userId.HasValue)
            {
                where.And(a => a.Approver == req.userId.Value);
            }

            var totalCount = await _workflowCrmService.CountWorkflowDoneAsync(where);
            var orderBy = new OrderBy<WorkflowDoneView>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _workflowCrmService.QueryWorkflowDoneList(where, orderBy, paging);


            return new PageQueryResult<WorkflowDoneView>()
            {
                Data = data,
                TotalCount = totalCount,
            };

        }

        private bool GetChildrenTickets(List<CmTicktsDataInfo> allList, long? Ticket_Parent_Id)
        {
            var hasChildren = false;

            var children = allList.Where(x => x.Ticket_Parent_Id == Ticket_Parent_Id).ToList();

            if (children != null && children.Count > 0)
            {
                if (children[0].Ticket_Status != Consts.Ticket_Status_Solved)
                {
                    return hasChildren;
                }
                else
                {
                    hasChildren = GetChildrenTickets(allList, children[0].Id);
                }
            }
            else
            {
                hasChildren = true;
                return hasChildren;
            }

            return hasChildren;
        }

        #region 测试接口

        /// <summary>
        /// 触发工作流 （测试）
        /// </summary>
        /// <param name="workflowInstanceId"></param>
        /// <param name="action"></param>
        /// <returns></returns>
        [HttpPost("WorkflowExecutionUserTask")]
        [NonAction]
        public async Task<bool> WorkflowExecutionUserTask(string workflowInstanceId, string action)
        {
            return await _workflowCrmService.WorkflowExecutionUserTask(workflowInstanceId, action);
        }

        /// <summary>
        /// 触发工作流（测试使用，signal模式）
        /// </summary>
        /// <param name="signalName"></param>
        /// <returns></returns>
        [HttpPost("TestWorkflow")]
        [NonAction]
        public async Task<bool> TestWorkflow(string signalName)
        {

            await _workflowCrmService.GetWorkflowPermissions(0, signalName, _user.UserId);

            StartWorkflowDto obj = new StartWorkflowDto();
            obj.Title = "testTitle";
            //obj.Approver = 1;
            obj.DataType = "Test";
            obj.DataId = "1234567890";

            // var startedWorkflows = await _signaler.TriggerSignalAsync(signalName, obj);

            return true;
        }

        #endregion
    }
}