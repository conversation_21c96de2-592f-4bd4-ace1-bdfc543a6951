﻿using System;
using System.IO;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using YunLanCrm.Common;
using YunLanCrm.Common.LogHelper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace YunLanCrm.Extensions.Middlewares
{
    /// <summary>
    /// 中间件
    /// 记录请求和响应数据
    /// </summary>
    public class RequRespLogMiddleware
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly RequestDelegate _next;
        private readonly ILogger<RequRespLogMiddleware> _logger;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="next"></param>
        public RequRespLogMiddleware(RequestDelegate next, ILogger<RequRespLogMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (Appsettings.app("Middleware", "RequestResponseLog", "Enabled").ObjToBool())
            {
                // 过滤，只有接口
                if (context.Request.Path.Value.Contains("api") && context.Request.Method != "OPTIONS")
                {
                    context.Request.EnableBuffering();

                    Stream originalBody = context.Response.Body;

                    try
                    {
                        // 存储请求数据
                        await RequestDataLog(context);

                        using (var ms = new MemoryStream())
                        {
                            context.Response.Body = ms;

                            await _next(context);

                            // 存储响应数据
                            await ResponseDataLog(context, context.Response, ms);

                            ms.Position = 0;

                            await ms.CopyToAsync(originalBody);
                        }
                    }
                    catch (Exception ex)
                    {
                        // 记录异常                        
                        _logger.LogToFile(LogLevel.Error, ex.Message + "" + ex.InnerException);
                    }
                    finally
                    {
                        context.Response.Body = originalBody;
                    }
                }
                else
                {
                    await _next(context);
                }
            }
            else
            {
                await _next(context);
            }
        }

        private async Task RequestDataLog(HttpContext context)
        {
            var request = context.Request;

            var sr = new StreamReader(request.Body);

            var content = $"【请求路径】=> {request.Path + request.QueryString}\r\n";
            content += $"【请求方式】=> {request.Method}\r\n";
            content += $"【请求的数据】=> {await sr.ReadToEndAsync()}";

            if (!string.IsNullOrEmpty(content))
            {
                //Parallel.For(0, 1, e =>
                //{
                //    LogLock.OutSql2Log("RequestResponseLog", new string[] { "Request Data:", content });
                //});

                if (context.Request.Path.Value.IndexOf("RefreshToken") > -1)
                {
                    //_logger.LogToFile(LogLevel.Information, content, nameof(RequRespLogMiddleware) + "/RefreshToken/RequRespLog.log");
                }
                else
                {
                    _logger.LogToFile(LogLevel.Information, content, nameof(RequRespLogMiddleware) + "/UserAccess/RequRespLog.log");
                }

                request.Body.Position = 0;
            }
        }

        private async Task ResponseDataLog(HttpContext context, HttpResponse response, MemoryStream ms)
        {
            ms.Position = 0;

            var responseBody = new StreamReader(ms).ReadToEnd();

            // 去除 Html
            var reg = "<[^>]+>";

            var isHtml = Regex.IsMatch(responseBody, reg);

            if (!string.IsNullOrEmpty(responseBody))
            {
                //Parallel.For(0, 1, e =>
                //{
                //    LogLock.OutSql2Log("RequestResponseLog", new string[] { "Response Data:", ResponseBody });
                //});

                if (context.Request.Path.Value.IndexOf("RefreshToken") > -1)
                {
                    //_logger.LogToFile(LogLevel.Information, "【响应数据】=> " + responseBody, nameof(RequRespLogMiddleware) + "/RefreshToken/RequRespLog.log");
                }
                else
                {
                    _logger.LogToFile(LogLevel.Information, "【响应数据】=> " + responseBody, nameof(RequRespLogMiddleware) + "/UserAccess/RequRespLog.log");
                }
            }
        }
    }
}

