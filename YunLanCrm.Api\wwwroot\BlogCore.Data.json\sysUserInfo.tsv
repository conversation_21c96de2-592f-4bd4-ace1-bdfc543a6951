[{"Id": 1, "LoginName": "admin", "LoginPWD": "2AEFC34200A294A3CC7DB81B43A81873", "RealName": "管理员", "Status": 0, "Remark": null, "CreateTime": "/Date(1546272000000+0800)/", "UpdateTime": "/Date(1546272000000+0800)/", "LastErrorTime": "/Date(1546272000000+0800)/", "ErrorCount": 0, "Name": "admin", "Sex": 1, "Age": 0, "Birth": "/Date(1546272000000+0800)/", "Address": null, "IsDeleted": 0, "Mobile": "15800000001", "Email": "<EMAIL>"}, {"Id": 2, "LoginName": "test", "LoginPWD": "2AEFC34200A294A3CC7DB81B43A81873", "RealName": "测试员", "Status": 0, "Remark": null, "CreateTime": "/Date(1546272000000+0800)/", "UpdateTime": "/Date(1546272000000+0800)/", "LastErrorTime": "/Date(1546272000000+0800)/", "ErrorCount": 0, "Name": "test", "Sex": 1, "Age": 0, "Birth": "/Date(1546272000000+0800)/", "Address": null, "IsDeleted": 0, "Mobile": "15800000002", "Email": "<EMAIL>"}]