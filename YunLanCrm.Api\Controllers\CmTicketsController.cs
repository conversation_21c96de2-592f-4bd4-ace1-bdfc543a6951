﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmTickets;
using YunLanCrm.Services;
using YunLanCrm.Dto.CmTicketContents;
using SkyWalking.NetworkProtocol.V3;
using YunLanCrm.Common.Helper;
using YunLanCrm.Dto.User;
using Elsa;
using YunLanCrm.Model.Views;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmTicketsController : ControllerBase
    {
        private readonly ILogger<CmTicketsInfo> _logger;
        private readonly ICmTicketsService _cmTicketsService;
        private readonly IWorkflowCrmService _workflowCrmService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmTicketsService"></param>
        /// <param name="logger"></param>
        public CmTicketsController(ICmTicketsService cmTicketsService, ILogger<CmTicketsInfo> logger, IWorkflowCrmService workflowCrmService)
        {
            _logger = logger;
            _workflowCrmService = workflowCrmService;
            _cmTicketsService = cmTicketsService;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Save")]
        public async Task<CmTicketsInfo> Save(CmTicketsAddOrUpdateDto req)
        {
            return await _workflowCrmService.SaveCmTickets(req);
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CmTicketsAddOrUpdateDto req)
        {
            return await _cmTicketsService.AddCmTickets(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmTicketsAddOrUpdateDto req)
        {
            return await _cmTicketsService.UpdateCmTickets(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            return await _cmTicketsService.HiddenTicketV2(id);
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public bool Delete(object[] items)
        {
            return false;
        }

        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmTicketsDto> Get(long id)
        {
            return await _cmTicketsService.QueryInfo<CmTicketsDto>(a => a.Id == id);
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmTicketsDto>> GetList([FromQuery] CmTicketsListQueryDto req)
        {
            return await _cmTicketsService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<CmTicketsDetailDto> Detail(string id)
        {
            return await _cmTicketsService.Detail(id);
        }

        [HttpPost("DetailOnHold")]
        public async Task<CmTicketsDetailDto> DetailOnHold(CmTicketsDto req)
        {
            return await _cmTicketsService.DetailOnHold(req.Id,req.Ticket_Parent_Id);
        }

        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmTicketsDetailDto>> DetailList([FromQuery] CmTicketsListQueryDto req)
        {
            return await _cmTicketsService.DetailList(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmTicketsDetailDto>> Query([FromQuery] CmTicketsPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmTicketsDetailDto>> QueryPost(CmTicketsPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmTicketsDetailDto>> PageQuery([FromQuery] CmTicketsPageQueryDto req)
        {
            return await _cmTicketsService.PageQueryView(req);
        }

        [HttpPost("QueryV2")]
        public async Task<PageQueryResult<CmTicketsDetailDto>> QueryPostV2(CmTicketsPageQueryDto req)
        {
            return await _cmTicketsService.PageQueryViewV2(req);
        }

        /// <summary>
        /// 统计
        /// </summary>
        /// <returns></returns>
        [HttpGet("TicketCount")]
        public async Task<CmTicketsCountDto> GetTicketCount()
        {
            return await _cmTicketsService.GetTicketCount();
        }

        [HttpPost("Export")]
        public async Task<IActionResult> Export(CmTicketsPageQueryDto req)
        {
            var isAllCheck = req.ischeckType == 0 ? true : false;
            return await ExcelHelper.Export<Task<PageQueryResult<CmTicketsDetailDto>>>(_cmTicketsService, typeof(ICmTicketsService).Name, "PageQueryView", this, isAllCheck, req);
        }

        /// <summary>
        /// 添加评论
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("SaveComment")]
        public async Task<long> SaveCmTicketComment(CmTicketsAddOrUpdateDto req)
        {
            return await _cmTicketsService.SaveCmTicketComment(req);
        }

        [HttpPost("SaveDescription")]
        public async Task<long> SaveCmTicketDescription(CmTicketsNoteDto req)
        {
            return await _cmTicketsService.SaveCmTicketDescription(req);
        }
        
        [HttpPost("UpdateTicketGrant")]
        public async Task<bool> UpdateTicketGrant(CmTicketsAddOrUpdateDto req)
        {
            CmTicketsView oldTicketInfo = null;

            oldTicketInfo = await _cmTicketsService.GetTicketInfo(req.Id.ObjToLong());

            await _cmTicketsService.UpdateTicket(req);

            await _cmTicketsService.RecordTicketUpdateLog(req, oldTicketInfo);

            return true;
        }
    }
}