﻿<template>
	<div class="roleModulePermission-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="500px">
			<el-form ref="ruleFormRef" label-width="90px" label-position="top">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item>
							<el-input v-model="filterText" placeholder="Filter keyword" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item prop="createId">
							<div class="treeMain">
								<el-tree ref="treeRef" :data="treeApiData" show-checkbox node-key="id" highlight-current
									default-expand-all :props="{ children: 'children', label: 'name' }"
									:default-checked-keys="treeCheckData" :filter-node-method="filterNode" />
							</div>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">cancel</el-button>

					<el-button type="primary" @click="onSubmit" size="small">Save</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, ref, defineComponent, watch, nextTick } from 'vue';
import roleModulePermissionApi from '/@/api/roleModulePermission';
import apiApi from '/@/api/api/index';

export default defineComponent({
	name: 'roleModulePermissionCreateOrEdit',
	components: {},
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const treeRef = ref();
		const state = reactive({
			title: '选择 Api',
			isShowDialog: false,
			saveLoading: false,
			treeApiData: [],
			treeCheckData: [], //已勾选
			filterText: '',
		});
		// 打开弹窗
		const openDialog = (parmas: any, name: any) => {
			state.treeCheckData = parmas;
			state.isShowDialog = true;
			window.console.log('name', name);
			apiApi.Tree().then((rs) => {
				state.treeApiData = rs.data;

				nextTick(() => {
					// 在这里执行您想要在下一个 DOM 更新周期后运行的代码
					if (name) {
						//state.filterText = name.split('.')[0];
						//treeRef.value!.filter(state.filterText);
					}
				});
			});
			nextTick(() => {
				// 在这里执行您想要在下一个 DOM 更新周期后运行的代码
				if (name) state.filterText = name.split('.')[0];
			});
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			var items = treeRef.value.getCheckedNodes();
			items = items.filter((a) => a.gid == null);
			//console.log('it', items);

			context.emit('fetchData', items);
			closeDialog();
		};
		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			state.treeCheckData = [];
			state.treeApiData = [];
			state.filterText = '';
		};
		const filterNode = (value: string, data: any) => {
			if (!value) return true;
			return data.name.toLowerCase().includes(value.toLowerCase());
		};
		watch(
			() => state.filterText,
			(val) => {
				treeRef.value!.filter(val);
			}
		);
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onInitForm,
			treeRef,
			filterNode,
			...toRefs(state),
		};
	},
});
</script>
<style scoped>
.treeMain {
	height: 280px;
	overflow: auto;
	border: 1px solid #dcdfe6;
	margin-bottom: 10px;
	width: 500px;
}
</style>
