﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmTicketDelegate;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmTicketDelegateController : ControllerBase
    {
        private readonly ILogger<CmTicketDelegateInfo> _logger;
        private readonly ICmTicketDelegateService _cmTicketDelegateService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmTicketDelegateService"></param>
        /// <param name="logger"></param>
        public CmTicketDelegateController(ICmTicketDelegateService cmTicketDelegateService, ILogger<CmTicketDelegateInfo> logger)
        {
            _logger = logger;
            _cmTicketDelegateService = cmTicketDelegateService;
            
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CmTicketDelegateAddOrUpdateDto req)
        {
            return await _cmTicketDelegateService.AddCmTicketDelegate(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmTicketDelegateAddOrUpdateDto req)
        {
            return await _cmTicketDelegateService.UpdateCmTicketDelegate(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _cmTicketDelegateService.Delete(a => a.Id == id);

            return result > 0;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _cmTicketDelegateService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmTicketDelegateDto> Get(long id)
        {
            return await _cmTicketDelegateService.QueryInfo<CmTicketDelegateDto>(a => a.Id == id);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmTicketDelegateDto>> GetList([FromQuery] CmTicketDelegateListQueryDto req)
        {
            return await _cmTicketDelegateService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<CmTicketDelegateDetailDto> Detail(long id)
        {
            return await _cmTicketDelegateService.Detail(id);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmTicketDelegateDetailDto>> DetailList([FromQuery] CmTicketDelegateListQueryDto req)
        {
            return await _cmTicketDelegateService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmTicketDelegateDetailDto>> Query([FromQuery] CmTicketDelegatePageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmTicketDelegateDetailDto>> QueryPost(CmTicketDelegatePageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmTicketDelegateDetailDto>> PageQuery([FromQuery] CmTicketDelegatePageQueryDto req)
        {
            return await _cmTicketDelegateService.PageQueryView(req);
        }

    }
}