<template>
	<div class="toggle-settings-container scrolly layout-padding w100">
		<el-form :model="ruleForm" ref="ruleFormRef">
			<el-form-item label="Send Email After User Creation">
				<el-switch v-model="ruleForm.sendEmailAfterUserCreation" size="middle" />
			</el-form-item>
			<el-form-item label="Enable Swagger">
				<el-switch v-model="ruleForm.swaggerEnabled" size="middle" />
			</el-form-item>
			<el-form-item label="Enable Security Validation">
				<el-switch v-model="ruleForm.securityValidation" size="middle" />
			</el-form-item>
			<el-form-item label="Enable Referer Validation">
				<el-switch v-model="ruleForm.refererValidation" size="middle" />
			</el-form-item>
			<el-form-item label="Allow Password Reset" v-if="false">
				<el-switch v-model="ruleForm.allowPasswordReset" size="middle" />
			</el-form-item>
			<el-form-item>
				<el-button v-auth="'systemSetting.Edit'" type="primary" @click="onSave" size="small">{{
					$t('message.page.buttonSave') }}</el-button>
			</el-form-item>
		</el-form>
	</div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, defineComponent, getCurrentInstance, watch } from 'vue';
import { ElMessage } from 'element-plus';
import configApi from '/@/api/config/index';

export default defineComponent({
	name: 'ToggleSettings',
	components: {},
	props: {
		toggleObj: {
			type: Object,
			default: {},
		},
	},
	setup(props: any) {
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			loading: false,
			ruleForm: {},
			rules: {},
		});

		const onSave = () => {
			proxy.$refs.ruleFormRef.validate((valid: boolean) => {
				if (!valid) {
					return;
				}
				var obj = { Toggles: state.ruleForm };
				state.loading = true;
				configApi
					.SaveConfig(obj)
					.then(() => {
						ElMessage.success('Saved Successfully');
					})
					.finally(() => {
						state.loading = false;
					});
			});
		};

		watch(
			() => props.toggleObj,
			(newVal) => {
				state.ruleForm = props.toggleObj || {};
			},
			{ deep: true }
		);

		return {
			onSave,
			...toRefs(state),
		};
	},
});
</script>

<style scoped>
.toggle-settings-container {
	padding: 20px;
}
</style>
