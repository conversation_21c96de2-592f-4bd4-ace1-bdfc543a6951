﻿<template>
	<div class="emails-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="769px" :close-on-click-modal="false" draggable>
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="90px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item label="subject" prop="subject">
							<el-input v-model="ruleForm.subject" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item label="body" prop="body">
							<el-input v-model="ruleForm.body" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item label="fromAddr" prop="fromAddr">
							<el-input v-model="ruleForm.fromAddr" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item label="fromName" prop="fromName">
							<el-input v-model="ruleForm.fromName" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item label="toAddrs" prop="toAddrs">
							<el-input v-model="ruleForm.toAddrs" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item label="ccAddrs" prop="ccAddrs">
							<el-input v-model="ruleForm.ccAddrs" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item label="dataType" prop="dataType">
							<el-input v-model="ruleForm.dataType" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item label="status" prop="status">
							<el-select v-model="ruleForm.status" placeholder="Select" clearable class="w100">
								<el-option label="选项1" value="1"></el-option>
								<el-option label="选项2" value="2"></el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item label="description" prop="description">
							<el-input v-model="ruleForm.Msg" type="textarea" placeholder="请输入备注" maxlength="150"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
					<el-button v-if="dialogParams.action === 'Create'" @click="onInitForm" size="small" type="danger">{{
						$t('message.page.buttonReset')
					}}</el-button>
					<el-button v-if="dialogParams.action === 'Edit'" @click="onDelete" type="danger" size="small">{{
						$t('message.page.buttonDelete')
					}}</el-button>
					<el-button :loading="saveLoading" type="primary" @click="onSubmit" size="small">{{ $t('message.page.buttonSave') }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';

import emailsApi from '/@/api/emails';
import { useI18n } from 'vue-i18n';

interface DialogParams {
	action: string;
	id: number;
}

export default defineComponent({
	name: 'emailsCreateOrEdit',
	components: {},
	setup(props, context) {
		const { t } = useI18n();
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			title: 'Create Emails',
			isShowDialog: false,
			saveLoading: false,
			deleteLoading: false,
			dialogParams: {
				action: '',
				id: -1,
			},
			ruleForm: {
				id: 0, //
				subject: '', //
				body: '', //
				fromAddr: '', //
				fromName: '', //
				toAddrs: '', //
				ccAddrs: '', //
				dataType: '', //
				status: '', //
				isDeleted: false, //
				createById: 0, //
				createTime: new Date(), //
				modifiedById: 0, //
				modifiedTime: new Date(), //
				description: '', //
			},
			rules: {
				id: [{ required: true, message: 'Please input', trigger: 'blur' }],
				subject: [{ required: true, message: 'Please input', trigger: 'blur' }],
				body: [{ required: true, message: 'Please input', trigger: 'blur' }],
				fromAddr: [{ required: true, message: 'Please input', trigger: 'blur' }],
				fromName: [{ required: true, message: 'Please input', trigger: 'blur' }],
				toAddrs: [{ required: true, message: 'Please input', trigger: 'blur' }],
				ccAddrs: [{ required: true, message: 'Please input', trigger: 'blur' }],
				dataType: [{ required: true, message: 'Please input', trigger: 'blur' }],
				status: [{ required: true, message: 'Please input', trigger: 'blur' }],
				isDeleted: [{ required: true, message: 'Please input', trigger: 'blur' }],
				createTime: [{ type: 'date', required: true, message: 'Pick date', trigger: 'change' }],
				modifiedTime: [{ type: 'date', required: true, message: 'Pick date', trigger: 'change' }],

				//type1: [{ type: 'array', required: true, message: '', trigger: 'change' }],
			},
		});
		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			state.dialogParams = parmas;

			onInitForm();

			if (parmas.action == 'Create') {
				state.title = 'Create emails';
			} else if (parmas.action == 'Edit') {
				state.title = 'Edit Emails';
				getData(parmas.id);
			} else {
				ElMessage.error('Parameter action cannot be empty.');
			}
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}
				var obj = state.ruleForm;
				state.saveLoading = true;
				emailsApi
					.SendEmail(obj)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						//ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};
		//根据id获取完整的信息
		const getData = (id: any) => {
			emailsApi.GetByKey(id).then((rs) => {
				state.ruleForm = Object.assign({}, rs.data);
			});
		};
		//删除纪录
		const onDelete = () => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				state.deleteLoading = true;
				emailsApi
					.DeleteByKey(state.ruleForm.id)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.deleteLoading = false;
					});
			});
		};
		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.ruleForm = {
				id: 0, //
				subject: '', //
				body: '', //
				fromAddr: '', //
				fromName: '', //
				toAddrs: '', //
				ccAddrs: '', //
				dataType: '', //
				status: '', //
				isDeleted: false, //
				createById: 0, //
				createTime: new Date(), //
				modifiedById: 0, //
				modifiedTime: new Date(), //
				description: '', //
			};
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			...toRefs(state),
		};
	},
});
</script>
