[{"IsDeleted": 0, "ParentId": null, "Name": "values接口", "LinkUrl": "/api/values", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 1, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": null, "CreateBy": null, "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 1}, {"IsDeleted": 0, "ParentId": null, "Name": "claims的接口", "LinkUrl": "/api/claims", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 1, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": null, "CreateBy": null, "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 2}, {"IsDeleted": 0, "ParentId": null, "Name": "UserRole接口", "LinkUrl": "/api/UserRole", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 1, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": null, "CreateBy": null, "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 3}, {"IsDeleted": 0, "ParentId": null, "Name": null, "LinkUrl": "/api/v2/Apb/apbs", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 1, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": null, "CreateBy": null, "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 4}, {"IsDeleted": 0, "ParentId": null, "Name": "修改 tibug 文章", "LinkUrl": "/api/TopicDetail/update", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 1, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": null, "CreateBy": null, "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 5}, {"IsDeleted": 0, "ParentId": null, "Name": "删除tibug文章", "LinkUrl": "/api/TopicDetail/delete", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 1, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": null, "CreateBy": null, "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 6}, {"IsDeleted": 0, "ParentId": null, "Name": "获取用户", "LinkUrl": "/api/user/get", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 1, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": null, "CreateBy": null, "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 7}, {"IsDeleted": 0, "ParentId": null, "Name": "获取用户详情", "LinkUrl": "/api/user/get/\\d+", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 1, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": null, "CreateBy": null, "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 8}, {"IsDeleted": 1, "ParentId": null, "Name": "角色接口", "LinkUrl": "/api/role", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 9}, {"IsDeleted": 0, "ParentId": null, "Name": "添加用户", "LinkUrl": "/api/user/post", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 10}, {"IsDeleted": 0, "ParentId": null, "Name": "删除用户", "LinkUrl": "/api/user/delete", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 11}, {"IsDeleted": 0, "ParentId": null, "Name": "修改用户", "LinkUrl": "/api/user/put", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 12}, {"IsDeleted": 0, "ParentId": null, "Name": "获取api接口", "LinkUrl": "/api/module/get", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 13}, {"IsDeleted": 0, "ParentId": null, "Name": "删除api接口", "LinkUrl": "/api/module/delete", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 14}, {"IsDeleted": 0, "ParentId": null, "Name": "修改api接口", "LinkUrl": "/api/module/put", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 15}, {"IsDeleted": 0, "ParentId": null, "Name": "添加api接口", "LinkUrl": "/api/module/post", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 16}, {"IsDeleted": 0, "ParentId": null, "Name": "获取菜单", "LinkUrl": "/api/permission/get", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 17}, {"IsDeleted": 0, "ParentId": null, "Name": "删除菜单", "LinkUrl": "/api/permission/delete", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 18}, {"IsDeleted": 0, "ParentId": null, "Name": "修改菜单", "LinkUrl": "/api/permission/put", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 19}, {"IsDeleted": 0, "ParentId": null, "Name": "添加菜单", "LinkUrl": "/api/permission/post", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 20}, {"IsDeleted": 0, "ParentId": null, "Name": "获取菜单树", "LinkUrl": "/api/permission/getpermissiontree", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 21}, {"IsDeleted": 0, "ParentId": null, "Name": "获取角色", "LinkUrl": "/api/role/get", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 22}, {"IsDeleted": 0, "ParentId": null, "Name": "删除角色", "LinkUrl": "/api/role/delete", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 23}, {"IsDeleted": 0, "ParentId": null, "Name": "修改角色", "LinkUrl": "/api/role/put", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 24}, {"IsDeleted": 0, "ParentId": null, "Name": "添加角色", "LinkUrl": "/api/role/post", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 25}, {"IsDeleted": 0, "ParentId": null, "Name": "获取bug", "LinkUrl": "/api/TopicDetail/Get", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 26}, {"IsDeleted": 0, "ParentId": null, "Name": "获取博客", "LinkUrl": "/api/Blog", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 18, "CreateBy": "提bug账号", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 27}, {"IsDeleted": 0, "ParentId": null, "Name": "保存分配", "LinkUrl": "/api/permission/Assign", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 23, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 28}, {"IsDeleted": 0, "ParentId": null, "Name": "Get导航条", "LinkUrl": "/api/permission/GetNavigationBar", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 23, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 29}, {"IsDeleted": 1, "ParentId": null, "Name": "test", "LinkUrl": "/api/Blog/delete1", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 30}, {"IsDeleted": 1, "ParentId": null, "Name": "test", "LinkUrl": "/api/Blog/delete2", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 31}, {"IsDeleted": 0, "ParentId": null, "Name": "删除博客", "LinkUrl": "/api/Blog/delete", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 32}, {"IsDeleted": 0, "ParentId": null, "Name": "获取全部日志", "LinkUrl": "/api/Monitor/get", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 33}, {"IsDeleted": 1, "ParentId": null, "Name": "Agent -测试- 快速添加接口权限", "LinkUrl": "/api/Agent/get", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 34}, {"IsDeleted": 1, "ParentId": null, "Name": "test", "LinkUrl": "/api/test/get", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 35}, {"IsDeleted": 0, "ParentId": null, "Name": "Department - 测试新建api -  部门管控", "LinkUrl": "/api/Department/get", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 36}, {"IsDeleted": 0, "ParentId": null, "Name": "获取任务调取分页", "LinkUrl": "/api/TasksQz/get", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 37}, {"IsDeleted": 0, "ParentId": null, "Name": "添加任务", "LinkUrl": "/api/TasksQz/Post", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 38}, {"IsDeleted": 0, "ParentId": null, "Name": "编辑任务", "LinkUrl": "/api/TasksQz/put", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 39}, {"IsDeleted": 0, "ParentId": null, "Name": "开启任务", "LinkUrl": "/api/TasksQz/StartJob", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 40}, {"IsDeleted": 0, "ParentId": null, "Name": "停止任务", "LinkUrl": "/api/TasksQz/StopJob", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 41}, {"IsDeleted": 0, "ParentId": null, "Name": "重启任务", "LinkUrl": "/api/TasksQz/ReCovery", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 42}, {"IsDeleted": 0, "ParentId": null, "Name": "删除任务", "LinkUrl": "/api/TasksQz/Delete", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 43}, {"IsDeleted": 0, "ParentId": null, "Name": "暂停任务", "LinkUrl": "/api/TasksQz/PauseJob", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 44}, {"IsDeleted": 0, "ParentId": null, "Name": "恢复任务", "LinkUrl": "/api/TasksQz/ResumeJob", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 45}, {"IsDeleted": 0, "ParentId": null, "Name": "获取任务类名", "LinkUrl": "/api/TasksQz/GetTaskNameSpace", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 12, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 46}, {"Id": "47", "IsDeleted": 0, "Name": "微信获取", "LinkUrl": "/api/WeChatConfig/get", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-03-22 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2020-04-06 00:00:00", "ParentId": 0}, {"Id": "48", "IsDeleted": 0, "Name": "微信客户批量删除", "LinkUrl": "/api/WeChatCompany/BatchDelete", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-04-06 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2020-04-06 00:00:00", "ParentId": 0}, {"Id": "49", "IsDeleted": 0, "Name": "微信客户删除", "LinkUrl": "/api/WeChatCompany/delete", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-04-06 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2020-04-06 00:00:00", "ParentId": 0}, {"Id": "50", "IsDeleted": 0, "Name": "微信客户获取", "LinkUrl": "/api/WeChatCompany/get", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-04-06 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2020-04-06 00:00:00", "ParentId": 0}, {"Id": "51", "IsDeleted": 0, "Name": "微信客户添加", "LinkUrl": "/api/WeChatCompany/post", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-04-06 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2020-04-06 00:00:00", "ParentId": 0}, {"Id": "52", "IsDeleted": 0, "Name": "微信客户更新", "LinkUrl": "/api/WeChatCompany/put", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-04-06 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2020-04-06 00:00:00", "ParentId": 0}, {"Id": "53", "IsDeleted": 0, "Name": "微信公众号批量删除", "LinkUrl": "/api/WeChatConfig/BatchDelete", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-03-25 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2020-04-06 00:00:00", "ParentId": 0}, {"Id": "54", "IsDeleted": 0, "Name": "微信公众号获取", "LinkUrl": "/api/WeChatConfig/get", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-03-22 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2020-04-06 00:00:00", "ParentId": 0}, {"Id": "55", "IsDeleted": 0, "Name": "获取公众号菜单设置", "LinkUrl": "/api/WeChat/GetMenu", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-04-06 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2021-09-29 00:00:00", "ParentId": 0}, {"Id": "56", "IsDeleted": 0, "Name": "获取订阅用户", "LinkUrl": "/api/WeChat/GetSubUsers", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-04-23 16:20:30", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2021-09-29 00:00:00", "ParentId": 0}, {"Id": "57", "IsDeleted": 0, "Name": "获取消息模板列表", "LinkUrl": "/api/WeChat/GetTemplate", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-04-08 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2021-09-29 00:00:00", "ParentId": 0}, {"Id": "58", "IsDeleted": 0, "Name": "微信公众号更新", "LinkUrl": "/api/WeChatConfig/post", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-03-24 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2020-04-06 00:00:00", "ParentId": 0}, {"Id": "59", "IsDeleted": 0, "Name": "微信公众号添加", "LinkUrl": "/api/WeChatConfig/put", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-03-24 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2020-04-06 00:00:00", "ParentId": 0}, {"Id": "60", "IsDeleted": 0, "Name": "刷新Token", "LinkUrl": "/api/WeChat/RefreshToken", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-03-30 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2021-09-29 00:00:00", "ParentId": 0}, {"Id": "61", "IsDeleted": 0, "Name": "更新微信菜单设置", "LinkUrl": "/api/WeChat/UpdateMenu", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-04-06 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2021-09-29 00:00:00", "ParentId": 0}, {"Id": "62", "IsDeleted": 0, "Name": "获取推送记录", "LinkUrl": "/api/WeChatPushLog/get", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-04-08 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2021-09-29 00:00:00", "ParentId": 0}, {"Id": "63", "IsDeleted": 0, "Name": "获取绑定用户", "LinkUrl": "/api/WeChatSub/get", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-04-23 16:20:47", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2020-04-23 00:00:00", "ParentId": 0}, {"Id": "64", "IsDeleted": 0, "Name": "微信公众号删除", "LinkUrl": "/api/WeChatConfig/delete", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 8, "CreateBy": "test", "CreateTime": "2020-03-24 00:00:00", "ModifyId": null, "ModifyBy": null, "ModifyTime": "2020-04-06 00:00:00", "ParentId": 0}, {"IsDeleted": false, "Name": "获取部门数据", "LinkUrl": "/api/department/get", "OrderSort": 0, "IsMenu": false, "Enabled": true, "CreateId": 12, "CreateBy": "blogadmin", "CreateTime": "2022-03-23 00:00:00", "ModifyTime": "2022-03-23 00:00:00", "ParentId": 0, "Id": 65}, {"IsDeleted": false, "Name": "获取部门数据树表格", "LinkUrl": "/api/permission/GetTreeTable", "OrderSort": 0, "IsMenu": false, "Enabled": true, "CreateId": 12, "CreateBy": "blogadmin", "CreateTime": "2022-03-23 00:00:00", "ModifyTime": "2022-03-23 00:00:00", "ParentId": 0, "Id": 66}, {"IsDeleted": false, "Name": "删除部门", "LinkUrl": "/api/department/delete", "OrderSort": 0, "IsMenu": false, "Enabled": true, "CreateId": 12, "CreateBy": "blogadmin", "CreateTime": "2022-03-23 00:00:00", "ModifyTime": "2022-03-23 00:00:00", "ParentId": 0, "Id": 67}, {"IsDeleted": false, "Name": "更新部门", "LinkUrl": "/api/department/put", "OrderSort": 0, "IsMenu": false, "Enabled": true, "CreateId": 12, "CreateBy": "blogadmin", "CreateTime": "2022-03-23 00:00:00", "ModifyTime": "2022-03-23 00:00:00", "ParentId": 0, "Id": 68}, {"IsDeleted": false, "Name": "添加部门", "LinkUrl": "/api/department/post", "OrderSort": 0, "IsMenu": false, "Enabled": true, "CreateId": 12, "CreateBy": "blogadmin", "CreateTime": "2022-03-23 00:00:00", "ModifyTime": "2022-03-23 00:00:00", "ParentId": 0, "Id": 69}, {"IsDeleted": false, "Name": "获取部门树", "LinkUrl": "/api/department/getDepartmentTree", "OrderSort": 0, "IsMenu": false, "Enabled": true, "CreateId": 12, "CreateBy": "blogadmin", "CreateTime": "2022-03-23 00:00:00", "ModifyTime": "2022-03-23 00:00:00", "ParentId": 0, "Id": 70}, {"IsDeleted": 0, "ParentId": null, "Name": "Get导航条Pro", "LinkUrl": "/api/permission/GetNavigationBarPro", "Area": null, "Controller": null, "Action": null, "Icon": null, "Code": null, "OrderSort": 0, "Description": null, "IsMenu": 0, "Enabled": 1, "CreateId": 23, "CreateBy": "后台总管理员", "CreateTime": "/Date(1546272000000+0800)/", "ModifyId": null, "ModifyBy": null, "ModifyTime": "/Date(1546272000000+0800)/", "Id": 71}]