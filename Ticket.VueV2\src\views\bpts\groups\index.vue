<template>
	<div class="list-page-layout">
		<el-container style="height: 100%;">
			<el-header style="height: auto">
				<el-card class="list-search-card" shadow="never" style="width: 100%; " :body-style="{ paddingTop: '10px' }">
					<div class="list-index-search">
						<el-form label-width="150px" @keyup.enter="onSearch">
							<el-row :gutter="35">
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="8" class="mb6">
									<el-form-item :label="$t('message.groupFields.name')">
										<el-input
											
											v-model="groupsTable.params.group_name"
											clearable
											class="w-20"
										></el-input>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="8" class="mb6">
									<el-form-item :label="$t('message.groupFields.description')">
										<el-input
											
											v-model="groupsTable.params.group_description"
											clearable
											class="w-20"
										></el-input>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="8" class="mb6">
									<el-form-item :label="$t('message.groupFields.status')">
										<el-select
											:placeholder="$t('message.page.selectKeyPlaceholder')"
											v-model="groupsTable.params.group_status"
											clearable
											class="w-20"
										>
											<el-option :label="$t('message.userFields.Active')" :value="0"></el-option>
											<el-option :label="$t('message.userFields.Inactive')" :value="1"></el-option>
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="8" class="mb6">
									<el-form-item>
										<el-button size="small" type="primary" @click="onSearch"> {{ $t('message.page.buttonSearch') }} </el-button>
										<el-button size="small" type="danger" @click="onSearchReSet"> {{ $t('message.page.buttonReset') }} </el-button>
									</el-form-item>
								</el-col>
							</el-row>
						</el-form>
					</div>
				</el-card>
			</el-header>

			<el-header class="table_header mt5" style="display: flex">
				<div class="left-panel">
					<el-button v-auth="'ticketGroups.Create'" size="small" type="primary" class="ml10" @click="onOpenAddGroup">
						{{ $t('message.groupButtons.createGroup') }}
					</el-button>
				</div>
				<div class="right-panel">
					<el-dropdown>
						<el-button v-auth="'ticketGroups.Export'" type="primary" size="small" class="ml10">
							<el-icon>
								<ele-ArrowDownBold />
							</el-icon>
							{{ $t('message.page.buttonExport') }}
						</el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item @click="onExportAllRecord(0)">{{ $t('message.page.buttonExportEntireList') }}</el-dropdown-item>
								<el-dropdown-item @click="onExportAllRecord(1)">{{ $t('message.page.buttonExportSelectedRecords') }}</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</div>
			</el-header>

			<el-main style="flex: 1" ref="printMain">
				<div class="scTable" style="height: 100%" ref="scTableMain">
					<div class="scTable-table">
						<el-table
							:data="groupsTable.data"
							:v-loading="groupsTable.loading"
							height="calc(100%)"
							table-layout="fixed"
							style="width: 100%"
							row-key="id"
							lazy
							@selection-change="selectionChange"
							@sort-change="onSortChange"
							:row-style="{ height: '40px' }"
							:cell-style="{ padding: '0px' }" 
							stripe border
						>
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')" :image-size="100"></el-empty>
							</template>
							<el-table-column type="selection" />

							<el-table-column sortable :label="$t('message.groupFields.name')" show-overflow-tooltip>
								<template #default="scope">
									<span class="ml10" style="padding-right: 10px">{{ $t(scope.row.group_Name) }} ({{ scope.row.group_User_Count }})</span>

									<el-tag class="ml-2" v-if="scope.row.default_Group == true">{{ $t('message.groupFields.ShowDefault') }}</el-tag>
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.groupFields.description')" prop="group_Description" show-overflow-tooltip>
							</el-table-column>

							<el-table-column sortable :label="$t('message.groupFields.status')" show-overflow-tooltip>
								<template #default="scope">
									<el-tag type="danger" v-if="scope.row.group_Status == '1'">{{ $t('message.userFields.Inactive') }}</el-tag>
									<el-tag type="success" v-if="scope.row.group_Status == '0'">{{ $t('message.userFields.Active') }}</el-tag>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.page.actions')" show-overflow-tooltip width="140">
								<template #default="scope">
									<el-button v-auth="'ticketGroups.Edit'" size="mini" type="text" @click="onOpenEditGroups(scope.row)">
										{{ $t('message.page.actionsEdit') }}
									</el-button>

									<el-button v-auth="'ticketGroups.Delete'" size="mini" type="text" style="color: #ff3a3a" @click="onTabelRowDel(scope.row)">
										{{ $t('message.page.actionsDelete') }}
									</el-button>
								</template>
							</el-table-column>
						</el-table>

						<div class="scTable-page">
							<el-pagination
								@size-change="onSizechange"
								@current-change="onCurrentChange"
								:pager-count="5"
								:page-sizes="[15, 20, 30]"
								v-model:current-page="groupsTable.params.pageIndex"
								background
								v-model:page-size="groupsTable.params.pageSize"
								layout="total, sizes, prev, pager, next, jumper"
								:total="groupsTable.total"
								small
							>
							</el-pagination>
						</div>
					</div>
				</div>
			</el-main>

			<EditGroups ref="editGroupsRef" @fetchData="onInit" />
		</el-container>
	</div>
</template>

<script lang="ts">
import { nextTick, ref, toRefs, reactive, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import EditMenu from '/@/views/bpts/menu/component/createOrEdit.vue';
import EditGroups from '/@/views/bpts/groups/component/createOrEdit.vue';
// import { useStore } from '/@/store/index';
import { useI18n } from 'vue-i18n';
import groupsApi from '/@/api/cmGroups/index';
import { formatDateTime } from '/@/utils/formatTime';

export default {
	name: 'ticketGroups',
	components: { EditMenu, EditGroups },
	setup() {
		const { t } = useI18n();
		const editGroupsRef = ref();
		// const store = useStore();
		const state = reactive({
			groupsTable: {
				data: [],
				loading: false,
				total: 0,
				selection: [],
				params: {
					pageIndex: 1,
					pageSize: 15,
					searchKey: '',
					order: 'CreatedAt',
					sort: 'desc',
					group_name: '',
					group_description: '',
					group_status: '',
					ids: [],
					ischeckType: 0,
				},
			},
		});
		//初始化

		const onInit = () => {
			state.groupsTable.loading = true;
			groupsApi
				.Query(state.groupsTable.params)
				.then((rs: any) => {
					state.groupsTable.data = rs.data;
					state.groupsTable.total = rs.totalCount;
				})
				.catch(() => {})
				.finally(() => (state.groupsTable.loading = false));
		};

		// 打开新增菜单弹窗
		const onOpenAddGroup = () => {
			editGroupsRef.value.openDialog({ action: 'Create' });
		};
		// 打开编辑菜单弹窗
		const onOpenEditGroups = (row: object) => {
			editGroupsRef.value.openDialog({ action: 'Edit', id: row.id });
		};

		// 删除当前行 1
		const onTabelRowDel = (row: object) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal:false,
			})
				.then(() => {
					groupsApi
						.DeleteByKey(row.id)
						.then((rs) => {
							ElMessage.success(t('message.page.deleteSuccess'));
							onInit();
						})
						.catch((rs) => {
							nextTick(() => {
								ElMessageBox.alert(t('message.page.existCanNotDelete'), t('message.page.dlgTip'), {
									type: 'warning',
								});
							});
						});
				})
				.catch(() => {});
		};
		const onSearch = () => {
			onInit();
		};

		const initFormData = () => {
			state.groupsTable.params = {
				pageIndex: 1,
				pageSize: 15,
				searchKey: '',
				order: 'CreatedAt',
				sort: 'desc',
				category_Code: '',
				category_Description: '',
				category_Status: '',
			} as any;
		};

		const onSearchReSet = () => {
			initFormData();
			onInit();
		};

		const selectionChange = (selection: any) => {
			state.groupsTable.selection = selection;
		};

		const onSizechange = (val: number) => {
			state.groupsTable.params.pageSize = val;
			onInit();
		};
		const onCurrentChange = (val: number) => {
			state.groupsTable.params.pageIndex = val;
			onInit();
		};
		const onSortChange = (column: any) => {
			state.groupsTable.params.order = column.prop;
			state.groupsTable.params.sort = column.order;
			onInit();
		};

		const onExportAllRecord = (selectAll: number) => {
			let currPageSize = state.groupsTable.params.pageSize;
			state.groupsTable.params.pageSize = 10000;

			var checkSelection = state.groupsTable.selection;
			let ids_arr = [] as any;

			checkSelection.forEach(function (item, index) {
				let idjson = {} as any;
				idjson = item.id;
				ids_arr.push(idjson);
			});
			state.groupsTable.params.ischeckType = selectAll;
			if (selectAll == 1 && ids_arr.length == 0) {
				ElMessage.error(t('message.userFields.SelectTheDataYouWantToExport'));
				return;
			}
			if (selectAll == 0) {
				state.groupsTable.params.ids = [];
			} else {
				state.groupsTable.params.ids = ids_arr;
			}

			groupsApi
				.Export(state.groupsTable.params)
				.then((rs) => {
					downloadCallback(rs);
				})
				.catch((rs) => {})
				.finally(() => (state.groupsTable.loading = false));

			state.groupsTable.params.pageSize = currPageSize;
		};

		const downloadCallback = (rs) => {
			let data = rs;
			var newBlob = new Blob([data], { type: 'text/plain;charset=UTF-8' });
			var anchor = document.createElement('a');
			anchor.download = 'Groups_' + formatDateTime() + '.xlsx';
			anchor.href = window.URL.createObjectURL(newBlob);
			anchor.click();
		};

		// 页面加载时
		onMounted(() => {
			onInit();
		});

		return {
			editGroupsRef,
			onSearch,
			formatDateTime,
			onSearchReSet,
			onOpenAddGroup,
			onOpenEditGroups,
			onTabelRowDel,
			onInit,
			selectionChange,
			onSizechange,
			onCurrentChange,
			onSortChange,
			onExportAllRecord,
			...toRefs(state),
		};
	},
};
</script>

<style scoped>
.menu-index-container {
	padding: 10px;
}
</style>
