﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.OperateLog;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class OperateLogController : ControllerBase
    {
        private readonly ILogger<OperateLogInfo> _logger;

        private readonly IOperateLogService _operateLogService;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="operateLogService"></param>
        /// <param name="logger"></param>
        public OperateLogController(IOperateLogService operateLogService, ILogger<OperateLogInfo> logger)
        {
            this._operateLogService = operateLogService;
            this._logger = logger;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(OperateLogAddDto req)
        {
            return await _operateLogService.AddIdentity(req.Adapt<OperateLogInfo>());
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(OperateLogEditDto req)
        {
            return await _operateLogService.Update(req.Adapt<OperateLogInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(int id)
        {
            int result = await _operateLogService.Delete(a => a.Id == id);

            return result > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _operateLogService.DeleteByIds(items);

            return result;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<OperateLogDto> Get(int id)
        {
            return await _operateLogService.QueryInfo<OperateLogDto>(a => a.Id == id);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<OperateLogDetailDto> Detail(int id)
        {
            var obj = await _operateLogService.QueryInfo(a => a.Id == id);

            if (obj != null)
            {
                return  _operateLogService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<OperateLogListDto>> List([FromQuery] OperateLogListQueryDto req)
        {
            var list = new List<OperateLogListDto>();
            var where = PredicateBuilder.New<OperateLogInfo>(true);
            var orderBy = new OrderBy<OperateLogInfo>(req.order, req.sort);
            var data = await _operateLogService.QueryList(where, orderBy);
            
            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _operateLogService.Join(item);
                list.Add(detail.Adapt<OperateLogListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<OperateLogListDto>> Query([FromQuery] OperateLogPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<OperateLogListDto>> QueryPost(OperateLogPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<OperateLogListDto>> PageQuery([FromQuery] OperateLogPageQueryDto req)
        {
            var list = new List<OperateLogListDto>();
            var where = PredicateBuilder.New<OperateLogInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            var totalCount = await _operateLogService.CountAsync(where);
            var orderBy = new OrderBy<OperateLogInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _operateLogService.QueryList(where, orderBy, paging);
            
            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _operateLogService.Join(item);
                list.Add(detail.Adapt<OperateLogListDto>());
            }
            #endregion

            return new PageQueryResult<OperateLogListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

    }
}