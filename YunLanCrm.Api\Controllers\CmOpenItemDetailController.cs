﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmOpenItemDetail;
using YunLanCrm.Common.Helper;
using YunLanCrm.Dto.CmProjects;
using YunLanCrm.Services;
using YunLanCrm.Model.Dto.CmOpenItemDetail;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmOpenItemDetailController : ControllerBase
    {
        private readonly ILogger<CmOpenItemDetailInfo> _logger;
        private readonly ICmOpenItemDetailService _cmOpenItemDetailService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmOpenItemDetailService"></param>
        /// <param name="logger"></param>
        public CmOpenItemDetailController(ICmOpenItemDetailService cmOpenItemDetailService, ILogger<CmOpenItemDetailInfo> logger)
        {
            _logger = logger;
            _cmOpenItemDetailService = cmOpenItemDetailService;
            
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CmOpenItemDetailAddOrUpdateDto req)
        {
            return await _cmOpenItemDetailService.AddCmOpenItemDetail(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmOpenItemDetailAddOrUpdateDto req)
        {
            return await _cmOpenItemDetailService.UpdateCmOpenItemDetail(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            var flag = false;

            try
            {
                int result = await _cmOpenItemDetailService.Delete(a => a.Id == id);
                flag = result > 0;
                if (flag)
                {
                    //点击列表右边的删除open item按钮时，提示用户，Are you sure you want to delete this open item and cancel the associated ticket ?，点击Yes，删除open item的同时把ticket 更新为Cancelled状态（记得要更新Closed date），点击No 取消操作

                    await _cmOpenItemDetailService.CancelTicket(id);
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }

            return flag;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _cmOpenItemDetailService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmOpenItemDetailDto> Get(long id)
        {
            return await _cmOpenItemDetailService.QueryInfo<CmOpenItemDetailDto>(a => a.Id == id);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmOpenItemDetailDto>> GetList([FromQuery] CmOpenItemDetailListQueryDto req)
        {
            return await _cmOpenItemDetailService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<CmOpenItemDetailDto> Detail(long id)
        {
            return await _cmOpenItemDetailService.Detail(id);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmOpenItemDetailDto>> DetailList([FromQuery] CmOpenItemDetailListQueryDto req)
        {
            return await _cmOpenItemDetailService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmOpenItemDetailDto>> Query([FromQuery] CmOpenItemDetailPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmOpenItemDetailDto>> QueryPost(CmOpenItemDetailPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmOpenItemDetailDto>> PageQuery([FromQuery] CmOpenItemDetailPageQueryDto req)
        {
            return await _cmOpenItemDetailService.PageQueryView(req);
        }
        private async Task<PageQueryResult<CmOpenItemExportDto>> ExportView([FromQuery] CmOpenItemDetailPageQueryDto req)
        {
            return await _cmOpenItemDetailService.ExportView(req);
        }

        [HttpPost("Export")]
        public async Task<IActionResult> Export(CmOpenItemDetailPageQueryDto req)
        {
            IActionResult result = null;
            try
            {
                var isAllCheck = req.ischeckType == 0 ? true : false;

                result = await ExcelHelper.Export<Task<PageQueryResult<CmOpenItemExportDto>>>(_cmOpenItemDetailService,
                    nameof(ICmOpenItemDetailService), nameof(ICmOpenItemDetailService.ExportView), this, isAllCheck, req);
            }
            catch (Exception ex)
            {
                SystemLogRecord.createLog("Export22222 error: " + ex.Message, "ExportExcel.txt", "TxtLog", true, true);
            }

            return result;
        }

    }
}