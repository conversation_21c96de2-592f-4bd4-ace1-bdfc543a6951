﻿using YunLanCrm.Common;
using YunLanCrm.Common.DB;
using YunLanCrm.Common.Helper;
using YunLanCrm.Common.LogHelper;
using Microsoft.Extensions.DependencyInjection;
using SqlSugar;
using StackExchange.Profiling;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Google.Protobuf.WellKnownTypes;
using YunLanCrm.Common.HttpContextUser;
using Serilog.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using Furion;
using Microsoft.AspNetCore.Http;

namespace YunLanCrm.Extensions
{
    /// <summary>
    /// SqlSugar 启动服务
    /// </summary>
    public static class SqlsugarSetup
    {
        private static ILogger _logger = new SerilogLoggerFactory().CreateLogger(typeof(SqlsugarSetup));

        public static void AddSqlsugarSetup(this IServiceCollection services)
        {
            if (services == null) throw new ArgumentNullException(nameof(services));

            // 默认添加主数据库连接
            MainDb.CurrentDbConnId = Appsettings.app(new string[] { "MainDB" });

            BaseDBConfig.MutiConnectionString.slaveDbs.ForEach(s =>
            {
                BaseDBConfig.AllSlaveConfigs.Add(new SlaveConnectionConfig()
                {
                    HitRate = s.HitRate,
                    ConnectionString = s.Connection
                });
            });

            BaseDBConfig.MutiConnectionString.allDbs.ForEach(m =>
            {
                var config = new ConnectionConfig()
                {
                    ConfigId = m.ConnId.ObjToString().ToLower(),
                    ConnectionString = m.Connection,
                    DbType = (DbType)m.DbType,
                    IsAutoCloseConnection = true,
                    // Check out more information: https://github.com/anjoy8/Blog.Core/issues/122
                    //IsShardSameThread = false,
                    MoreSettings = new ConnMoreSettings()
                    {
                        //IsWithNoLockQuery = true,
                        IsAutoRemoveDataCache = true,
                        SqlServerCodeFirstNvarchar = true,
                    },
                    // 从库
                    SlaveConnectionConfigs = BaseDBConfig.AllSlaveConfigs,
                    // 自定义特性
                    ConfigureExternalServices = new ConfigureExternalServices()
                    {
                        EntityService = (property, column) =>
                        {
                            if (column.IsPrimarykey && property.PropertyType == typeof(int))
                            {
                                column.IsIdentity = true;
                            }
                        }
                    },
                    InitKeyType = InitKeyType.Attribute
                };

                BaseDBConfig.ValidConfig.Add(config);

                BaseDBConfig.AllConfigs.Add(config);
            });

            services.AddSingleton<ISqlSugarClient>(o =>
            {
                var memoryCache = o.GetRequiredService<IMemoryCache>();

                foreach (var config in BaseDBConfig.AllConfigs)
                {
                    config.ConfigureExternalServices.DataInfoCacheService = new SqlSugarMemoryCacheService(memoryCache);
                }

                return new SqlSugarScope(BaseDBConfig.AllConfigs, db =>
                {
                    BaseDBConfig.ValidConfig.ForEach(config =>
                    {
                        var dbProvider = db.GetConnectionScope((string)config.ConfigId);

                        // 打印SQL语句
                        dbProvider.Aop

                        //SQL执行前
                        .OnLogExecuting = (sql, p) =>
                        {
                            try
                            {
                                if (Appsettings.app(new string[] { "AppSettings", "SqlAOP", "Enabled" }).ObjToBool())
                                {
                                    if (Appsettings.app(new string[] { "AppSettings", "SqlAOP", "OutToLogFile", "Enabled" }).ObjToBool())
                                    {
                                        IHttpContextAccessor httpContextAccessor = App.GetService<IHttpContextAccessor>();
                                        IUser CurrentUser = App.GetService<IUser>();

                                        //Parallel.For(0, 1, e =>
                                        //{
                                        SqlLogInfo obj = new SqlLogInfo()
                                        {
                                            Sql = sql,
                                            Params = GetParas(p)
                                        };

                                        //SerilogServer.WriteSql(obj);

                                        //在Swagger中展示 Sql 执行日志
                                        //MiniProfiler.Current.CustomTiming("SQL：", GetParas(p) + "【SQL执行前语句】=> " + Environment.NewLine + sql);

                                        string message = string.Empty;

                                        if (httpContextAccessor.HttpContext != null && !string.IsNullOrEmpty(CurrentUser.Name))
                                        {
                                            HttpRequest request = httpContextAccessor.HttpContext.Request;

                                            message += "【用户名】=> " + CurrentUser.Name + Environment.NewLine
                                                    + "【用户id】=> " + CurrentUser.UserId + Environment.NewLine
                                                    + "【访问的Url】=> " + Convert.ToString(request.Headers["AccessUrl"]) + Environment.NewLine
                                            ;
                                        }

                                        message += GetParas(p) + "【SQL执行前语句】=> " + Environment.NewLine + sql + Environment.NewLine;

                                        LogToFile(CurrentUser, httpContextAccessor, message, 0);

                                        //});
                                    }

                                    //if (Appsettings.app(new string[] { "AppSettings", "SqlAOP", "OutToConsole", "Enabled" }).ObjToBool())
                                    //{
                                    //    ConsoleHelper.WriteColorLine(string.Join("\r\n", new string[] { "--------", "【SQL语句】：" + GetWholeSql(p, sql) }), ConsoleColor.DarkCyan);
                                    //}
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogToFile(LogLevel.Critical, "Error occured OnLogExecuting:" + ex, nameof(SqlsugarSetup) + "/Error/Sql.log");
                            }
                        };

                        //SQL执行完
                        dbProvider.Aop.OnLogExecuted = (sql, pars) =>
                        {
                            try
                            {
                                IHttpContextAccessor httpContextAccessor = App.GetService<IHttpContextAccessor>();
                                IUser CurrentUser = App.GetService<IUser>();

                                //SerilogServer.WriteSqlLog("Executed", new string[] { GetParas(pars), "【SQL语句】：" + Environment.NewLine  + sql });

                                //执行时间
                                var executeTime = dbProvider.Ado.SqlExecutionTime.TotalMilliseconds;

                                //代码CS文件名
                                var fileName = dbProvider.Ado.SqlStackTrace.FirstFileName;

                                //代码行数
                                var fileLine = dbProvider.Ado.SqlStackTrace.FirstLine;

                                //方法名称
                                var FirstMethodName = dbProvider.Ado.SqlStackTrace.FirstMethodName;

                                //堆栈上得方法名称
                                //var PreviousFileName = dbProvider.Ado.SqlStackTrace.MyStackTraceList[0].FileName;

                                //_logger.LogToFile(LogLevel.Debug, GetParas(pars) + "【SQL执行完语句（参数化）】=> " + Environment.NewLine + sql, "Sql.log");

                                string message = string.Empty;

                                if (httpContextAccessor.HttpContext != null && !string.IsNullOrEmpty(CurrentUser.Name))
                                {
                                    HttpRequest request = httpContextAccessor.HttpContext.Request;

                                    message += "【用户名】=> " + CurrentUser.Name + Environment.NewLine
                                            + "【用户id】=> " + CurrentUser.UserId + Environment.NewLine
                                            + "【访问的Url】=> " + Convert.ToString(request.Headers["AccessUrl"]) + Environment.NewLine
                                    ;
                                }

                                message += "【SQL执行时间】=> " + executeTime + "毫秒" + Environment.NewLine
                                + "【SQL执行完语句（带有真实数据）】=> " + Environment.NewLine
                                + UtilMethods.GetSqlString(DbType.SqlServer, sql, pars) + Environment.NewLine + Environment.NewLine
                                ;

                                message += "【SQL执行完语句（参数化）】=> " + Environment.NewLine + sql + Environment.NewLine + Environment.NewLine
                                + GetParas(pars) + Environment.NewLine
                                ;

                                if (!string.IsNullOrEmpty(fileName))
                                {
                                    message += "【代码CS文件名】=> " + fileName + Environment.NewLine
                                            + "【代码行数】=> " + fileLine + Environment.NewLine
                                            + "【方法名称】=> " + FirstMethodName + Environment.NewLine
                                            ;
                                }

                                LogToFile(CurrentUser, httpContextAccessor, message, dbProvider.Ado.SqlExecutionTime.TotalSeconds);

                                //#if DEBUG                               
                                //#endif
                            }
                            catch (Exception ex)
                            {
                                _logger.LogToFile(LogLevel.Critical, "Error occured OnLogExecuted:" + ex, nameof(SqlsugarSetup) + "/Error/Sql.log");
                            }
                        };

                        //SQL报错
                        dbProvider.Aop.OnError = (exp) =>
                        {
                            try
                            {
                                IHttpContextAccessor httpContextAccessor = App.GetService<IHttpContextAccessor>();
                                IUser CurrentUser = App.GetService<IUser>();

                                //SerilogServer.WriteErrorLog(exp.Message, exp);

                                //代码CS文件名
                                var fileName = dbProvider.Ado.SqlStackTrace.FirstFileName;

                                //代码行数
                                var fileLine = dbProvider.Ado.SqlStackTrace.FirstLine;

                                //方法名称
                                var FirstMethodName = dbProvider.Ado.SqlStackTrace.FirstMethodName;

                                //堆栈上得方法名称
                                //var PreviousFileName = dbProvider.Ado.SqlStackTrace.MyStackTraceList[0].FileName;

                                string message = string.Empty;

                                if (httpContextAccessor.HttpContext != null && !string.IsNullOrEmpty(CurrentUser.Name))
                                {
                                    HttpRequest request = httpContextAccessor.HttpContext.Request;

                                    message += "【用户名】=> " + CurrentUser.Name + Environment.NewLine
                                            + "【用户id】=> " + CurrentUser.UserId + Environment.NewLine
                                            + "【访问的Url】=> " + Convert.ToString(request.Headers["AccessUrl"]) + Environment.NewLine
                                    ;
                                }

                                message
                                   += "【SQL执行完语句（带有真实数据）】=> " + Environment.NewLine
                                   + UtilMethods.GetSqlString(DbType.SqlServer, exp.Sql, (SugarParameter[])exp.Parametres) + Environment.NewLine + Environment.NewLine
                                   + "【SQL执行报错信息】=> " + Environment.NewLine + exp.Message + Environment.NewLine
                                   + "【StackTrace信息】=> " + Environment.NewLine + exp.StackTrace + Environment.NewLine
                                   ;

                                if (!string.IsNullOrEmpty(fileName))
                                {
                                    message += "【代码CS文件名】=> " + fileName + Environment.NewLine
                                            + "【代码行数】=> " + fileLine + Environment.NewLine
                                            + "【方法名称】=> " + FirstMethodName + Environment.NewLine;
                                }

                                LogToFile(CurrentUser, httpContextAccessor, message, 0);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogToFile(LogLevel.Critical, "Error occured OnError:" + ex, nameof(SqlsugarSetup) + "/Error/Sql.log");
                            }
                        };

                        //SQL执行前 可以修改SQL
                        dbProvider.Aop.OnExecutingChangeSql = (sql, pars) =>
                        {
                            try
                            {
                                //判断update或delete方法是否有where条件。如果真的想删除所有数据，请where(p=>true)或where(p=>p.id>0)
                                if (sql.TrimStart().IndexOf("delete ", StringComparison.CurrentCultureIgnoreCase) == 0)
                                {
                                    if (sql.IndexOf("where", StringComparison.CurrentCultureIgnoreCase) <= 0)
                                    {
                                        throw new Exception("delete删除方法需要有where条件！！");
                                    }
                                }
                                else if (sql.TrimStart().IndexOf("update", StringComparison.CurrentCultureIgnoreCase) == 0)
                                {
                                    if (sql.IndexOf("where", StringComparison.CurrentCultureIgnoreCase) <= 0 && sql.IndexOf("on", StringComparison.CurrentCultureIgnoreCase) <= 0)
                                    {
                                        throw new Exception("update更新方法需要有where条件！！");
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogToFile(LogLevel.Critical, "Error occured OnExecutingChangeSql:" + ex, nameof(SqlsugarSetup) + "/Error/Sql.log");
                            }

                            return new KeyValuePair<string, SugarParameter[]>(sql, pars);
                        };

                    });
                });
            });
        }

        private static void LogToFile(IUser currentUser, IHttpContextAccessor httpContextAccessor, string message, double totalSeconds)
        {
            if (totalSeconds > 3)
            {
                _logger.LogToFile(LogLevel.Debug, message, nameof(SqlsugarSetup) + "/LongTimeSql/Sql.log");
            }
            else if (currentUser != null && currentUser.UserId > 0)
            {
                if (httpContextAccessor.HttpContext.Request.Path.Value.IndexOf("refreshtoken",
                        StringComparison.OrdinalIgnoreCase) > -1)
                {
                    //_logger.LogToFile(LogLevel.Debug, message, nameof(SqlsugarSetup) + "/RefreshToken/Sql.log");
                }
                else
                {
                    _logger.LogToFile(LogLevel.Debug, message, nameof(SqlsugarSetup) + "/Included_UserId/Sql.log");
                }
            }
            else
            {
                _logger.LogToFile(LogLevel.Debug, message, nameof(SqlsugarSetup) + "/Excluded_UserId/Sql.log");
            }
        }

        private static string GetWholeSql(SugarParameter[] paramArr, string sql)
        {
            foreach (var param in paramArr)
            {
                sql.Replace(param.ParameterName, param.Value.ObjToString());
            }

            return sql;
        }

        private static string GetParas(SugarParameter[] pars)
        {
            string key = "【SQL参数】=> " + Environment.NewLine;

            string paramsStr = string.Empty;

            foreach (var param in pars)
            {
                paramsStr += $"{param.ParameterName} = {param.Value}" + Environment.NewLine;
            }

            return string.IsNullOrEmpty(paramsStr) ? string.Empty : key + paramsStr + Environment.NewLine;
        }
    }

    public class SqlLogInfo
    {
        public string Params { get; set; }

        public string Sql { get; set; }
    }
}