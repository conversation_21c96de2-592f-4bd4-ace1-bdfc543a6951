﻿using System;
using System.Security.Cryptography;
using System.Text;

namespace YunLanCrm.Common.Helper
{
    public static class AesEncryptionHelper
    {
        //private const string AES_KEY = "PLmRonGl7CtIudqmr/GUUky+EB6NB+D0NwoLLJWv0fg=";
        //private const string AES_IV = "UVAaaCOBgpkpndeH52kuvQ==";

        /// <summary>
        /// 加密
        /// </summary>
        /// <param name="plainText"></param>
        /// <returns></returns>
        public static string Encrypt(string plainText, string AES_KEY, string AES_IV)
        {
            using (Aes aes = Aes.Create())
            {
                aes.Key = Convert.FromBase64String(AES_KEY);
                aes.IV = Convert.FromBase64String(AES_IV);

                ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);
                using (var ms = new System.IO.MemoryStream())
                {
                    using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                    {
                        byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);
                        cs.Write(plainBytes, 0, plainBytes.Length);
                        cs.FlushFinalBlock();
                        return Convert.ToBase64String(ms.ToArray());
                    }
                }
            }
        }

        /// <summary>
        /// 解密
        /// </summary>
        /// <param name="cipherText"></param>
        /// <returns></returns>
        public static string Decrypt(string cipherText, string AES_KEY, string AES_IV)
        {


            using (Aes aes = Aes.Create())
            {
                aes.Key = Convert.FromBase64String(AES_KEY);
                aes.IV = Convert.FromBase64String(AES_IV);
                ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);
                using (var ms = new System.IO.MemoryStream(Convert.FromBase64String(cipherText)))
                {
                    using (var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                    {
                        using (var sr = new System.IO.StreamReader(cs))
                        {
                            return sr.ReadToEnd();
                        }
                    }
                }
            }
        }
    }

    public class AesEncryptionInfo
    {
        public string BackendKey { get; set; }
        public string BackendIV { get; set; }

        public string FrontendKey { get; set; }
        public string FrontendIV { get; set; }
    }
}

