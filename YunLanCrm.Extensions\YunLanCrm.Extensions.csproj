﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="EventHandling\**" />
    <EmbeddedResource Remove="EventHandling\**" />
    <None Remove="EventHandling\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Properties\crm_private_key.pem" />
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="Furion" Version="3.1.3" />
    <PackageReference Include="AspNetCoreRateLimit" Version="3.0.5" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="6.0.0" />
    <PackageReference Include="Autofac.Extras.DynamicProxy" Version="5.0.0" />
    <PackageReference Include="Com.Ctrip.Framework.Apollo" Version="2.4.4" />
    <PackageReference Include="Com.Ctrip.Framework.Apollo.Configuration" Version="*******" />
    <PackageReference Include="Consul" Version="*******" />
    <PackageReference Include="Elsa" Version="2.8.2" />
    <PackageReference Include="Elsa.Abstractions" Version="2.8.2" />
    <PackageReference Include="Elsa.Activities.Email" Version="2.8.2" />
    <PackageReference Include="Elsa.Activities.Http" Version="2.8.2" />
    <PackageReference Include="Elsa.Activities.Temporal.Quartz" Version="2.8.2" />
    <PackageReference Include="Elsa.Activities.UserTask" Version="2.8.2" />
	<PackageReference Include="Elsa.Designer.Components.Web" Version="2.8.2" />
    <PackageReference Include="Elsa.Core" Version="2.8.2" />
    <PackageReference Include="Elsa.Server.Api" Version="2.8.2" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="5.0.9" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson" Version="5.0.0-preview.2.20167.3" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="5.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Log4Net.AspNetCore" Version="3.1.0" />
	<PackageReference Include="MiniProfiler.AspNetCore.Mvc" Version="4.2.22" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.3.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="7.0.2" />

    <PackageReference Include="nacos-sdk-csharp-unofficial" Version="0.8.5" />
    <PackageReference Include="nacos-sdk-csharp-unofficial.AspNetCore" Version="0.8.5" />
    <PackageReference Include="nacos-sdk-csharp-unofficial.Extensions.Configuration" Version="0.8.5" />
    <PackageReference Include="nacos-sdk-csharp-unofficial.IniParser" Version="0.8.5" />
    <PackageReference Include="nacos-sdk-csharp.AspNetCore" Version="1.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\YunLanCrm.EventBus\YunLanCrm.EventBus.csproj" />
    <ProjectReference Include="..\YunLanCrm.Services\YunLanCrm.Services.csproj" />
    <ProjectReference Include="..\YunLanCrm.Tasks\YunLanCrm.Tasks.csproj" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Properties\crm_private_key.pem" />
  </ItemGroup>

</Project>
