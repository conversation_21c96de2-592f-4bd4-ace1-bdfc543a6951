﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.UserDelegateAudit;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class UserDelegateAuditController : ControllerBase
    {
        private readonly ILogger<UserDelegateAuditInfo> _logger;
        private readonly IUserDelegateAuditService _userDelegateAuditService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="userDelegateAuditService"></param>
        /// <param name="logger"></param>
        public UserDelegateAuditController(IUserDelegateAuditService userDelegateAuditService, ILogger<UserDelegateAuditInfo> logger)
        {
            _logger = logger;
            _userDelegateAuditService = userDelegateAuditService;
            
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(UserDelegateAuditAddOrUpdateDto req)
        {
            return await _userDelegateAuditService.AddUserDelegateAudit(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(UserDelegateAuditAddOrUpdateDto req)
        {
            return await _userDelegateAuditService.UpdateUserDelegateAudit(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="auditId">auditId</param>
        /// <returns></returns>
        [HttpPost("Delete/{auditId}")]
        public async Task<bool> Delete(long auditId)
        {
            int result = await _userDelegateAuditService.Delete(a => a.AuditId == auditId);

            return result > 0;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _userDelegateAuditService.Delete(a => items.Contains(a.AuditId));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="auditId"></param>
        /// <returns></returns>
        [HttpGet("Get/{auditId}")]
        public async Task<UserDelegateAuditDto> Get(long auditId)
        {
            return await _userDelegateAuditService.QueryInfo<UserDelegateAuditDto>(a => a.AuditId == auditId);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<UserDelegateAuditDto>> GetList([FromQuery] UserDelegateAuditListQueryDto req)
        {
            return await _userDelegateAuditService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="auditId"></param>
        /// <returns></returns>
        [HttpGet("Detail/{auditId}")]
        public async Task<UserDelegateAuditDetailDto> Detail(long auditId)
        {
            return await _userDelegateAuditService.Detail(auditId);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<UserDelegateAuditDetailDto>> DetailList([FromQuery] UserDelegateAuditListQueryDto req)
        {
            return await _userDelegateAuditService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<UserDelegateAuditDetailDto>> Query([FromQuery] UserDelegateAuditPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<UserDelegateAuditDetailDto>> QueryPost(UserDelegateAuditPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<UserDelegateAuditDetailDto>> PageQuery([FromQuery] UserDelegateAuditPageQueryDto req)
        {
            return await _userDelegateAuditService.PageQueryView(req);
        }

    }
}