﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="LogHelper\ILoggerHelper.cs" />
    <Compile Remove="LogHelper\LogHelper.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DotNetCore.NPOI" Version="1.2.3" />
    <PackageReference Include="InitQ" Version="1.0.0.7" />
    <PackageReference Include="log4net" Version="2.0.13" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.1.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="5.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="5.0.0" />
    <PackageReference Include="ncalc" Version="1.3.8" />
    <PackageReference Include="Npoi.Mapper" Version="4.1.0" />
    <PackageReference Include="PinYinConverterCore" Version="1.0.2" />
    <PackageReference Include="RestSharp" Version="106.11.8-alpha.0.13" />
    <PackageReference Include="RSAExtensions" Version="1.0.3" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.0.1" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="8.4.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Serilog.Sinks.MSSqlServer" Version="5.7.0" />
    <PackageReference Include="Serilog.Sinks.MSSqlServerCore" Version="1.1.1" />
    <PackageReference Include="StackExchange.Redis" Version="2.1.58" />
    <PackageReference Include="System.Drawing.Common" Version="6.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.8.0" />

    <PackageReference Include="Serilog" Version="2.11.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="3.4.0" />
    <PackageReference Include="Serilog.Sinks.RollingFile" Version="3.3.1-dev-00771" />
    <PackageReference Include="System.IO.FileSystem" Version="4.3.0" />
    <PackageReference Include="System.Net.Primitives" Version="4.3.0" />
    <PackageReference Include="WebApiClient.Extensions.DependencyInjection" Version="2.0.3" />
    <PackageReference Include="WebApiClient.JIT" Version="1.1.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\YunLanCrm.Model\YunLanCrm.Model.csproj" />
    <ProjectReference Include="..\YunLanCrm.Serilog.Es\YunLanCrm.Serilog.Es.csproj" />
    <ProjectReference Include="..\Ocelot.Provider.Nacos\Ocelot.Provider.Nacos.csproj" />
  </ItemGroup>

</Project>
