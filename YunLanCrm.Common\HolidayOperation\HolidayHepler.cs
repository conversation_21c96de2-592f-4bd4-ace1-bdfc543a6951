﻿using Hczb.Tools.HolidayOperation;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YunLanCrm.Common.Helper;
using YunLanCrm.Model.Models;

namespace YunLanCrm.Common.HolidayOperation
{
    public static class HolidayHepler
    {

        /// <summary>
        /// 除去节假日，统计工作日天数
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="dtHoliday"></param>
        /// <returns></returns>
        public static int RemoveHolidayDay(string startDate, string endDate, List<HolidayInfo> listHoliday)
        {
            bool startTimeIsHolidayDate = false;

            DateTime start = Convert.ToDateTime(startDate);
            DateTime end = Convert.ToDateTime(endDate);

            string holidayYear = DateTime.Now.Year.ToString();

            int lastDay = GetWorkDayCount(start, end);

            if (listHoliday.Any(a => a.HolidayYear.ToString() == holidayYear))
            {
                startTimeIsHolidayDate = true;
                for (int i = 0; i < listHoliday.Count; i++) {
                    var holidayDate =listHoliday[i].HolidayDate;

                    if (holidayDate > start && holidayDate <= end)
                    {
                        lastDay--;
                    }
                }
            }
            else
            {
                var holidays = DateSystemHelper.GetPublicHolidays(Convert.ToInt32(holidayYear), CountryCode.US);

                var selectRow = holidays.Where(r => r.Date == start);

                if (selectRow.Any())
                {
                    startTimeIsHolidayDate = true;
                }

                foreach (PublicHoliday holiday in holidays)
                {
                    var holidayDate = holiday.Date;

                    if (holidayDate > start && holidayDate <= end)
                    {
                        lastDay--;
                    }
                }
            }
            if (!IsWeekend(start) && !startTimeIsHolidayDate)
            {
                lastDay--;
            }

            return lastDay;
        }
        //判断是否为周末
        public static bool IsWeekend(DateTime date)
        {
            bool isWeekend = false;
            var day = date.DayOfWeek;

            if (day == DayOfWeek.Sunday || day == DayOfWeek.Saturday)
            {
                isWeekend = true;
            }

            return isWeekend;
        }

        /// <summary>
        /// Get business day which exclude weekend
        /// </summary>
        /// <param name="beginDay"></param>
        /// <param name="endDay"></param>
        /// <returns></returns>
        public static int GetWorkDayCount(DateTime beginDay, DateTime endDay)
        {
            //set creat day and end day as business day.
            if (beginDay.DayOfWeek == DayOfWeek.Saturday)
            {
                beginDay = beginDay.AddDays(2);
            }

            if (beginDay.DayOfWeek == DayOfWeek.Sunday)
            {
                beginDay = beginDay.AddDays(1);
            }

            if (endDay.DayOfWeek == DayOfWeek.Saturday)
            {
                endDay = endDay.AddDays(-1);
            }

            if (endDay.DayOfWeek == DayOfWeek.Sunday)
            {
                endDay = endDay.AddDays(-2);
            }

            //get days between create day and end day
            var allDays = Convert.ToInt32(ExecDateDiff(beginDay, endDay, 1)) + 1;

            //get weekend days between create day and end day
            var weekendDays = Convert.ToInt32(ExecDateDiff(GetFirstDayOfWeek(beginDay), GetFirstDayOfWeek(endDay), 1)) / 7 * 2;

            //get work day
            var workDay = allDays - weekendDays;

            return workDay;
        }

        /// <summary>
        /// Calculate the time span
        /// </summary>
        /// <param name="dateBegin"></param>
        /// <param name="dateEnd"></param>
        /// <returns></returns>
        public static string ExecDateDiff(DateTime dateBegin, DateTime dateEnd, int timeType)
        {
            TimeSpan ts1 = new TimeSpan(dateBegin.Ticks);
            TimeSpan ts2 = new TimeSpan(dateEnd.Ticks);
            TimeSpan ts3 = ts1.Subtract(ts2).Duration();
            string result = string.Empty;
            switch (timeType)
            {
                case 1:
                    result = ts3.TotalDays.ToString();
                    break;
                case 2:
                    result = ts3.TotalHours.ToString();
                    break;
                case 3:
                    result = ts3.TotalMinutes.ToString();
                    break;
                case 4:
                    result = ts3.TotalSeconds.ToString();
                    break;
            }
            return result;
        }

        /// <summary>
        /// Get first day of the week
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        public static DateTime GetFirstDayOfWeek(DateTime time)
        {
            int i = time.DayOfWeek - DayOfWeek.Monday;
            if (i == -1) i = 6;
            TimeSpan ts = new TimeSpan(i, 0, 0, 0);
            return time.Subtract(ts);
        }


        public static bool IsHoliday(DateTime date,bool hasHoliday)
        {
            bool bHoliday = false;
            //var date = new DateTime(2021, 1, 20);

            var day = date.DayOfWeek;

            //是否公假日
            try
            {
                var isPublicHoliday = DateSystemHelper.IsPublicHoliday(date, CountryCode.US);

                //获取自定义的假期
                var isCustomHoliday = false;
                if (hasHoliday)
                {
                    isCustomHoliday = true;
                }

                //判断是否为周末
                if (day == DayOfWeek.Sunday || day == DayOfWeek.Saturday)
                {
                    bHoliday = true;
                }
                else if (isCustomHoliday)
                {
                    bHoliday = true;
                }
                else if (isPublicHoliday)
                {
                    bHoliday = true;
                }
            }
            catch (Exception ex)
            {

                throw;
            }

            return bHoliday;
        }
    }
}
