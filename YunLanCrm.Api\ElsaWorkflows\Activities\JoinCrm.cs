﻿using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using Elsa.Services.WorkflowStorage;
using MediatR;
using YunLanCrm.Dto.WorkflowExecutionLogRecords;
using YunLanCrm.IRepositories;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Models;

namespace YunLanCrm.Api.ElsaWorkflows.Activities
{
    [Action(Category = "基础节点", DisplayName = "合并", Description = "合并（Join）")]
    public class JoinCrm : Elsa.Activities.ControlFlow.Join
    {
        IWorkflowStorageService _workflowStorageService;
        IWorkflowCrmService _workflowCrmService;
        IWorkflowCrmRepository _workflowCrmRepository;
        public JoinCrm(IMediator mediator, IWorkflowStorageService workflowStorageService, IWorkflowCrmService workflowCrmService, IWorkflowCrmRepository workflowCrmRepository) : base(mediator)
        {
            _workflowStorageService = workflowStorageService;
            _workflowCrmService = workflowCrmService;
            _workflowCrmRepository = workflowCrmRepository;
        }

        [ActivityInput(Hint = "Config", SupportedSyntaxes = new[] { SyntaxNames.Literal, SyntaxNames.JavaScript, SyntaxNames.Liquid })]
        public string Config { get; set; } = default;

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            if (context.Input is WorkflowExecutionDto)
            {
                var input = context.GetInput<WorkflowExecutionDto>();
                if (Mode == JoinMode.WaitAll)
                {

                }

                if (Mode == JoinMode.WaitAny)
                {
                    var list = await _workflowCrmService.PreviousApproval(context.WorkflowInstance.DefinitionId, context.ActivityId);
                    var acIds = list.Select(a => a.ActivityId).ToList();

                    var tasks = await _workflowCrmRepository.Db.Queryable<WorkflowTaskInfo>()
                        .Where(a => a.WorkflowInstanceId == context.WorkflowInstance.Id && a.TaskStatus == Consts.WorkflowTaskStatus_Pending && acIds.Contains(a.ActivityId)).ToListAsync();
                    foreach (var item in tasks)
                    {
                        await _workflowCrmRepository.Db.Updateable<WorkflowTaskInfo>()
                                                      .SetColumns(a => a.TaskStatus, Consts.WorkflowTaskStatus_Cancelled)
                                                      .SetColumns(a => a.UpdatedById, input.UserId)
                                                      .SetColumns(a => a.UpdatedDate, DateTime.Now)
                                                      .SetColumns(a => a.Msg, $"其中一个分支完成即可")
                                                      .Where(a => a.TaskId == item.TaskId && a.TaskStatus == Consts.WorkflowTaskStatus_Pending)
                                                      .ExecuteCommandAsync();

                        await _workflowCrmRepository.Db.Updateable<WorkflowTaskUserInfo>()
                           .SetColumns(a => a.Status == Consts.WorkflowTaskStatus_Cancelled)
                           .SetColumns(a => a.OperationMsg, $"其中一个分支完成即可")
                           .Where(a => a.TaskId == item.TaskId && a.Status == Consts.WorkflowTaskStatus_Pending)
                           .ExecuteCommandAsync();

                        //阻断的活动要清除
                        context.WorkflowInstance.BlockingActivities.RemoveWhere(a => a.ActivityId == item.ActivityId);
                    }
                }

                return Done(input);
            }

            return await base.OnExecuteAsync(context);
        }
    }
}
