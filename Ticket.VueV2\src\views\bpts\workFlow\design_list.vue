﻿<template>
	<el-container class="workflowApproval-index-container tablelist" style="height: 100%">
		<el-header class="list-search-header mb2" style="height: 40px">
			<div class="list-search-container">
				<el-form :inline="true" label-width="80px">
					<el-row :gutter="35">
						<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="8" class="mb6">
							<el-form-item :label="$t('message.workflow_labelArea.keywords')">
								<el-input v-model="tableData.param.searchKey"  clearable class="w-20">
								</el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="8" class="mb6">
							<el-form-item :label="$t('message.workflow_labelArea.type')">
								<el-select v-model="tableData.param.categoryId" :placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w-20">
									<el-option v-for="item in workflowTypes" :label="item.itemName" :value="item.itemValue" :key="item.itemId"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="8">
							<el-form-item style="vertical-align: top">
								<el-button type="primary" class="ml10 mt3" @click="onSearch" size="small">
									<el-icon>
										<ele-Search />
									</el-icon>
									{{ $t('message.workflow_btnArea.search') }}
								</el-button>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</div>
		</el-header>
		<el-header class="table_header" style="display: flex">
			<div class="left-panel">
				<el-button v-auth="'workflowDesign.Create'" size="small" type="success" class="ml10" @click="onAdd">
					<el-icon>
						<ele-Plus />
					</el-icon>
					{{ $t('message.workflow_btnArea.create') }}
				</el-button>

				<el-button v-if="false" type="danger" size="small" icon="ele-Delete" :disabled="tableData.selection.length == 0" @click="onDeleteByList"
					>删除</el-button
				>
			</div>
			<div class="right-panel" v-if="false">
				<el-button size="small" class="ml10" @click="onSearch">
					<el-icon>
						<ele-Refresh />
					</el-icon>
					刷新
				</el-button>
				<el-button size="small" class="ml10" @click="onSearch">
					<el-icon>
						<ele-ArrowDownBold />
					</el-icon>
					{{ $t('message.lang.buttonExport') }}
				</el-button>
				<el-button size="small" class="ml10" @click="onPrint">
					<el-icon>
						<ele-Printer />
					</el-icon>
					打印
				</el-button>
			</div>
		</el-header>
		<el-main class="nopadding" ref="printMain">
			<div class="scTable" style="height: 100%" ref="scTableMain">
				<div class="scTable-table">
					<el-table
						:data="tableData.data"
						v-loading="tableData.loading"
						height="calc(100%)"
						@selection-change="selectionChange"
						:row-style="{ height: '40px' }"
						:cell-style="{ padding: '0px' }"
						stripe border
					>
						<template #empty>
							<el-empty :description="$t('message.page.emptyDescription')" :image-size="100"></el-empty>
						</template>

						<el-table-column type="selection" />

						<el-table-column :label="$t('message.workflow_fieldArea.workflowName')" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.workflowName }}</span>
							</template>
						</el-table-column>
						<el-table-column :label="$t('message.workflow_fieldArea.workflowCode')" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.workflowCode }}</span>
							</template>
						</el-table-column>
						<el-table-column :label="$t('message.workflow_fieldArea.type')" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.categoryName }}</span>
							</template>
						</el-table-column>
						<el-table-column :label="$t('message.workflow_fieldArea.createdByName')" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.createdByName }}</span>
							</template>
						</el-table-column>
						<el-table-column :label="$t('message.workflow_fieldArea.createdAt')" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.createdAt }}</span>
							</template>
						</el-table-column>
						<el-table-column :label="$t('message.workflow_fieldArea.status')" show-overflow-tooltip>
							<template #default="{ row }">
								<el-tag v-if="row.status == 0" type="success">{{ $t('message.workflow_labelArea.enable') }}</el-tag>
								<el-tag type="danger" v-else-if="row.status == 1">{{ $t('message.workflow_labelArea.disable') }}</el-tag>
							</template>
						</el-table-column>

						<el-table-column fixed="right" align="left" :label="$t('message.workflow_fieldArea.operation')">
							<template #default="{ row }">
								<el-button v-auth="'workflowDesign.Edit'" size="mini" type="text" @click="onEdit(row)">
									{{ $t('message.workflow_btnArea.edit') }}
								</el-button>
								<el-button v-auth="'workflowDesign.Delete'" size="mini" type="text" style="color: red" @click="onDelete(row)">
									{{ $t('message.workflow_btnArea.delete') }}
								</el-button>

								<el-dropdown v-if="false" size="small" style="vertical-align: middle; margin-left: 12px; margin-top: -3px; color: #1890ff">
									<span class="el-button--text">
										{{ $t('message.workflow_btnArea.more') }}
										<el-icon>
											<ele-ArrowDown />
										</el-icon>
									</span>
									<template #dropdown>
										<el-dropdown-menu>
											<el-dropdown-item>{{ $t('message.workflow_btnArea.disable') }}</el-dropdown-item>
										</el-dropdown-menu>
									</template>
								</el-dropdown>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<div class="scTable-page">
					<el-pagination
						@size-change="onSizechange"
						@current-change="onCurrentChange"
						:pager-count="5"
						:page-sizes="[10, 20, 30]"
						v-model:current-page="tableData.param.pageIndex"
						background
						v-model:page-size="tableData.param.pageSize"
						layout="total, sizes, prev, pager, next, jumper"
						:total="tableData.total"
						small
					>
					</el-pagination>
				</div>
			</div>

			<Design ref="designRef" @fetchData="onInit"></Design>
		</el-main>
	</el-container>
</template>

<script lang="ts">
import { useRouter, useRoute } from 'vue-router';
// import { useStore } from '/@/store/index';
import { toRefs, reactive, onMounted, ref, defineComponent, computed } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate } from '/@/utils/formatTime';
import print from '/@/utils/print.js';

import dictItemApi from '/@/api/dictItem/index';
import workflowDesignApi from '/@/api/workflowDesign/index';
import Detail from './components/detail.vue';
import Design from './components/design.vue';
import { useI18n } from 'vue-i18n';

export default defineComponent({
	name: 'workflowApprovalIndex',
	components: { Detail, Design },
	setup() {
		const { t } = useI18n();
		const router = useRouter();
		const route = useRoute();
		// const store = useStore();
		const designRef = ref();
		const printMain = ref(null);
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					categoryId: '',
					order: 'CreatedAt',
					sort: 'desc', // asc or desc
				},
			},
			tableRow: {},

			infoDrawer: false,
			detailObj: {},
			workflowTypes: [],
		});

		// 获取用户信息 vuex
		// const currentUser = computed(() => {
		// 	return store.state.userInfos.userInfos;
		// });

		//初始化
		const onInit = () => {
			state.tableData.loading = true;

			workflowDesignApi
				.Query(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
				})
				.catch((rs) => {})
				.finally(() => (state.tableData.loading = false));
		};
		//搜索
		const onSearch = () => {
			onInit();
		};

		// 添加
		const onAdd = () => {
			designRef.value.openDialog({ action: 'Create' });
			//router.push('/purchase/purchaseOrder');
		};
		// 修改
		const onEdit = (row: Object) => {
			//createOrEditRef.value.openDialog({ action: 'Edit', id: row.id });
			//router.push('/workflowApproval/edit/' + row.id);
			//router.push('/workflow/detail/' + row.crmWorkflowId);
			designRef.value.openDialog({ action: 'Edit', designId: row.designId, row });
		};
		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(`此操作将永久当前纪录，是否继续?`, '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(() => {
				workflowDesignApi
					.DeleteByKey(row.designId)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((rs) => {});
			});
		};
		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection;
		};
		//打印
		const onPrint = () => {
			print(printMain.value);
		};
		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};
		// 页面加载时
		onMounted(() => {
			var dictArr = ['WorkflowType'];
			dictItemApi.Many(dictArr).then((rs) => {
				state.workflowTypes = rs.data?.find((a) => {
					return a.dictValue === 'WorkflowType';
				})?.items;
			});

			onInit();
		});
		return {
			printMain,
			formatStrDate,
			onPrint,
			designRef,
			selectionChange,
			onInit,
			onAdd,
			onEdit,
			onDelete,
			onSizechange,
			onCurrentChange,
			onSearch,
			...toRefs(state),
		};
	},
});
</script>
