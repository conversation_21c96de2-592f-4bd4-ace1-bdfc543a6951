﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.DictItem;
using Furion.Logging.Extensions;
using YunLanCrm.Common.HttpContextUser;
using SqlSugar;
using YunLanCrm.Services;
using YunLanCrm.Common.Helper;
using YunLanCrm.Dto.CmCategory;
using YunLanCrm.Dto.Organization;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class DictItemController : ControllerBase
    {
        private readonly ILogger<DictItemInfo> _logger;

        private readonly IDictItemService _dictItemService;
        private readonly IDictService _dictService;

        private readonly IUser CurrentUser;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="dictItemService"></param>
        /// <param name="logger"></param>
        public DictItemController(IDictItemService dictItemService, IUser user, ILogger<DictItemInfo> logger, IDictService dictService)
        {
            this._dictItemService = dictItemService;
            this._logger = logger;
            CurrentUser = user;
            _dictService = dictService;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(DictItemAddDto req)
        {
            var addIdentity = await _dictItemService.AddDictItem(req);

            return addIdentity;
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(DictItemEditDto req)
        {
            var obj = await _dictItemService.QueryInfo(a => a.DictId == req.DictId && a.Value == req.Value && a.ItemId != req.ItemId);

            if (obj != null)
            {
                throw new Exception("This dictionary entry already exists.");
            }

            return await _dictItemService.Update(req.Adapt<DictItemInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="itemId">itemId</param>
        /// <returns></returns>
        [HttpPost("Delete/{itemId}")]
        public async Task<bool> Delete(long itemId)
        {
            int result = await _dictItemService.Delete(a => a.ItemId == itemId);

            return result > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        [NonAction]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _dictItemService.DeleteByIds(items);

            return result;
        }

        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="itemId"></param>
        /// <returns></returns>
        [HttpGet("Get/{itemId}")]
        public async Task<DictItemDto> Get(long itemId)
        {

            return await _dictItemService.QueryInfo<DictItemDto>(a => a.ItemId == itemId);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="itemId"></param>
        /// <returns></returns>
        [HttpGet("Detail/{itemId}")]
        public async Task<DictItemDetailDto> Detail(long itemId)
        {
            var obj = await _dictItemService.QueryInfo(a => a.ItemId == itemId);

            if (obj != null)
            {
                return _dictItemService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取多类型的字典列表
        /// </summary>
        /// <param name="types"></param>
        /// <returns></returns>
        [HttpPost("Many")]
        public async Task<List<DictItemManyDto>> Many(List<string> types)
        {
            return await _dictItemService.GetMany(types);
        }

        [HttpGet("Get")]
        public async Task<List<DictItemListDto>> List([FromQuery] DictItemListQueryDto req)
        {
            var list = new List<DictItemListDto>();
            var where = PredicateBuilder.New<DictItemInfo>(true);
            if (req.dictId.HasValue && req.dictId.Value > 0)
            {
                where.And(a => a.DictId == req.dictId.Value);
            }
            var orderBy = new OrderBy<DictItemInfo>(req.order, req.sort);
            var data = await _dictItemService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _dictItemService.Join(item);
                list.Add(detail.Adapt<DictItemListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        [NonAction]
        public async Task<PageQueryResult<DictItemListDto>> Query([FromQuery] DictItemPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<DictItemListDto>> QueryPost(DictItemPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<DictItemListDto>> PageQuery([FromQuery] DictItemPageQueryDto req)
        {
            var list = new List<DictItemListDto>();
            var where = PredicateBuilder.New<DictItemInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                where.And(a => a.Name.Contains(req.searchKey));
            }

            if (req.dictTypeId.HasValue && req.dictTypeId.Value > 0)
            {
                where.And(a => a.DictId == req.dictTypeId.Value);
            }

            if (!string.IsNullOrWhiteSpace(req.dictName))
            {
                where.And(a => a.Value.Contains(req.dictName));
            }
            if (!string.IsNullOrWhiteSpace(req.dictDescr))
            {
                where.And(a => a.Description.Contains(req.dictDescr));
            }
            //用于导出
            if (req.ids != null && req.ids.Count > 0)
            {
                where.And(a => req.ids.Contains(a.ItemId.ToString()));
            }

            var totalCount = await _dictItemService.CountAsync(where);
            var orderBy = new OrderBy<DictItemInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _dictItemService.QueryList(where, orderBy, paging);

            var dictTypeName = string.Empty;
            if (req.dictTypeId > 0)
            {
                var dictType = await _dictService.QueryById(req.dictTypeId);
                dictTypeName = dictType.Name;
            }


            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _dictItemService.Join(item);
                var obj = detail.Adapt<DictItemListDto>();
                obj.DictTypeName = dictTypeName;
                list.Add(obj);
            }
            #endregion

            return new PageQueryResult<DictItemListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

        [HttpPost("Export")]
        public async Task<IActionResult> Export(DictItemPageQueryDto req)
        {
            var isAllCheck = req.ischeckType == 0 ? true : false;
            return await ExcelHelper.Export<Task<PageQueryResult<DictItemListDto>>>(this, typeof(IDictItemService).Name, "Query", this, isAllCheck, req);
        }

    }
}