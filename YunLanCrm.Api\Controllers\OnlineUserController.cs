﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.OnlineUser;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    [NonController]
    public class OnlineUserController : ControllerBase
    {
        private readonly ILogger<OnlineUserInfo> _logger;

        private readonly IOnlineUserService _onlineUserService;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="onlineUserService"></param>
        /// <param name="logger"></param>
        public OnlineUserController(IOnlineUserService onlineUserService, ILogger<OnlineUserInfo> logger)
        {
            this._onlineUserService = onlineUserService;
            this._logger = logger;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(OnlineUserAddDto req)
        {
            return await _onlineUserService.ClearUser(req.Adapt<OnlineUserInfo>());
            // return  //await _onlineUserService.AddIdentity(req.Adapt<OnlineUserInfo>());
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(OnlineUserEditDto req)
        {
            return await _onlineUserService.Update(req.Adapt<OnlineUserInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _onlineUserService.Delete(a => a.Id == id);

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<OnlineUserDto> Get(long id)
        {
            return await _onlineUserService.QueryInfo<OnlineUserDto>(a => a.Id == id);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<OnlineUserDetailDto> Detail(long id)
        {
            var obj = await _onlineUserService.QueryInfo(a => a.Id == id);

            if (obj != null)
            {
                return _onlineUserService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<OnlineUserListDto>> List([FromQuery] OnlineUserListQueryDto req)
        {
            var list = new List<OnlineUserListDto>();
            var where = PredicateBuilder.New<OnlineUserInfo>(true);
            var orderBy = new OrderBy<OnlineUserInfo>(req.order, req.sort);
            var data = await _onlineUserService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _onlineUserService.Join(item);
                list.Add(detail.Adapt<OnlineUserListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<OnlineUserListDto>> Query([FromQuery] OnlineUserPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<OnlineUserListDto>> QueryPost(OnlineUserPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<OnlineUserListDto>> PageQuery([FromQuery] OnlineUserPageQueryDto req)
        {
            var list = new List<OnlineUserListDto>();
            var where = PredicateBuilder.New<OnlineUserInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            var totalCount = await _onlineUserService.CountAsync(where);
            var orderBy = new OrderBy<OnlineUserInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _onlineUserService.QueryList(where, orderBy, paging);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _onlineUserService.Join(item);
                list.Add(detail.Adapt<OnlineUserListDto>());
            }
            #endregion

            return new PageQueryResult<OnlineUserListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

    }
}