using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using YunLanCrm.IServices;
using YunLanCrm.Model.Models;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// API Key管理控制器
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)] // 需要管理员权限
    public class ApiKeyController : ControllerBase
    {
        private readonly IApiKeyService _apiKeyService;
        private readonly ILogger<ApiKeyController> _logger;

        public ApiKeyController(IApiKeyService apiKeyService, ILogger<ApiKeyController> logger)
        {
            _apiKeyService = apiKeyService;
            _logger = logger;
        }

        /// <summary>
        /// 创建新的API Key
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建结果</returns>
        [HttpPost("Create")]
        public async Task<IActionResult> CreateApiKey([FromBody] CreateApiKeyRequest request)
        {
            try
            {
                var (apiKeyInfo, plainTextKey) = await _apiKeyService.CreateApiKeyAsync(
                    request.Name,
                    request.ExternalSystemName,
                    request.Description,
                    request.ExpiryDate,
                    request.AllowedIPs,
                    request.AllowedEndpoints,
                    1 // TODO: 从当前用户上下文获取用户ID
                );

                return Ok(new
                {
                    Success = true,
                    Message = "API Key created successfully",
                    Data = new
                    {
                        ApiKeyId = apiKeyInfo.Id,
                        Name = apiKeyInfo.Name,
                        ApiKey = plainTextKey, // 只在创建时返回一次
                        ExternalSystemName = apiKeyInfo.ExternalSystemName,
                        ExpiryDate = apiKeyInfo.ExpiryDate,
                        CreateTime = apiKeyInfo.CreateTime
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating API Key");
                return BadRequest(new { Success = false, Message = "Failed to create API Key" });
            }
        }

        /// <summary>
        /// 获取API Key列表
        /// </summary>
        /// <returns>API Key列表</returns>
        [HttpGet("List")]
        public async Task<IActionResult> GetApiKeys()
        {
            try
            {
                var apiKeys = await _apiKeyService.GetApiKeysAsync();
                var result = apiKeys.Select(x => new
                {
                    x.Id,
                    x.Name,
                    x.ExternalSystemName,
                    x.Description,
                    x.IsActive,
                    x.CreateTime,
                    x.ExpiryDate,
                    x.LastUsedTime,
                    x.UsageCount,
                    KeyValue = "***" // 不返回实际的Key值
                });

                return Ok(new { Success = true, Data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting API Keys");
                return BadRequest(new { Success = false, Message = "Failed to get API Keys" });
            }
        }

        /// <summary>
        /// 启用API Key
        /// </summary>
        /// <param name="id">API Key ID</param>
        /// <returns>操作结果</returns>
        [HttpPost("Enable/{id}")]
        public async Task<IActionResult> EnableApiKey(long id)
        {
            try
            {
                var result = await _apiKeyService.EnableApiKeyAsync(id);
                return Ok(new { Success = result, Message = result ? "API Key enabled" : "Failed to enable API Key" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error enabling API Key {id}");
                return BadRequest(new { Success = false, Message = "Failed to enable API Key" });
            }
        }

        /// <summary>
        /// 禁用API Key
        /// </summary>
        /// <param name="id">API Key ID</param>
        /// <returns>操作结果</returns>
        [HttpPost("Disable/{id}")]
        public async Task<IActionResult> DisableApiKey(long id)
        {
            try
            {
                var result = await _apiKeyService.DisableApiKeyAsync(id);
                return Ok(new { Success = result, Message = result ? "API Key disabled" : "Failed to disable API Key" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error disabling API Key {id}");
                return BadRequest(new { Success = false, Message = "Failed to disable API Key" });
            }
        }

        /// <summary>
        /// 删除API Key
        /// </summary>
        /// <param name="id">API Key ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("Delete/{id}")]
        public async Task<IActionResult> DeleteApiKey(long id)
        {
            try
            {
                var result = await _apiKeyService.DeleteApiKeyAsync(id);
                return Ok(new { Success = result, Message = result ? "API Key deleted" : "Failed to delete API Key" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting API Key {id}");
                return BadRequest(new { Success = false, Message = "Failed to delete API Key" });
            }
        }

        /// <summary>
        /// 获取API Key使用统计
        /// </summary>
        /// <param name="id">API Key ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>使用统计</returns>
        [HttpGet("Usage/{id}")]
        public async Task<IActionResult> GetApiKeyUsage(long id, [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var usage = await _apiKeyService.GetApiKeyUsageStatsAsync(id, startDate, endDate);
                return Ok(new { Success = true, Data = usage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting API Key usage for {id}");
                return BadRequest(new { Success = false, Message = "Failed to get API Key usage" });
            }
        }
    }

    /// <summary>
    /// 创建API Key请求模型
    /// </summary>
    public class CreateApiKeyRequest
    {
        /// <summary>
        /// API Key名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 外部系统名称
        /// </summary>
        public string ExternalSystemName { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 过期时间（可选）
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// 允许的IP地址列表（可选）
        /// </summary>
        public List<string> AllowedIPs { get; set; }

        /// <summary>
        /// 允许的端点列表（可选）
        /// </summary>
        public List<string> AllowedEndpoints { get; set; }
    }
}
