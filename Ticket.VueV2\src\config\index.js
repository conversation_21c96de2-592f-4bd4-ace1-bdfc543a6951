const DEFAULT_CONFIG = {
	APP_NAME: 'CaseX Management',
	APP_TAG: '①',
	BASE_URL: 'http://192.168.1.8:7008/',
	API_TIMEOUT: 500000, //请求Api的超时时间，毫秒为单位
	IDLE_TIMEOUT: 30, // 前端无操作超时时间，单位为分钟
	ENV: 'development', //development、demo、test、production
	LOGGING_ENABLED: true, // 开启前端日志打印
	MY_SHOW_LOGIN_OAUTH: false, //是否显示第三方授权登录
};

// 如果生产模式，就合并动态的APP_CONFIG 文件在 public/config.js
if (APP_CONFIG) {
	Object.assign(DEFAULT_CONFIG, APP_CONFIG);
}

export default DEFAULT_CONFIG;

