[{"name": "IUserService.PageQueryView", "cols": [{"display": "User Name", "name": "UserName"}, {"display": "Real Name", "name": "RealName"}, {"display": "User Type", "name": "UserType"}, {"display": "Escalation", "name": "EscalationName"}, {"display": "Email", "name": "Email"}, {"display": "Status", "name": "Status", "convert": {"0": "Active", "1": "InActive"}}, {"display": "Company Name", "name": "OrgName"}, {"display": "Create Time", "name": "CreateTime"}]}, {"name": "ICmTicketsService.PageQueryView", "cols": [{"display": "Status", "name": "TicketStatusFlag"}, {"display": "Ticket Number", "name": "Ticket_Number"}, {"display": "Subject", "name": "Ticket_Subject"}, {"display": "Customer", "name": "Ticket_Company_Name"}, {"display": "Business Unit", "name": "BusinessUnit"}, {"display": "Project", "name": "Ticket_Customer_Value"}, {"display": "Assigned To", "name": "AssigToName"}, {"display": "Grant Access", "name": "UserGrantName"}, {"display": "Delegate To", "name": "DelegateName"}, {"display": "Priority", "name": "Ticket_Priority"}, {"display": "Category", "name": "Ticket_Category"}, {"display": "Attachments", "name": "IsAttachment"}, {"display": "Ticket Type", "name": "TicketContentType"}, {"display": "Open By", "name": "Ticket_Open_By_Name"}, {"display": "Solved By", "name": "Ticket_Close_By_Name"}, {"display": "Created Date", "name": "CreatedAt"}, {"display": "Created By", "name": "CreatedByName"}, {"display": "Modified Date", "name": "ModifiedAt"}, {"display": "Modified By", "name": "ModifiedByName"}, {"display": "Ticket From", "name": "Ticket_From"}]}, {"name": "ICmCategoryService.PageQueryView_V2", "cols": [{"display": "Category Code", "name": "Category_Code"}, {"display": "Category Description", "name": "Category_Description"}, {"display": "Category Status", "name": "Category_Status_Value", "convert": {"0": "Active", "1": "InActive"}}, {"display": "Projects", "name": "CompanyName"}, {"display": "New Ticket Default <PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"name": "ICmGroupsService.PageQueryView", "cols": [{"display": "Groups Name", "name": "Group_Name"}, {"display": "Description", "name": "Group_Description"}, {"display": "Status", "name": "Group_Status", "convert": {"True": "Active", "False": "InActive"}}]}, {"name": "ICmProjectsService.PageQueryView", "cols": [{"display": "Project ID", "name": "ProjectId"}, {"display": "Project Name", "name": "Project_Name"}, {"display": "Project Description", "name": "Project_Description"}, {"display": "Project Status", "name": "Project_Status_Value", "convert": {"0": "Active", "1": "InActive"}}, {"display": "Company", "name": "CompanyName"}, {"display": "Business Unit", "name": "BusinessUnit"}, {"display": "Create At", "name": "CreatedAt"}]}, {"name": "IEmailsService.PageQueryView", "cols": [{"display": "Subject", "name": "Subject"}, {"display": "Email", "name": "ToAddrs"}, {"display": "Status", "name": "Status", "convert": {"0": "Draft", "1": "Sending", "2": "<PERSON><PERSON>", "3": "Failed"}}, {"display": "Create By", "name": "CreatedByName"}, {"display": "Create At", "name": "CreatedAt"}]}, {"name": "IUserRoleService.GetRoleUsers", "cols": [{"display": "User Name", "name": "UserName"}, {"display": "First Name", "name": "RealName"}, {"display": "Phone", "name": "Mobile"}, {"display": "Status", "name": "Status", "convert": {"0": "Active", "1": "InActive"}}, {"display": "Create Time", "name": "CreateTime"}]}, {"name": "IOrganizationService.Tree", "cols": [{"display": "Company Name", "name": "Name"}, {"display": "Company Code", "name": "Code"}, {"display": "Industry", "name": "CompanyType"}, {"display": "Business Unit", "name": "BusinessUnit"}, {"display": "User Permission", "name": "OrganizationType"}, {"display": "Status", "name": "Status", "convert": {"0": "Active", "1": "InActive"}}, {"display": "Create At", "name": "CreateAt"}]}, {"name": "IDictItemService.Query", "cols": [{"display": "Name", "name": "Name"}, {"display": "Value", "name": "Value"}, {"display": "Description", "name": "Description"}, {"display": "Enable", "name": "Enable", "convert": {"0": "Active", "1": "InActive"}}, {"display": "Create Time", "name": "CreatedAt"}]}]