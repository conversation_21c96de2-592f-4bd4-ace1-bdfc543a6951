﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class departmentApi extends BaseApi {
	Tree(params?: any) {
		return request({
			url: this.baseurl + 'Tree',
			method: 'get',
			params,
		});
	}

	DetailByOrgId(orgId?: any) {
		return request({
			url: this.baseurl + 'DetailByOrgId/' + orgId,
			method: 'get',
		});
	}
}

export default new departmentApi('/api/department/','id');




                        
        
        