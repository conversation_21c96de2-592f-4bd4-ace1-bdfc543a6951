﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Services;

namespace YunLanCrm.Extensions.Middlewares
{
    public class SwaggerIndexMiddleware
    {
        private readonly ILogger<SwaggerIndexMiddleware> _logger;
        private readonly RequestDelegate _next;
        private readonly IConfigService _configService;

        public SwaggerIndexMiddleware(RequestDelegate next
            , ILogger<SwaggerIndexMiddleware> logger, IConfigService configService)
        {
            _logger = logger;
            _next = next;
            _configService = configService;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                //var togglesConfig = ConfigManager.GetAppConfig()?.Toggles;
                var sysConfig = await _configService.GetAppConfig();
                var togglesConfig = sysConfig?.Toggles;

                if (IsSwaggerRequest(context.Request.Path))
                {
                    if (togglesConfig != null && !togglesConfig.SwaggerEnabled)
                    {
                        context.Response.StatusCode = 403;
                        await context.Response.WriteAsync("Forbidden");
                        return;
                    }
                }
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogToFile(LogLevel.Error, $"{ex.Message}", "Swagger.log");
                context.Response.StatusCode = 403;
                await context.Response.WriteAsync("Forbidden");
            }
        }

        private bool IsSwaggerRequest(PathString path)
        {
            string[] swaggerPaths = new[] { $"/{Consts.SwaggerRoutePrefix}", $"/{Consts.SwaggerRoutePrefix}/index.html" };
            return swaggerPaths.Any(p => path.Value?.Contains(p, StringComparison.OrdinalIgnoreCase) == true);
        }
    }
}
