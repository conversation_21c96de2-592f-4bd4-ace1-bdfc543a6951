{
    //本地开发
    //"urls": "http://*:7008", //web服务端口，如果用IIS部署，把这个去掉
    "webUrl": "http://localhost:8002", //定义一个前端的访问地址，主要是给邮件里面使用，其他的地方都可以统一使用这个定义好的地址

    //发布.8
    "urls": "http://*:7008",
    //"webUrl": "http://***********:8064",

    //发布日本服务器
    //"urls": "http://*:7008",
    //"webUrl": "https://ticketdemo.docxtract.com",

    //AP生产服务器
    //"urls": "http://*:7008",
    //"webUrl": "https://ticketmgmt.docxtract.com",

    "Logging": {
        "LogLevel": {
            "Default": "Information", //加入Default否则log4net本地写入不了日志
            "YunLanCrm.AuthHelper.ApiResponseHandler": "Error"
        },
        "Debug": {
            "IncludeScopes": false,
            "LogLevel": {
                "Default": "Warning"
            }
        },
        "Console": {
            "IncludeScopes": false,
            "LogLevel": {
                "Default": "Warning",
                "Microsoft.Hosting.Lifetime": "Debug"
            }
        },
        "Log4Net": {
            "Name": "YunLanCrm"
        }
    },

    "Redis": {
        "ConnectionString": "***********:6379"
    },
    "AllowedHosts": "*",
    "Serilog": {
        "ConnectionString": "Data Source=***********;Initial Catalog=CaseManagementNetCore;User ID=CaseManagementNetCore;Password=CaseManagementNetCore!@#;TrustServerCertificate=True;MultipleActiveResultSets=true;",
        //"ConnectionString": "Data Source=*************;Initial Catalog=CaseManagementNetCore;User ID=YunLan;Password=********;TrustServerCertificate=True;",
        "TableName": "Serilogs",
        "BatchPostingLimit": 50
    },
    "RabbitMQ": {
        "Enabled": false,
        "Connection": "*************",
        "UserName": "",
        "Password": "!",
        "RetryCount": 3
    },
    "Kafka": {
        "Enabled": false,
        "Servers": "localhost:9092",
        "Topic": "blog",
        "GroupId": "blog-consumer",
        "NumPartitions": 3 //主题分区数量
    },
    "EventBus": {
        "Enabled": false,
        "SubscriptionClientName": "YunLanCrm"
    },
    "AppSettings": {
        "RedisCachingAOP": {
            "Enabled": false
        },
        "MemoryCachingAOP": {
            "Enabled": false
        },
        "LogAOP": {
            "Enabled": false
        },
        "TranAOP": {
            "Enabled": false
        },
        "SqlAOP": {
            "Enabled": true,
            "OutToLogFile": {
                "Enabled": true
            },
            "OutToConsole": {
                "Enabled": false
            }
        },
        "LogToDb": {
            "Enabled": true
        },
        "Date": "2018-08-28",
        "SeedDBEnabled": true, //只生成表结构
        "SeedDBDataEnabled": true, //生成表,并初始化数据
        "Author": "YunLanCrm",
        "SvcName": "", // /svc/blog
        "UseLoadTest": false
    },
    "AzureStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=cumminsstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "Elsa": {
        "Server": {
            "BaseUrl": "http://localhost:7008"
        },
        "Smtp": {
            "Host": "localhost",
            "Port": "25",
            "DefaultSender": "<EMAIL>",
            "SecureSocketOptions": 0
        }
    },
    // 请配置MainDB为你想要的主库的ConnId值,并设置对应的Enabled为true；
    // *** 单库操作，把 MutiDBEnabled 设为false ***；
    // *** 多库操作，把 MutiDBEnabled 设为true，其他的从库Enabled也为true **；
    // 具体配置看视频：https://www.bilibili.com/video/BV1BJ411B7mn?p=6

    "MainDB": "WMBLOG_MSSQL_1", //当前项目的主库，所对应的连接字符串的Enabled必须为true
    "MutiDBEnabled": false, //是否开启多库模式
    "CQRSEnabled": false, //是否开启读写分离模式,必须是单库模式，且数据库类型一致，比如都是SqlServer
    "DBS": [
        /*
      对应下边的 DBType
      MySql = 0,
      SqlServer = 1,
      Sqlite = 2,
      Oracle = 3,
      PostgreSQL = 4,
      Dm = 5,//达梦
      Kdbndp = 6,//人大金仓
    */
        {
            "ConnId": "WMBLOG_SQLITE",
            "DBType": 2,
            "Enabled": false,
            "HitRate": 50, // 值越大，优先级越高
            "Connection": "WMBlog.db" //sqlite只写数据库名就行
        },
        {
            "ConnId": "WMBLOG_MSSQL_1",
            "DBType": 1,
            "Enabled": true,
            "HitRate": 40,
            "Connection": "Data Source=***********;Initial Catalog=CaseManagementNetCore;User ID=CaseManagementNetCore;Password=CaseManagementNetCore!@#;TrustServerCertificate=True;MultipleActiveResultSets=true;",
            //"Connection": "Data Source=*************;Initial Catalog=CaseManagementNetCore;User ID=YunLan;Password=********;TrustServerCertificate=True;",
            "ProviderName": "System.Data.SqlClient"
        },
        {
            "ConnId": "WMBLOG_MSSQL_2",
            "DBType": 1,
            "Enabled": false,
            "HitRate": 30,
            "Connection": "Data Source=***********;Initial Catalog=CaseManagementNetCore;User ID=CaseManagementNetCore;Password=CaseManagementNetCore!@#;TrustServerCertificate=True;MultipleActiveResultSets=true;",
            //"Connection": "Data Source=*************;Initial Catalog=CaseManagementNetCore;User ID=YunLan;Password=********;TrustServerCertificate=True;",
            "ProviderName": "System.Data.SqlClient"
        },
        {
            "ConnId": "WMBLOG_MYSQL",
            "DBType": 0,
            "Enabled": false,
            "HitRate": 20,
            "Connection": "server=.;Database=ddd;Uid=root;Pwd=******;Port=10060;Allow User Variables=True;"
        },
        {
            "ConnId": "WMBLOG_MYSQL_2",
            "DBType": 0,
            "Enabled": true,
            "HitRate": 20,
            "Connection": "server=.;Database=blogcore001;Uid=root;Pwd=******;Port=3096;Allow User Variables=True;"
        },
        {
            "ConnId": "WMBLOG_ORACLE",
            "DBType": 3,
            "Enabled": false,
            "HitRate": 10,
            "Connection": "Data Source=127.0.0.1/ops;User ID=OPS;Password=******;Persist Security Info=True;Connection Timeout=60;"
        },
        {
            "ConnId": "WMBLOG_DM",
            "DBType": 5,
            "Enabled": false,
            "HitRate": 10,
            "Connection": "PORT=5236;DATABASE=DAMENG;HOST=localhost;PASSWORD=******;USER ID=******;"
        },
        {
            "ConnId": "WMBLOG_KDBNDP",
            "DBType": 6,
            "Enabled": true,
            "HitRate": 10,
            "Connection": "Server=127.0.0.1;Port=54321;UID=SYSTEM;PWD=system;database=SQLSUGAR4XTEST1;"
        }
    ],
    "Audience": {
        "Secret": "CASEX_SITEJ9@kL1mNpQrS2t#Uv3!Wx4$ABCD",
        "SecretFile": "",
        "Issuer": "Casex",
        "Audience": "Casex",
        "TokenExp": 7200, //60*60*2 2h
        "RefreshTokenExp": 28800 //60*60*8 8h
    },
    "Mongo": {
        "ConnectionString": "mongodb://nosql.data",
        "Database": "BlogCoreDb"
    },
    "Startup": {
        "Cors": {
            "PolicyName": "CorsIpAccess", //策略名称
            "EnableAllIPs": true, //当为true时，开放所有IP均可访问。
            // 支持多个域名端口，注意端口号后不要带/斜杆：比如localhost:8000/，是错的
            // 注意，http://127.0.0.1:1818 和 http://localhost:1818 是不一样的
            "IPs": "http://127.0.0.1:8002,http://localhost:8002,http://***********:8064,https://casexdemo.docxtract.com,https://casex.docxtract.com"
        },
        "AppConfigAlert": {
            "Enabled": true
        },
        "ApiName": "YunLanCrm",
        "IdentityServer4": {
            "Enabled": false, // 这里默认是false，表示使用jwt，如果设置为true，则表示系统使用Ids4模式
            "AuthorizationUrl": "http://localhost:5004", // 认证中心域名
            "ApiName": "yunlancrm.api" // 资源服务器
        },
        "RedisMq": {
            "Enabled": false //redis 消息队列
        },
        "MiniProfiler": {
            "Enabled": false //性能分析开启
        },
        "Nacos": {
            "Enabled": false //Nacos注册中心
        }
    },
    "Middleware": {
        "RequestResponseLog": {
            "Enabled": true
        },
        "IPLog": {
            "Enabled": true
        },
        "RecordAccessLogs": {
            "Enabled": true,
            "IgnoreApis": "/api/permission/getnavigationbar,/api/monitor/getids4users,/api/monitor/getaccesslogs,/api/monitor/server,/api/monitor/getactiveusers,/api/monitor/server,"
        },
        "SignalR": {
            "Enabled": false
        },
        "QuartzNetJob": {
            "Enabled": true
        },
        "Consul": {
            "Enabled": false
        },
        "IpRateLimit": {
            "Enabled": true
        }
    },
    "IpRateLimiting": {
        "EnableEndpointRateLimiting": true, //False: globally executed, true: executed for each
        "StackBlockedRequests": false, //False: Number of rejections should be recorded on another counter
        "RealIpHeader": "X-Real-IP",
        "ClientIdHeader": "X-ClientId",
        "IpWhitelist": [], //白名单
        "EndpointWhitelist": [ "get:/api/xxx", "*:/api/yyy" ],
        "ClientWhitelist": [ "dev-client-1", "dev-client-2" ],
        "QuotaExceededResponse": {
            "Content": "{{\"status\":429,\"msg\":\"Access too frequently, please try again later\",\"success\":false}}",
            "ContentType": "application/json",
            "StatusCode": 429
        },
        "HttpStatusCode": 429, //返回状态码
        "GeneralRules": [ //api规则,结尾一定要带*
            {
                "Endpoint": "*:/api/blog*",
                "Period": "1s",
                "Limit": 10
            },
            {
                "Endpoint": "*/api/*",
                "Period": "1s",
                "Limit": 10
            }
        ]

    },
    "ConsulSetting": {
        "ServiceName": "BlogCoreService",
        "ServiceIP": "localhost",
        "ServicePort": "9291",
        "ServiceHealthCheck": "/healthcheck",
        "ConsulAddress": "http://localhost:8500"
    },
    "PayInfo": { //建行聚合支付信息
        "MERCHANTID": "", //商户号
        "POSID": "", //柜台号
        "BRANCHID": "", //分行号
        "pubKey": "", //公钥
        "USER_ID": "", //操作员号
        "PASSWORD": "", //密码
        "OutAddress": "http://127.0.0.1:12345" //外联地址
    },
    "nacos": {
        "ServerAddresses": [ "http://localhost:8848" ], // nacos 连接地址
        "DefaultTimeOut": 15000, // 默认超时时间
        "Namespace": "public", // 命名空间
        "ListenInterval": 10000, // 监听的频率
        "ServiceName": "blog.Core.Api", // 服务名
        "Port": "9291", // 服务端口号
        "RegisterEnabled": true // 是否直接注册nacos
    },
    "LogFiedOutPutConfigs": {
        "tcpAddressHost": "", // 输出elk的tcp连接地址
        "tcpAddressPort": 0, // 输出elk的tcp端口号
        "ConfigsInfo": [ // 配置的输出elk节点内容 常用语动态标识
            {
                "FiedName": "applicationName",
                "FiedValue": "YunLanCrm.Api"
            }
        ]
    },
    "Encryption": {
        "BackendKey": "4kxaYfrfBuF4n4S9UKTMvyhuTaje3FiXz8zpTCj4JLI=",
        "BackendIV": "JhaVFysHstum7Tfe+8TTPg=="
    },
    //"Kestrel": {
    //    "Endpoints": {
    //        "Http": {
    //            "Url": "http://*:7008"
    //        },
    //        "Https": {
    //            "Url": "https://*:7009",
    //            "Certificate": {
    //                "Path": "C:\\Certificates\\Casex.pfx",
    //                "Password": "******"
    //            }
    //        }
    //    }
    //}
}
