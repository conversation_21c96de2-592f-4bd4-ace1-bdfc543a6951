<template>
	<el-main style="padding: 0 20px">
		<el-descriptions :column="1" border size="small">
            <el-descriptions-item label="ID">{{ info.id }}</el-descriptions-item>
			<el-descriptions-item label="接口编码">{{ info.code }}</el-descriptions-item>
			<el-descriptions-item label="接口名称">{{ info.name }}</el-descriptions-item>
			<el-descriptions-item label="请求接口">{{ info.linkUrl }}</el-descriptions-item>
			<el-descriptions-item label="请求方法">{{ info.action }}</el-descriptions-item>

			<el-descriptions-item label="控制器">{{ info.controller }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ info.createTime }}</el-descriptions-item>
            <el-descriptions-item label="接口状态">{{ info.enabled }}</el-descriptions-item>
            <el-descriptions-item label="备注">{{ info.description }}</el-descriptions-item>

		</el-descriptions>
	</el-main>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent } from 'vue';

export default defineComponent({
	name: 'apiDetail',
	props: {
		info: Object,
	},
	setup(props) {
		const state = reactive({
			isShowDialog: false,
			obj: {},
		});
		// 页面加载时
		onMounted(() => {
			window.console.log('props', props.info);
		});
		return {
			...toRefs(state),
		};
	},
});
</script>
