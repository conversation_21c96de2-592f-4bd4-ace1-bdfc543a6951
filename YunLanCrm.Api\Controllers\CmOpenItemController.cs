﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmOpenItem;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmOpenItemController : ControllerBase
    {
        private readonly ILogger<CmOpenItemInfo> _logger;
        private readonly ICmOpenItemService _cmOpenItemService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmOpenItemService"></param>
        /// <param name="logger"></param>
        public CmOpenItemController(ICmOpenItemService cmOpenItemService, ILogger<CmOpenItemInfo> logger)
        {
            _logger = logger;
            _cmOpenItemService = cmOpenItemService;
            
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<string> Add(CmOpenItemAddOrUpdateDto req)
        {
            return await _cmOpenItemService.AddCmOpenItem(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmOpenItemAddOrUpdateDto req)
        {
            return await _cmOpenItemService.UpdateCmOpenItem(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _cmOpenItemService.Delete(a => a.Id == id);

            return result > 0;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _cmOpenItemService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmOpenItemDto> Get(long id)
        {
            return await _cmOpenItemService.QueryInfo<CmOpenItemDto>(a => a.Id == id);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmOpenItemDto>> GetList([FromQuery] CmOpenItemListQueryDto req)
        {
            return await _cmOpenItemService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<CmOpenItemDto> Detail(long id)
        {
            return await _cmOpenItemService.Detail(id);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmOpenItemDto>> DetailList([FromQuery] CmOpenItemListQueryDto req)
        {
            return await _cmOpenItemService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmOpenItemDto>> Query([FromQuery] CmOpenItemPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmOpenItemDto>> QueryPost(CmOpenItemPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmOpenItemDto>> PageQuery([FromQuery] CmOpenItemPageQueryDto req)
        {
            return await _cmOpenItemService.PageQueryView(req);
        }

    }
}