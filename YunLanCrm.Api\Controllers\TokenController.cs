using System;
using System.Linq;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

using Mapster;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Dto.Token;
using YunLanCrm.Common.DB;
using YunLanCrm.Common;
using Microsoft.AspNetCore.Authorization;
using YunLanCrm.AuthHelper.OverWrite;
using System.Collections.Generic;
using YunLanCrm.Common.HttpContextUser;
using YunLanCrm.Model.Models;
using YunLanCrm.Common.Helper;
using YunLanCrm.Common.Utils;
using YunLanCrm.Dto.User;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using SqlSugar;
using YunLanCrm.Services;
using YunLanCrm.IRepositories;
using YunLanCrm.Common.Seed;
using YunLanCrm.Extensions.Exceptions;
using Org.BouncyCastle.Ocsp;
using YunLanCrm.Dto.Organization;
using YunLanCrm.Repositories;
using System.Collections.ObjectModel;
using System.Data;
using Newtonsoft.Json;
using YunLanCrm.Extensions.Middlewares;
using Furion.DatabaseAccessor;
using LinqKit;
using YunLanCrm.Model.Views;
using Microsoft.AspNetCore.Identity;
using YunLanCrm.Model.Api;
using YunLanCrm.Common.SSO.Saml;
using YunLanCrm.Dto.LogEvent;


namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 认证
    /// </summary>
    [Route("Api/[controller]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class TokenController : ControllerBase
    {
        private readonly ILogger<TokenController> _logger;
        private readonly ILogEventService _logEventService;
        private readonly IUser _user;
        private readonly IUserService _userService;
        private readonly IUserRoleService _userRoleService;
        private readonly IRoleModulePermissionService _roleModulePermissionService;
        private readonly IRoleService _roleService;
        private readonly IMenuService _menuService;
        private readonly IOnlineUserService _onlineUserService;
        private readonly ICmGroupsUsersService _cmGroupsUsersService;
        private SystemAppInfo _systemAppInfo;
        private readonly ICmUserCompanyRepository _cmUserCompanyRepository;

        private readonly IOrganizationService _organizationService;
        private readonly IUserRepository _userRepository;

        private readonly IDictItemService _dictItemService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userService"></param>
        /// <param name="userRoleService"></param>
        /// <param name="user"></param>
        /// <param name="roleService"></param>
        /// <param name="roleModulePermissionService"></param>
        /// <param name="menuService"></param>
        /// <param name="onlineUserService"></param>
        /// <param name="cmGroupsUsersService"></param>
        /// <param name="cmUserCompanyRepository"></param>
        /// <param name="userRepository"></param>
        /// <param name="logEventService"></param>
        /// <param name="logger"></param>
        public TokenController(IUserService userService, IUserRoleService userRoleService,
            IUser user, IRoleService roleService, IRoleModulePermissionService roleModulePermissionService,
            IMenuService menuService,
            IOnlineUserService onlineUserService,
            ICmGroupsUsersService cmGroupsUsersService,
            ICmUserCompanyRepository cmUserCompanyRepository,
            IUserRepository userRepository,
            ILogEventService logEventService,
        ILogger<TokenController> logger, SystemAppInfo systemAppInfo, IOrganizationService organizationService, IDictItemService dictItemService)
        {
            this._userService = userService;
            this._userRoleService = userRoleService;
            this._logger = logger;
            _systemAppInfo = systemAppInfo;
            _organizationService = organizationService;
            this._user = user;
            this._roleService = roleService;
            this._roleModulePermissionService = roleModulePermissionService;
            this._menuService = menuService;
            _onlineUserService = onlineUserService;
            this._cmGroupsUsersService = cmGroupsUsersService;
            this._cmUserCompanyRepository = cmUserCompanyRepository;
            this._userRepository = userRepository;
            _dictItemService = dictItemService;
            this._logEventService = logEventService;
        }

        [HttpPost("HasNeedtToChangePwd")]
        public async Task<bool> HasNeedToChangePwd(LoginInput req)
        {
            UserInfo needChangeObj = null;

            if (req.loginType == 0)
            {
                needChangeObj = await _userService.QueryInfo(a => a.UserName == req.loginName && a.IsDeleted == false && a.Status == 0 && a.NeedToChangePwd == true);

            }
            else if (req.loginType == 3)
            {
                needChangeObj = await _userService.QueryInfo(a => a.Email == req.loginName && a.IsDeleted == false && a.Status == 0 && a.NeedToChangePwd == true);
            }

            if (needChangeObj == null)
            {
                return false;
            }
            else
            {
                return true;
            }
        }

        [HttpPost("ResetInitPassword")]
        public async Task<bool> ResetInitPassword(LoginInput req)
        {
            var empObj = await _userService.QueryInfo(a => a.UserName == req.loginName && a.IsDeleted == false && a.Status == 0 && a.NeedToChangePwd == true);

            if (empObj != null)
            {

                var resetDto = new ResetPasswordDto()
                {
                    userId = empObj.Id,
                    pwd = req.resetPwd
                };

                return await _userService.UserResetPassword(resetDto);
            }
            else
            {
                throw new Exception("Account does not exist.");
            }

        }

        [HttpPost("Logout")]
        public async Task<bool> Logout(TokenInputDto req)
        {
            var result = false;

            var tokenModel = YunLanCrm.Common.Authorizations.Helpers.JwtHelper.SerializeJwt(req.token);

            if (tokenModel == null || tokenModel.Uid <= 0)
            {
                result = true;
            }

            if (string.IsNullOrEmpty(tokenModel.Name) || string.IsNullOrEmpty(req.userName) || !tokenModel.Name.ToLower().Equals(req.userName.ToLower()))
            {
                result = true;
            }

            if (DateTime.Now > tokenModel.ExpireDate
                && !string.IsNullOrEmpty(req.userName)
                && tokenModel.Name.ToLower().Equals(req.userName.ToLower()))
            {
                await _onlineUserService.Delete(a => a.Token.Equals(req.token));

                result = true;
            }
            else if (!string.IsNullOrEmpty(req.userName) && tokenModel.Name.ToLower().Equals(req.userName.ToLower()))
            {
                await _onlineUserService.Delete(a => a.Token.Equals(req.token));
                result = true;
            }

            if (result)
            {
                var reqLog = new LogEventPageQueryDto
                {
                    order = "id",
                    sort = "desc",
                    pageIndex = 1,
                    pageSize = 50
                };
                var where = PredicateBuilder.New<LogEventInfo>(true);

                var orderBy = new OrderBy<LogEventInfo>(reqLog.order, reqLog.sort);
                var paging = reqLog.pageIndex != null ? Paging.Page(reqLog.pageIndex.Value, reqLog.pageSize.Value) : default;
                var data = await _logEventService.QueryList(where, orderBy, paging);
                var logs = data.Select(a => JsonHelper.JsonToObj<OnlineUserInfo>(a.Message)).ToList();
                var onlines = logs.Where(a => a.UserName?.ToLower() == req.userName?.ToLower()
                && a.ClientIP.Contains(_user.ClientIP)
                && (a.LogStatus == null || a.LogStatus != MessageInfo.MsgLogoutStatus))
                    .ToList();
                var onlineInfo = onlines.OrderByDescending(a => a.LoginTime).FirstOrDefault();
                if (onlineInfo != null)
                {
                    onlineInfo.LogStatus = MessageInfo.MsgLogoutStatus;
                    onlineInfo.LogoutTime = DateTime.Now;
                    _logger.LogToDatabase(LogLevel.Information, "Login", JsonConvert.SerializeObject(onlineInfo), "", "");
                }
            }

            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost("Login")]
        [AllowAnonymous]
        public async Task<TokenDto> Login(LoginInput req)
        {
            var empObj = new UserInfo();

            // 记录最后登录时间、失败次数清零

            OnlineUserInfo online = new OnlineUserInfo();
            online.UserId = empObj.Id;
            online.ClientIP = _user.ClientIP;
            online.LoginTime = DateTime.Now;
            online.Device = _user.Device;
            online.ModifyTime = DateTime.Now;
            online.UserName = req.loginName;
            //await _onlineUserService.ClearUser(online);
            var LoginMsg = string.Empty;

            if (req.loginType == 0)
            {
                empObj = await _userService.QueryInfo(a => a.UserName == req.loginName && a.IsDeleted == false && a.Status == 0);
            }

            if (empObj == null)
            {
                online.Description = MessageInfo.MsgLoginAccount;
                online.LogStatus = MessageInfo.MsgLoginUnsuccessful;
                var str = JsonConvert.SerializeObject(online);
                _logger.LogToDatabase(LogLevel.Information, "Login", str, "", "");
                throw new Exception(online.Description);
            }

            var pass = MD5Helper.MD5Encrypt64(req.password + empObj.UserSalt);

            if (empObj.LoginPWD != pass && req.loginType == 0)
            {
                var msg = await UpdateLoginLock(empObj.Id);
                online.Description = msg;// MessageInfo.MsgLoginIncorrectPassword;
                online.LogStatus = MessageInfo.MsgLoginUnsuccessful;
                var str = JsonConvert.SerializeObject(online);
                _logger.LogToDatabase(LogLevel.Information, "Login", str, "", "");
                //throw new Exception(online.Description);
                LoginMsg = msg;
            }
            else
            {
                var msg = await InitLoginLock(empObj.Id);
                if (!string.IsNullOrWhiteSpace(msg))
                {
                    online.Description = msg;
                    online.LogStatus = MessageInfo.MsgLoginUnsuccessful;
                    var str = JsonConvert.SerializeObject(online);
                    _logger.LogToDatabase(LogLevel.Information, "Login", str, "", "");
                    //throw new Exception(online.Description);
                    LoginMsg = msg;
                }
            }

            var obj = await GenerateToken(empObj, online);

            obj.LoginMsg = LoginMsg;

            if (string.IsNullOrWhiteSpace(obj.LoginMsg))
            {
                online.LogStatus = MessageInfo.MsgLoginSuccessful;
                _logger.LogToDatabase(LogLevel.Information, "Login", JsonConvert.SerializeObject(online), "", "");
            }

            obj.AppVersion = _userRepository.Db.Queryable<ConfigInfo>().Where(a => a.IsDeleted == false && a.Name == "AppVersion").Select(a => a.Value).First();

            return obj;
        }

        private async Task<TokenDto> GenerateToken(UserInfo empObj, OnlineUserInfo online)
        {
            //var tokenConfig = (await _userService.GetAppConfig())?.Token;
            var tokenConfig = Appsettings.GetSection<TokenConfig>(Consts.ConfigSections_Audience);

            if (tokenConfig == null)
            {
                online.Description = MessageInfo.MsgLoginNoTokenConfiguration;
                online.LogStatus = MessageInfo.MsgLoginUnsuccessful;
                var str = JsonConvert.SerializeObject(online);
                _logger.LogToDatabase(LogLevel.Information, "Login", str, "", "");
                throw new Exception(online.Description);
            }

            if (string.IsNullOrWhiteSpace(tokenConfig.Audience) || string.IsNullOrWhiteSpace(tokenConfig.Issuer))
            {
                online.Description = MessageInfo.MsgLoginTokenError;
                online.LogStatus = MessageInfo.MsgLoginUnsuccessful;
                var str = JsonConvert.SerializeObject(online);
                _logger.LogToDatabase(LogLevel.Information, "Login", str, "", "");
                throw new Exception(online.Description);
            }

            var roleList = await _userRoleService.QueryList(a => a.UserId == empObj.Id);
            var roles = string.Join(',', roleList.Select(a => a.RoleId));

            // 生成刷新令牌的配置
            var refreshTokenConfig = new TokenConfig()
            {
                TokenExp = tokenConfig.RefreshTokenExp, // 刷新令牌的过期时间设置为8h
                Audience = tokenConfig.Audience,
                Issuer = tokenConfig.Issuer,
                Secret = tokenConfig.Secret,
                TokenValid = tokenConfig.TokenValid
            };

            // 返回刷新令牌 ,前端会在收到401（未授权）响应时，或者在访问令牌即将过期时，自动使用刷新令牌来获取新的访问令牌
            var refreshToken = JwtHelper.IssueJwt(empObj.Id, roles, refreshTokenConfig, empObj.UserType, empObj.UserName, empObj.GroupIds);

            TokenDto obj = await GetToken(empObj, tokenConfig, refreshToken);
            obj.RefreshToken = refreshToken;
            obj.RefreshTokenExp = tokenConfig.RefreshTokenExp;

            return obj;
        }

        private async Task<TokenDto> GetToken(UserInfo empObj, TokenConfig tokenConfig, string refreshToken)
        {
            var roleList = await _userRoleService.QueryList(a => a.UserId == empObj.Id);
            var roles = string.Join(',', roleList.Select(a => a.RoleId));

            var groupUserInfoes = await _cmGroupsUsersService.GetDetailByUserID(empObj.Id);

            if (groupUserInfoes != null)
            {
                string groupIds = string.Empty;

                foreach (var groupUserInfo in groupUserInfoes)
                {
                    groupIds = groupIds + groupUserInfo.cmGroupsId.ToString() + ",";
                }

                if (groupIds.Length > 0)
                {
                    empObj.GroupIds = groupIds.Substring(0, groupIds.Length - 1);
                }
                else
                {
                    empObj.GroupIds = "";
                }
            }

            // 生成访问令牌的配置
            var accessTokenConfig = new TokenConfig()
            {
                TokenExp = tokenConfig.TokenExp, // 访问令牌的过期时间
                Audience = tokenConfig.Audience,
                Issuer = tokenConfig.Issuer,
                Secret = tokenConfig.Secret,
                TokenValid = tokenConfig.TokenValid
            };

            // 生成访问令牌
            var accessToken = JwtHelper.IssueJwt(empObj.Id, roles, accessTokenConfig, empObj.UserType, empObj.UserName, empObj.GroupIds);

            OnlineUserInfo online = new OnlineUserInfo();
            online.UserId = empObj.Id;
            online.ClientIP = _user.ClientIP;
            online.LoginTime = DateTime.Now;
            online.Description = "";
            online.Device = _user.Device;
            online.ModifyTime = DateTime.Now;
            online.UserName = empObj.UserName;
            online.Token = accessToken;
            online.RefreshToken = refreshToken;

            await _onlineUserService.ClearUser(online);

            var obj = new TokenDto()
            {
                Token = accessToken, // 返回访问令牌
                Exp = accessTokenConfig.TokenExp,
                RefreshToken = refreshToken,
            };

            return obj;
        }

        /// <summary>
        /// 为外部系统提供的接口（使用API Key认证）
        /// </summary>
        [HttpGet("External/GenerateToken/{email}")]
        [Authorize(Policy = "ApiKeyPolicy")]
        public async Task<TokenDto> GenerateTokenForExternal(string email)
        {
            var userDbList = await _userRepository.Db.Queryable<UserInfo>()
                      .Where(a => a.IsDeleted == false && a.IsDeleted == false && a.Email == email).ToListAsync();

            TokenDto obj = new TokenDto();

            if (userDbList.Count > 0)
            {
                OnlineUserInfo online = new OnlineUserInfo();

                obj = await GenerateToken(userDbList[0], online);
            }

            return obj;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="System.Exception"></exception>
        [HttpPost("RefreshToken")]
        [AllowAnonymous]
        public async Task<TokenDto> RefreshToken(TokenInputDto req)
        {
            var validToken = JwtHelper.customSafeVerify(req.token);

            if (!validToken)
            {
                throw new System.Exception("Invalid Token");
            }

            var userTokenInfo = _cmUserCompanyRepository.Db.Queryable<OnlineUserInfo>().Where(a => a.RefreshToken == req.token).ToList();

            if (userTokenInfo.Count <= 0)
            {
                throw new CrmException("Invalid session.", 4005);
            }

            // 反序列化刷新令牌
            var tokenModel = JwtHelper.SerializeJwt(req.token);

            // 检查刷新令牌是否有效
            if (tokenModel == null || tokenModel.Uid <= 0)
            {
                throw new System.Exception("Invalid Token");
            }

            //一个用户 可能在不同的浏览器登录，所以会存在多个 token ，这里还需要根据 token 来判断 todo
            //if(list.Any(a=>a.Token))

            var empObj = await _userService.QueryInfo(a => a.Id == tokenModel.Uid && a.Status == 0);

            if (empObj == null)
            {
                throw new CrmException("Invalid session.", 4005);
            }

            //如果是基于用户的授权策略，这里要添加用户;如果是基于角色的授权策略，这里要添加角色
            var roleList = await _userRoleService.QueryList(a => a.UserId == empObj.Id);

            var roles = string.Join(',', roleList.Select(a => a.RoleId));

            //获取token的配置信息
            //var tokenConfig = (await _userService.GetAppConfig())?.Token;
            var tokenConfig = Appsettings.GetSection<TokenConfig>(Consts.ConfigSections_Audience);

            if (tokenConfig == null)
            {
                throw new Exception("No token configuration information.");
            }

            if (string.IsNullOrWhiteSpace(tokenConfig.Audience) || string.IsNullOrWhiteSpace(tokenConfig.Issuer))
            {
                throw new Exception("Token configuration error.");
            }

            var obj = await GetToken(empObj, tokenConfig, req.token);

            obj.RefreshToken = req.token; // 返回原始的刷新令牌
            obj.AppVersion = _userRepository.Db.Queryable<ConfigInfo>().Where(a => a.IsDeleted == false && a.Name == "AppVersion").Select(a => a.Value).First();

            return obj;
        }

        [HttpGet("TokenCheck")]
        [NonAction]
        public bool TokenCheck([FromQuery] TokenInputDto req)
        {
            var tokenModel = YunLanCrm.Common.Authorizations.Helpers.JwtHelper.SerializeJwt(req.token);
            if (tokenModel == null || tokenModel.Uid <= 0)
            {
                //throw new System.Exception("Token configuration error.");
                return false;
            }

            if (DateTime.Now > tokenModel.ExpireDate)
            {
                //throw new System.Exception("Date Expired");
                return false;
            }

            if (string.IsNullOrEmpty(tokenModel.Name) || string.IsNullOrEmpty(req.userName) || !tokenModel.Name.ToLower().Equals(req.userName.ToLower()))
            {
                //throw new System.Exception("Token configuration error.");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 根据token 获取我的信息
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("GetMyInfo")]
        //public async Task<LoginDto> GetMyInfo([FromQuery] TokenInputDto req)
        public async Task<LoginDto> GetMyInfo()
        {
            //var tokenModel = YunLanCrm.Common.Authorizations.Helpers.JwtHelper.SerializeJwt(req.token);
            //if (tokenModel == null || tokenModel.Uid <= 0)
            //{
            //    throw new System.Exception("Token configuration error.");
            //}

            //if (string.IsNullOrEmpty(tokenModel.Name) || string.IsNullOrEmpty(req.userName))
            //{
            //    throw new System.Exception("Token configuration error.");
            //}

            //var obj = await _userService.QueryInfo(a => a.Id == tokenModel.Uid && (a.UserName == req.userName || a.Email == req.userName));

            //if (obj == null)
            //{
            //    throw new System.Exception("Account does not exist.");
            //}

            if (string.IsNullOrWhiteSpace(_user.Name) && string.IsNullOrWhiteSpace(_user.Email))
            {
                throw new ArgumentException("At least one of Name or Email must be non-empty.");
            }
            var obj = await _userService.QueryInfo(a => a.Id == _user.UserId);
            if (obj == null)
            {
                throw new System.Exception($"Account with userId {_user.UserId} does not exist.");
            }
            var model = obj.Adapt<LoginDto>();

            model.Name = obj.UserName;
            model.LoginName = obj.UserName;
            model.Language = obj.Language;
            model.NeedToChangePwd = obj.NeedToChangePwd;

            //获取用户的角色名称
            var roleList = (await _userRoleService.QueryList(a => a.UserId == model.Id))?.Select(a => a.RoleId);
            var names = await _roleService.QueryList(a => roleList.Contains(a.Id));
            model.RoleNames = names.Select(a => a.Name).ToList();

            //Project
            var userCompanyList = await _cmUserCompanyRepository.Db.Queryable<CmUserCompanyInfo>()
                .Where(a => a.IsDeleted == false && a.userId == model.Id).ToListAsync();

            var userProjectList = userCompanyList.Select(a => a.cmProjectId).ToList();

            var organizationList = await _organizationService.GetOrganizationList(new OrganizationTreeDto { });

            List<long> includeProjectList = new List<long>();

            foreach (var item in userProjectList)
            {
                var allOrgIds = _organizationService.GetAllOrgIds(item, organizationList);

                includeProjectList.Add(item);

                includeProjectList.AddRange(allOrgIds);
            }

            List<int> convertedList = includeProjectList.Select(Convert.ToInt32).ToList();

            //model.Projects = userCompanyList.Select(a => a.cmProjectId.ObjToInt()).ToList();
            model.Projects = convertedList;

            //获取用户的全部权限标识
            var pms = await _roleModulePermissionService.QueryList(a => roleList.Contains(a.RoleId));
            var pmsIds = pms.Select(p => p.PermissionId);
            var rolePermissions = await _menuService.QueryList(a => pmsIds.Contains(a.Id));
            model.Permissions = rolePermissions.Where(a => !string.IsNullOrWhiteSpace(a.Identification)).Select(a => a.Identification).ToList();

            model.SystemUrl = _systemAppInfo.AppUrl;

            model.Mobile = string.Empty;
            model.Email = string.Empty;

            return model;
        }

        private async Task<string> UpdateLoginLock(long id)
        {
            var msg = string.Empty;
            var num = 3;
            var userList = await _userRepository.Db.Queryable<UserInfo>()
                        .InnerJoin<UserView>((a, b) => a.Id == b.Id)
                        .Where((a, b) => a.IsDeleted == false && a.Id == id && a.Status == 0)
                        .Select((a, b) => new
                        {
                            LoginError = SqlFunc.ToInt32(a.LoginError),
                            b.minuteDate
                        })
                        .ToListAsync();
            if (userList != null && userList.Count > 0)
            {
                var numError = userList.Select(a => a.LoginError).FirstOrDefault();
                var minuteDate = userList.Select(a => a.minuteDate).FirstOrDefault();
                if (minuteDate > 60)
                {
                    var result = await _userRepository.Db.Updateable<UserInfo>()
                        .SetColumns(t => t.UpdateTime == DateTime.Now)
                        .SetColumns(t => t.LoginError == 1)
                        .SetColumns(t => t.PassDate == DateTime.Now)
                        .Where(a => a.Id == id)
                        .ExecuteCommandAsync();
                    if (result > 0)
                    {
                        msg = string.Format("Your login attempt was not successful. Please try again. Your account will be locked out after 3 unsuccessful attempts. {0} times left", 2);
                        return msg;
                    }
                }
                else
                {
                    var result = await _userRepository.Db.Updateable<UserInfo>()
                    .SetColumns(t => t.UpdateTime == DateTime.Now)
                    .SetColumns(t => t.LoginError == (numError + 1))
                    .SetColumns(t => t.PassDate == DateTime.Now)
                    .Where(a => a.Id == id)
                    .ExecuteCommandAsync();
                    if (result > 0)
                    {
                        msg = string.Format("Your login attempt was not successful. Please try again. Your account will be locked out after 3 unsuccessful attempts. {0} times left", num - (numError + 1));
                    }
                }
                if (num <= (numError + 1))
                {
                    msg = string.Format("Account is locked out, please try again in one hour, or click 'Forgot Password' to reset the password, or contact the system administrator for support.");
                }

            }


            return msg;
        }

        private async Task<string> InitLoginLock(long id)
        {
            var msg = string.Empty;
            var userList = await _userRepository.Db.Queryable<UserInfo>()
            .InnerJoin<UserView>((a, b) => a.Id == b.Id)
            .Where((a, b) => a.IsDeleted == false && a.Id == id && a.Status == 0)
            .Select((a, b) => new
            {
                LoginError = SqlFunc.ToInt32(a.LoginError),
                b.minuteDate
            })
            .ToListAsync();
            if (userList != null && userList.Count > 0)
            {
                var numError = userList.Select(a => a.LoginError).FirstOrDefault();
                var minuteDate = userList.Select(a => a.minuteDate).FirstOrDefault();
                if (minuteDate > 60 || numError < 3)
                {
                    var result = await _userRepository.Db.Updateable<UserInfo>()
                                .SetColumns(t => t.UpdateTime == DateTime.Now)
                                .SetColumns(t => t.LoginError == 0)
                                .SetColumns(t => t.PassDate == DateTime.Now)
                                .Where(a => a.Id == id)
                                .ExecuteCommandAsync();
                }
                else
                {
                    msg = string.Format("Account is locked out, please try again in one hour, or click 'Forgot Password' to reset the password, or contact the system administrator for support.");
                }

            }
            return msg;
        }

        [HttpGet("SSOLogin")]
        [AllowAnonymous]
        public async Task<ApisPageResult<string>> SSOLogin(string bptsSSOLogin)
        {
            string result = string.Empty;

            var samlConfig = await GetSamlConfiguration();

            var request = new AuthRequest(
                samlConfig.EntityID,
                samlConfig.AssertionConsumerURL
            );

            var samlEndpoint =
                !string.IsNullOrEmpty(bptsSSOLogin) && bptsSSOLogin == "8c303c46-79d1-44cd-ade6-fd0b2c7bec18"
                    ? samlConfig.EndpointBPTS
            : samlConfig.Endpoint;

            result = request.GetRedirectUrl(samlEndpoint);

            return new ApisPageResult<string>
            {
                Data = result,
            };
        }

        [HttpPost("SSOCallback")]
        [AllowAnonymous]
        public async Task<IActionResult> SSOCallback([FromForm] string SAMLResponse, string bptsSSOLogin)
        {
            try
            {
                if (string.IsNullOrEmpty(SAMLResponse))
                {
                    return BadRequest("SAML Response is missing");
                }

                _logger.LogToFile(LogLevel.Information, SAMLResponse, "SSOCallback.log");

                var samlConfig = await GetSamlConfiguration();

                var samlCertificate =
                    !string.IsNullOrEmpty(bptsSSOLogin) && bptsSSOLogin == "8c303c46-79d1-44cd-ade6-fd0b2c7bec18"
                        ? samlConfig.CertificateBPTS
                        : samlConfig.Certificate;

                // Log certificate info for debugging
                _logger.LogToFile(LogLevel.Information, $"Certificate length: {samlCertificate?.Length ?? 0}", "SSOCallback.log");
                _logger.LogToFile(LogLevel.Information, $"Certificate starts with: {samlCertificate?.Substring(0, Math.Min(50, samlCertificate?.Length ?? 0))}", "SSOCallback.log");

                Response samlResponse;
                try
                {
                    samlResponse = new Response(samlCertificate, SAMLResponse);
                    _logger.LogToFile(LogLevel.Information, "SAML Response created successfully", "SSOCallback.log");
                }
                catch (Exception certEx)
                {
                    _logger.LogToFile(LogLevel.Error, $"Certificate error: {certEx.Message}", "SSOCallback.log");
                    throw;
                }

                var frontendUrl = $"{_systemAppInfo.AppUrl}/#/login?bptsSSOLogin=8c303c46-79d1-44cd-ade6-fd0b2c7bec18";

                string email;

                bool isValid;
                try
                {
                    _logger.LogToFile(LogLevel.Information, "Starting SAML validation", "SSOCallback.log");
                    isValid = samlResponse.IsValid();
                    _logger.LogToFile(LogLevel.Information, $"SAML validation result: {isValid}", "SSOCallback.log");
                }
                catch (Exception validationEx)
                {
                    _logger.LogToFile(LogLevel.Error, $"SAML validation error: {validationEx.Message}", "SSOCallback.log");
                    _logger.LogToFile(LogLevel.Error, $"SAML validation stack trace: {validationEx.StackTrace}", "SSOCallback.log");
                    isValid = false;
                }

                if (isValid)
                {
                    try
                    {
                        OnlineUserInfo online = new OnlineUserInfo();

                        var str = JsonConvert.SerializeObject(online);

                        string dataKey = Guid.NewGuid().ToString();

                        email = samlResponse.GetNameID();

                        var userDbList = await _userRepository.Db.Queryable<UserInfo>()
                            .Where(a => a.IsDeleted == false && a.IsDeleted == false && a.Email == email).ToListAsync();

                        if (userDbList.Count > 0)
                        {
                            if (userDbList.Count > 1)
                            {
                                online.LogStatus = MessageInfo.MsgLoginUnsuccessful;
                                _logger.LogToDatabase(LogLevel.Information, "SSOLogin2", str, dataKey, "Multiple account are sharing this mailbox, please contact your Administrator.");
                            }
                            else
                            {
                                var obj = await GenerateToken(userDbList[0], online);

                                var logData = new
                                {
                                    UserName = userDbList[0].UserName,
                                    Token = obj.Token,
                                    RefreshToken = obj.RefreshToken,
                                    Exp = obj.Exp
                                };

                                string jsonLogData = JsonConvert.SerializeObject(logData);

                                online.LogStatus = MessageInfo.MsgLoginSuccessful;
                                //_logger.LogToDatabase(LogLevel.Information, "SSOLogin3", str, dataKey, jsonLogData);

                                LogEventInfo logEventInfo = new LogEventInfo()
                                {
                                    Message = string.Empty,
                                    MessageTemplate = string.Empty,
                                    Level = "Information",
                                    LogType = "SSOLogin3",
                                    DataKey = dataKey,
                                    DataMessage = jsonLogData,
                                    TimeStamp = DateTime.Now,
                                };

                                await _userRepository.Db.Insertable<LogEventInfo>(logEventInfo).ExecuteCommandAsync();
                            }
                        }
                        else
                        {
                            online.LogStatus = MessageInfo.MsgLoginUnsuccessful;
                            _logger.LogToDatabase(LogLevel.Information, "SSOLogin1", str, dataKey, "The mailbox is not found in the system, please contact your Administrator");
                        }

                        frontendUrl += $"&messageType=" + dataKey;
                    }
                    catch (Exception e)
                    {

                    }
                }

                return Redirect(frontendUrl);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SAML callback processing failed");
                return Redirect($"{_systemAppInfo.AppUrl}/#/login?bptsSSOLogin=8c303c46-79d1-44cd-ade6-fd0b2c7bec18");
            }
        }

        [HttpGet("SSOLogout")]
        [AllowAnonymous]
        public async Task<ApisPageResult<string>> SSOLogout(string bptsSSOLogin, long userId)
        {
            string result = string.Empty;

            var samlConfig = await GetSamlConfiguration();

            var userDbList = await _userRepository.Db.Queryable<UserInfo>()
                .Where(a => a.IsDeleted == false && a.IsDeleted == false && a.Id == userId).ToListAsync();

            var samlDestination =
                !string.IsNullOrEmpty(bptsSSOLogin) && bptsSSOLogin == "8c303c46-79d1-44cd-ade6-fd0b2c7bec18"
                    ? samlConfig.DestinationBPTS
                    : samlConfig.Destination;

            var request = new LogoutRequest(
                samlConfig.EntityID,
                userDbList[0].Email,
                samlDestination
            );

            var samlEndpoint =
                !string.IsNullOrEmpty(bptsSSOLogin) && bptsSSOLogin == "8c303c46-79d1-44cd-ade6-fd0b2c7bec18"
                    ? samlConfig.EndpointBPTS
                    : samlConfig.Endpoint;

            result = request.GetRedirectUrl(samlEndpoint);

            return new ApisPageResult<string>
            {
                Data = result,
            };
        }

        [HttpGet("SSOLogoutCallback")]
        [AllowAnonymous]
        public async Task<IActionResult> SSOLogoutCallback([FromQuery] string SAMLRequest, string bptsSSOLogin)
        {
            var result = false;

            _logger.LogToFile(LogLevel.Information, SAMLRequest, "SSOLogoutCallback.log");
            _logger.LogToFile(LogLevel.Information, bptsSSOLogin, "SSOLogoutCallback.log");

            var samlConfig = await GetSamlConfiguration();

            var samlCertificate =
                !string.IsNullOrEmpty(bptsSSOLogin) && bptsSSOLogin == "8c303c46-79d1-44cd-ade6-fd0b2c7bec18"
                    ? samlConfig.CertificateBPTS
                    : samlConfig.Certificate;

            LogoutResponse samlLogoutResponse = new LogoutResponse(samlCertificate, SAMLRequest);

            var username = samlLogoutResponse.GetNameID();

            _logger.LogToFile(LogLevel.Information, username, "SSOLogoutCallback.log");

            var userDbList = await _userRepository.Db.Queryable<UserInfo>()
                .Where(a => a.IsDeleted == false && a.IsDeleted == false && a.Email == username).ToListAsync();

            if (userDbList.Count > 0)
            {
                var userId = userDbList[0].Id;

                await _onlineUserService.Delete(a => a.UserId == userId);
            }

            var frontendUrl = $"{_systemAppInfo.AppUrl}/#/login";

            return Redirect(frontendUrl);
        }

        [HttpGet("GetSSOLoginMessage")]
        [AllowAnonymous]
        public async Task<ApisPageResult<string>> GetSSOLoginMessage(string dataKey)
        {
            string dataMessage = string.Empty;

            string data = string.Empty;

            var logList = await _userRepository.Db.Queryable<LogEventInfo>()
                .Where(a => a.DataKey == dataKey).ToListAsync();

            if (logList.Count > 0)
            {
                data = logList[0].LogType;
                dataMessage = logList[0].DataMessage;

                await _userRepository.Db.Deleteable<LogEventInfo>()
                    .Where(a => a.DataKey == dataKey).ExecuteCommandAsync();
            }

            return new ApisPageResult<string>
            {
                Data = data,
                ResultMsg = dataMessage,
            };
        }

        private async Task<SamlConfiguration> GetSamlConfiguration()
        {
            var dictItems = await _dictItemService.GetMany(new List<string> { "SSO" });

            var ssoConfig = dictItems
                .SelectMany(x => x.Items)
                .ToDictionary(x => x.ItemName, x => x.description ?? string.Empty);

            return new SamlConfiguration
            {
                EntityID = GetConfigValue(ssoConfig, "SamlEntityID"),
                Endpoint = GetConfigValue(ssoConfig, "SamlEndpoint"),
                EndpointBPTS = GetConfigValue(ssoConfig, "SamlEndpoint_BPTS"),
                Certificate = GetConfigValue(ssoConfig, "SamlCertificate"),
                CertificateBPTS = GetConfigValue(ssoConfig, "SamlCertificate_BPTS"),
                Destination = GetConfigValue(ssoConfig, "SamlDestination"),
                DestinationBPTS = GetConfigValue(ssoConfig, "SamlDestination_BPTS"),
                AssertionConsumerURL = GetConfigValue(ssoConfig, "SamlAssertionConsumerURL")
            };
        }

        private static string GetConfigValue(Dictionary<string, string> config, string key)
        {
            return config.TryGetValue(key, out var value) ? value : string.Empty;
        }
    }

    public class SamlConfiguration
    {
        public string EntityID { get; set; }
        public string Endpoint { get; set; }
        public string EndpointBPTS { get; set; }
        public string Certificate { get; set; }
        public string CertificateBPTS { get; set; }
        public string Destination { get; set; }
        public string DestinationBPTS { get; set; }
        public string AssertionConsumerURL { get; set; }
    }
}
