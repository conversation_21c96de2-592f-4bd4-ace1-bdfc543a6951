﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class configApi extends BaseApi {
	SaveConfig(data: any) {
		return request({
			url: this.baseurl + 'SaveConfig',
			method: 'post',
			data,
		});
	}

	GetConfig(params?: any) {
		return request({
			url: this.baseurl + 'GetConfig',
			method: 'get',
			params,
		});
	}
	GetAppConfig(params?: any) {
		return request({
			url: this.baseurl + 'GetAppConfig',
			method: 'get',
			params,
		});
	}
	UpdateByName(data: any) {
		return request({
			url: this.baseurl + 'UpdateByName',
			method: 'post',
			data,
		});
	}
}

export default new configApi('/api/config/', 'id');
