﻿using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Persistence;
using Elsa.Services.Models;
using YunLanCrm.Common.HttpContextUser;
using YunLanCrm.IRepositories;
using YunLanCrm.Model.Models;
using YunLanCrm.Model;
using YunLanCrm.Model.Dto.Emails;
using YunLanCrm.IServices;
using Elsa.Expressions;
using YunLanCrm.Services;

namespace YunLanCrm.ElsaWorkflows.Activities
{
    /// <summary>
    /// 流程结束节点
    /// </summary>
    [Action(Category = "基础节点", DisplayName = "Finish", Description = "结束节点", Outcomes = new string[] { })]
    public class FinishActivity : Elsa.Activities.ControlFlow.Finish
    {
        protected readonly IUser _user;
        protected readonly IWorkflowCrmService  _workflowCrmService;
        protected readonly IWorkflowCrmRepository _workflowCrmRepository;
        protected readonly IWorkflowDefinitionStore _workflowDefinitionStore;

        [ActivityInput(Hint = "Config", SupportedSyntaxes = new[] { SyntaxNames.Literal, SyntaxNames.JavaScript, SyntaxNames.Liquid })]
        public string Config { get; set; } = default;

        public FinishActivity(IUser user, IWorkflowCrmRepository workflowCrmRepository, IWorkflowDefinitionStore workflowDefinitionStore, IWorkflowCrmService workflowCrmService)
        {
            _workflowDefinitionStore = workflowDefinitionStore;
            _workflowCrmRepository = workflowCrmRepository;
            _user = user;
            _workflowCrmService = workflowCrmService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            var workflowInstance = context.WorkflowInstance;
            var obj = _workflowCrmRepository.Db.Queryable<WorkflowBusinessInfo>().Where(a => a.ElsaInstancesId == workflowInstance.Id).First();

            //表单数据
            var formObj = context.GetVariable("FormData");

            //添加流转记录
            WorkflowTransferInfo log = new()
            {
                TransferId = Guid.NewGuid(),
                WorkflowInstanceId = context.WorkflowInstance.Id,
                TransferDate = DateTime.Now.AddSeconds(1),
                TransferType = Consts.Activity_Type_Finish,
                TransferTypeName = "结束",
                ActivityId = context.ActivityBlueprint.Id,
                CreatedById = _user.UserId,
                Display = true
            };
            await _workflowCrmRepository.Db.Insertable(log).ExecuteCommandAsync();

            //更新当前审批节点
            await _workflowCrmRepository.Db.Updateable<WorkflowBusinessInfo>()
                .SetColumns(a => a.NextNode == "结束")
                .Where(a => a.ElsaInstancesId == workflowInstance.Id)
                .ExecuteCommandAsync();

            //发邮件给流程发起人
            //var toList = _workflowCrmRepository.Db.Queryable<UserInfo>()
            //           .Where(a => a.Id == obj.CreatedById && !string.IsNullOrWhiteSpace(a.Email))
            //           .Select(a => a.Email).Distinct().ToList();

            var req = new SendEmailWorkflowDto()
            {
                DefinitionId = workflowInstance.DefinitionId,
                WorkflowInstanceId=workflowInstance.Id,
                NoticeType = "Finish",
                //To = toList,
                Entity = formObj,
                DateType = obj.DateType,
                DateValue = obj.DataId,
                ToUserIds=new List<int>() { obj.CreatedById }
            };
            await _workflowCrmService.SendWorkflowEmail(req);


            return await base.OnExecuteAsync(context);
        }

    }
}