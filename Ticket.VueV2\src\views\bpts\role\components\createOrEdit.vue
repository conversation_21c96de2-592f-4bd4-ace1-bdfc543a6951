﻿<template>
	<div class="role-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="400px" :close-on-click-modal="false" draggable>
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="90px" label-position="top">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.roleFields.name')" prop="name">
							<el-input v-model="ruleForm.name"  />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.roleFields.description')" prop="description">
							<el-input v-model="ruleForm.Msg" type="textarea"  maxlength="150"> </el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">
						{{ $t('message.page.buttonCancel') }}
					</el-button>
					<el-button v-if="dialogParams.action === 'Create'" @click="onInitForm" size="small" type="danger">
						{{ $t('message.page.buttonReset') }}
					</el-button>
					<Auth :value="'systemRole.Delete'">
						<el-button v-if="dialogParams.action === 'Edit'" @click="onDelete" type="danger" size="small">
							{{ $t('message.page.buttonDelete') }}
						</el-button>
					</Auth>
					<el-button :loading="saveLoading" type="primary" @click="onSubmit" size="small">
						{{ $t('message.page.buttonSave') }}
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';

import roleApi from '/@/api/role';
import { useI18n } from 'vue-i18n';
import Auth from '/@/components/auth/auth.vue';

interface DialogParams {
	action: string;
	id: number;
}

export default defineComponent({
	name: 'roleCreateOrEdit',
	components: { Auth },
	setup(props, context) {
		const { t } = useI18n();
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			title: t('message.roleFields.createRole'),
			isShowDialog: false,
			saveLoading: false,
			deleteLoading: false,
			dialogParams: {
				action: '',
				id: -1,
			},
			ruleForm: {
				id: 0, //
				isDeleted: false, //
				name: '', //
				description: '', //
				orderSort: 0, //
				dids: '', //
				authorityScope: 0, //
				enabled: false, //
				createId: 0, //
				createBy: '', //
				createTime: new Date(), //
				modifyId: 0, //
				modifyBy: '', //
				modifyTime: new Date(), //
			},
			rules: {
				name: [{ required: true, message: 'Please input', trigger: 'blur' }],
				sort: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
		});
		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			state.dialogParams = parmas;

			onInitForm();

			if (parmas.action == 'Create') {
				state.title = t('message.roleFields.createRole');
			} else if (parmas.action == 'Edit') {
				state.title = t('message.roleFields.editRole');
				getData(parmas.id);
			} else {
				ElMessage.error('Parameter action cannot be empty.');
			}
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}

				var obj = state.ruleForm;

				if (state.dialogParams.action == 'Create') {
					obj.groupId = 2;
					obj.isCanDelete = true;
				}

				state.saveLoading = true;

				roleApi
					.Save(obj)
					.then((rs) => {
						ElMessage.success('Succeed');
						context.emit('fetchData', state.dialogParams.id);
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};
		//根据id获取完整的信息
		const getData = (id: any) => {
			roleApi.GetByKey(id).then((rs) => {
				state.ruleForm = Object.assign({}, rs.data);
			});
		};
		//删除纪录
		const onDelete = () => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				state.deleteLoading = true;
				roleApi
					.DeleteByKey(state.ruleForm.id)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.deleteLoading = false;
					});
			});
		};
		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.ruleForm = {
				id: 0, //
				isDeleted: false, //
				name: '', //
				description: '', //
				orderSort: 0, //
				dids: '', //
				authorityScope: 0, //
				enabled: false, //
				createId: 0, //
				createBy: '', //
				createTime: new Date(), //
				modifyId: 0, //
				modifyBy: '', //
				modifyTime: new Date(), //
			};
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			...toRefs(state),
		};
	},
});
</script>
