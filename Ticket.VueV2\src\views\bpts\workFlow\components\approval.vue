﻿<template>
	<div class="workflow-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="509px">
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="90px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="审批意见" prop="msg">
							<el-input v-model="ruleForm.msg" type="textarea" placeholder="请输入审批意见" maxlength="150">
							</el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">取消</el-button>
					<el-button :loading="saveLoading" type="primary" @click="onSubmit" size="small">确定</el-button>
				</span>
			</template>
		</el-dialog>
		<SelectUser ref="selectUserRef" @fetchData="setSelectItem" />
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent, ref } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';
import SelectUser from '/@/views/bpts/user/components/SelectUser.vue';
import workflowExecutionLogRecordsApi from '/@/api/workflowExecutionLogRecords/index';

export default defineComponent({
	name: 'workflowCreateOrEdit',
	components: { SelectUser },
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const selectUserRef = ref();
		const userRef = ref();
		const state = reactive({
			title: 'Create Workflow',
			isShowDialog: false,
			saveLoading: false,
			deleteLoading: false,
			ruleForm: {
				crmWorkflowId: 0,
				elsaInstancesId: '',
				executionType: '',
				nextApprovalType: '0',
				nextApprovalValue: '',
				msg: '',
			},
			rules: {
				msg: [{ required: true, message: '请输入审批意见', trigger: 'blur' }],
			},
			userDataSelect: [] as any, //已选择的主管
			userData: [], //部门主管 数据源
		});
		// 打开弹窗
		const openDialog = (obj: any) => {
			state.ruleForm = obj;
			state.isShowDialog = true;
			state.title = '同意';
			if (state.ruleForm.executionType == 'Refuse') {
				state.title = '拒绝';
			}
		};
		// 关闭弹窗
		const closeDialog = () => {
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}
				var obj = state.ruleForm;
				state.saveLoading = true;
				workflowExecutionLogRecordsApi
					.WorkflowExecution(obj)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};
		const onVisible = () => {
			userRef.value.blur(); //使选择器的输入框失去焦点，并隐藏下拉框
			selectUserRef.value.openDialog({ multiple: false });
		};
		const setSelectItem = (items: any) => {
			const newItem = [...items]; //复制数组
			state.userData = items;
			state.userDataSelect = newItem.map((a) => {
				return a.itemId;
			});
		};
		return {
			selectUserRef,
			userRef,
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onVisible,
			setSelectItem,
			...toRefs(state),
		};
	},
});
</script>
