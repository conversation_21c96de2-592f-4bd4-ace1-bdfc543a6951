﻿using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using YunLanCrm.Common;
using YunLanCrm.IServices;

namespace YunLanCrm.Extensions.Middlewares
{
    public class RefererMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IConfigService _configService;

        public RefererMiddleware(RequestDelegate next, IConfigService configService)
        {
            _next = next;
            _configService = configService;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var sysConfig = await _configService.GetAppConfig();
            var togglesConfig = sysConfig?.Toggles;

            if (togglesConfig != null && togglesConfig.RefererValidation)
            {
                var path = context.Request.Path;

                var lowerPath = path.ToString().ToLowerInvariant();

                if (!string.IsNullOrWhiteSpace(lowerPath) && (
                       lowerPath == "/api/index.html" 
                    || lowerPath == "/swagger/default/swagger.json"
                    || lowerPath == "/v1/workflow-definitions"
                    || lowerPath == "/api/token/ssocallback"
                    || lowerPath == "/index-mini-profiler/results"
                    )
                )
                {
                    await _next(context);
                    return;
                }

                // 检查当前endpoint是否使用ApiKeyPolicy
                var endpoint = context.GetEndpoint();
                var authorizeData = endpoint?.Metadata?.GetOrderedMetadata<IAuthorizeData>();
                var hasApiKeyPolicy = authorizeData?.Any(a => a.Policy == "ApiKeyPolicy") == true;

                if (hasApiKeyPolicy)
                {
                    // 如果是API Key策略的接口，跳过Referer验证
                    await _next(context);
                    return;
                }

                var origin = context.Request.Headers["Origin"].ToString();
                var referer = context.Request.Headers["Referer"].ToString();
                var ips = Appsettings.app("Startup", "Cors", "IPs").ObjToString();

                if (string.IsNullOrEmpty(ips))
                {
                    context.Response.StatusCode = 401;
                    context.Response.ContentType = "text/plain";
                    await context.Response.WriteAsync("401 Unauthorized");
                    return;
                }

                var allowedOrigins = ips.Split(",").ToList();
                var normalizedReferer = referer.TrimEnd('/');
                var normalizedOrigins = allowedOrigins.Select(origin => origin.TrimEnd('/')).ToList();

                if (string.IsNullOrEmpty(referer) || !normalizedOrigins.Any(origin => normalizedReferer.StartsWith(origin, StringComparison.OrdinalIgnoreCase)))
                {
                    context.Response.StatusCode = 401;
                    context.Response.ContentType = "text/plain";
                    await context.Response.WriteAsync("401 Unauthorized");
                    return;
                }
            }

            await _next(context);
        }
    }
}