﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.HolidayReminderLog;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class HolidayReminderLogController : ControllerBase
    {
        private readonly ILogger<HolidayReminderLogInfo> _logger;
        private readonly IHolidayReminderLogService _holidayReminderLogService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="holidayReminderLogService"></param>
        /// <param name="logger"></param>
        public HolidayReminderLogController(IHolidayReminderLogService holidayReminderLogService, ILogger<HolidayReminderLogInfo> logger)
        {
            _logger = logger;
            _holidayReminderLogService = holidayReminderLogService;
            
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(HolidayReminderLogAddOrUpdateDto req)
        {
            return await _holidayReminderLogService.AddHolidayReminderLog(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(HolidayReminderLogAddOrUpdateDto req)
        {
            return await _holidayReminderLogService.UpdateHolidayReminderLog(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _holidayReminderLogService.Delete(a => a.Id == id);

            return result > 0;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _holidayReminderLogService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<HolidayReminderLogDto> Get(long id)
        {
            return await _holidayReminderLogService.QueryInfo<HolidayReminderLogDto>(a => a.Id == id);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<HolidayReminderLogDto>> GetList([FromQuery] HolidayReminderLogListQueryDto req)
        {
            return await _holidayReminderLogService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<HolidayReminderLogDetailDto> Detail(long id)
        {
            return await _holidayReminderLogService.Detail(id);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<HolidayReminderLogDetailDto>> DetailList([FromQuery] HolidayReminderLogListQueryDto req)
        {
            return await _holidayReminderLogService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<HolidayReminderLogDetailDto>> Query([FromQuery] HolidayReminderLogPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<HolidayReminderLogDetailDto>> QueryPost(HolidayReminderLogPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<HolidayReminderLogDetailDto>> PageQuery([FromQuery] HolidayReminderLogPageQueryDto req)
        {
            return await _holidayReminderLogService.PageQueryView(req);
        }

    }
}