﻿<template>
	<div class="list-page-layout">
		<el-container style="height: 100%">
			<el-aside class="aside" style="width: 200px; background-color: #fff; overflow: hidden">
				<el-container style="height: 100%">
					<el-header style="background-color: #fff; padding: 10px; border-bottom: 1px solid #e6e6e6">
						<el-input v-model="filterTreeVal" filterable clearable></el-input>
					</el-header>
					<el-main class="nopadding">
						<el-scrollbar>
							<el-tree ref="treeRef" :data="orgTreeData" node-key="id"
								:props="{ value: 'orgId', label: 'name' }" :default-expand-all="false"
								:highlight-current="true" class="dep-tree" @node-click="onNodeClick"
								:filterNodeMethod="filterNode">
								<template #default="{ data }">
									<SvgIcon name="fa fa-sitemap ti" :size="12" v-if="data.orgType == 1"></SvgIcon>
									<SvgIcon name="fa fa-sitemap ti" :size="12" v-if="data.orgType == 2"></SvgIcon>
									<SvgIcon name="ele-User ti" :size="12" v-if="data.orgType == 3"></SvgIcon>
									<span>{{ data.name }}</span>
								</template>
							</el-tree>
						</el-scrollbar>
					</el-main>
				</el-container>
			</el-aside>
			<el-container style="margin-right: 10px">
				<template v-if="collapseUp == true">
					<el-header style="height: auto">
						<el-card shadow="never" style="width: 100%" :body-style="{ paddingTop: '10px' }">
							<div class="list-index-search">
								<el-form label-width="120px" style="padding-top: 10px; margin: 0px"
									@keyup.enter="onSearch">
									<el-row :gutter="1">
										<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="6" class="mb6">
											<el-form-item :label="$t('message.userFields.status')">
												<el-select placeholder="All" v-model="tableData.param.status" clearable
													class="w-20">
													<el-option :label="$t('message.userFields.Active')"
														value="0"></el-option>
													<el-option :label="$t('message.userFields.Inactive')"
														value="1"></el-option>
												</el-select>
											</el-form-item>
										</el-col>
										<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="6" class="mb6">
											<el-form-item :label="$t('message.userFields.userName')">
												<el-input v-model="tableData.param.userName" clearable
													class="w-20"></el-input>
											</el-form-item>
										</el-col>
										<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="6" class="mb6">
											<el-form-item :label="$t('message.userFields.email')">
												<el-input v-model="tableData.param.email" clearable
													class="w-20"></el-input>
											</el-form-item>
										</el-col>
										<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="6" class="mb6">
											<el-form-item :label="$t('message.userFields.realName')">
												<el-input v-model="tableData.param.realName" clearable
													class="w-20"></el-input>
											</el-form-item>
										</el-col>
										<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="6" class="mb6">
											<el-form-item :label="$t('message.userFields.lastName')">
												<el-input v-model="tableData.param.lastName" clearable
													class="w-20"></el-input>
											</el-form-item>
										</el-col>
										<el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6" class="mb6">
											<el-form-item :label="$t('message.userFields.userType')">
												<el-tree-select v-model="tableData.param.userTypeList"
													:data="userTypeData" multiple :render-after-expand="false"
													filterable clearable show-checkbox class="w-20 tag-select-input"
													:check-on-click-node="true" collapse-tags
													:placeholder="$t('message.page.selectKeyPlaceholder')" 
													@change="handleUserTypeCheckChange">
													<template #header>
														<el-checkbox v-model="userTypeCheckAll"
															@change="handleUserTypeCheckAll">
															All
														</el-checkbox>
													</template>
												</el-tree-select>
											</el-form-item>
										</el-col>
						
										<el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6" class="mb6">
											<el-form-item :label="$t('message.userFields.orgName')" >
												<el-tree-select v-model="tableData.param.companyId" class="w-20 tag-select-input"
													:placeholder="$t('message.page.selectKeyPlaceholder')" :data="orgData" node-key="orgId"
													:default-expand-all="true" checkStrictly clearable
													:props="{ value: 'orgId', label: 'name' }" filterable @clear="clearCompany">
													<template #default="{ data }">
														<SvgIcon name="fa fa-sitemap ti" :size="12"></SvgIcon>
														<span>{{ data.name }}</span>
													</template>
												</el-tree-select>
											</el-form-item>
										</el-col>


										<el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6" class="mb6">
											<el-form-item :label="$t('message.userFields.escalation')" >
												<el-select v-model="tableData.param.escalation" clearable filterable class="w-20 tag-select-input"
													:placeholder="$t('message.page.selectKeyPlaceholder')" >
													<el-option v-for="item in filteredUserBptsData" :key="parseInt(item.nodeId)" :label="item.label"
														:value="parseInt(item.nodeId)" />
												</el-select>
											</el-form-item>
										</el-col>

										<el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6" class="mb6">
											<el-form-item :label="$t('message.ticketLabels.createDate')" class="date-form-item">
												<div class="date-container">
													<MyDate v-model:input="tableData.param.startTime" style="width: 120px !important;" />
													<el-text>{{ $t('message.ticketLabels.to') }}</el-text>
													<MyDate v-model:input="tableData.param.endTime" style="width: 120px !important;"  />
												</div>
											</el-form-item>
										</el-col>
										<el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6" class="mb6">
											<el-form-item>
												<el-button size="small" type="primary" @click="onSearch"> {{
													$t('message.page.buttonSearch') }} </el-button>
												<el-button size="small" type="danger" @click="onSearchReSet"> {{
													$t('message.page.buttonReset') }} </el-button>
											</el-form-item>
										</el-col>
	
									</el-row>
								</el-form>
							</div>
						</el-card>
					</el-header>
				</template>

				<el-header class="table_header mt5" style="display: flex">
					<div class="left-panel">
						<el-button v-auth="'systemUser.Create'" size="small" type="primary" class="ml10" @click="onAdd">
							{{ $t('message.userButtons.buttonNew') }}
						</el-button>

						<el-button v-auth="'systemUser.BitchDelete'" type="danger" size="small"
							:disabled="tableData.selection.length == 0" @click="onDeleteByList">
							{{ $t('message.page.buttonDeleteBatch') }}
						</el-button>
					</div>
					<div class="right-panel">
						<el-dropdown v-auth="'systemUser.Export'">
							<el-button type="primary" size="small" class="ml10">
								<el-icon>
									<ele-ArrowDownBold />
								</el-icon>
								{{ $t('message.page.buttonExport') }}
							</el-button>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item @click="onExportAllRecord(0)">{{
										$t('message.page.buttonExportEntireList') }}</el-dropdown-item>
									<el-dropdown-item @click="onExportAllRecord(1)">{{
										$t('message.page.buttonExportSelectedRecords') }}</el-dropdown-item>
									<!-- <el-dropdown-item @click="onExportSelectRecord">{{ $t('message.page.buttonExportSelectedRecords') }}</el-dropdown-item> -->
								</el-dropdown-menu>
							</template>
						</el-dropdown>
					</div>
				</el-header>
				<el-main class="nopadding" style="margin-bottom: 10px; overflow: hidden">
					<div class="scTable" style="height: 100%" ref="scTableMain">
						<div class="scTable-table">
							<el-table ref="tableRef" :data="tableData.data" v-loading="tableData.loading"
								height="calc(100%)" @selection-change="selectionChange" highlight-current-row
								:row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }"  stripe border>
								<template #empty>
									<el-empty :description="$t('message.page.emptyDescription')"
										:image-size="100"></el-empty>
								</template>
								<el-table-column type="selection" />

								<el-table-column :label="$t('message.userFields.userName')" :min-width="120"
									show-overflow-tooltip>
									<template #default="{ row }">
										<span>{{ row.userName }}</span>
									</template>
								</el-table-column>

								<el-table-column :label="$t('message.userFields.fullName')" :min-width="120"
									show-overflow-tooltip>
									<template #default="{ row }">
										<span>{{ row.realName + ' ' + (row.lastName == null ? '' : row.lastName)
											}}</span>
									</template>
								</el-table-column>

								<el-table-column :label="$t('message.userFields.userType')" :min-width="120"
									show-overflow-tooltip>
									<template #default="{ row }">
										<span>{{ row.userType }}</span>
									</template>
								</el-table-column>
								<el-table-column :label="$t('message.userFields.escalation')" :min-width="200"
									show-overflow-tooltip>
									<template #default="{ row }">
										{{ row.escalationName }}
									</template>
								</el-table-column>

								<el-table-column :label="$t('message.userFields.email')" :min-width="140"
									show-overflow-tooltip>
									<template #default="{ row }">
										<span>{{ row.email }}</span>
									</template>
								</el-table-column>

								<el-table-column :label="$t('message.userFields.status')" :min-width="110"
									show-overflow-tooltip>
									<template #default="{ row }">
										<el-tag type="danger" v-if="row.status == '1'">{{
											$t('message.userFields.Inactive') }}</el-tag>
										<el-tag type="success" v-if="row.status == '0'">{{
											$t('message.userFields.Active') }}</el-tag>
									</template>
								</el-table-column>

								<el-table-column :label="$t('message.userFields.orgName')" :min-width="140"
									show-overflow-tooltip>
									<template #default="{ row }">
										<span>{{ row.orgName }}</span>
									</template>
								</el-table-column>
								<el-table-column :label="$t('message.userFields.createTime')" :min-width="200"
									show-overflow-tooltip>
									<template #default="{ row }">
										{{ row.createTime }}
									</template>
								</el-table-column>

								<el-table-column :label="$t('message.userFields.lockout')" :min-width="105"
									show-overflow-tooltip>
									<template #default="{ row }">
										{{ row.hasLock ? 'Locked' : '' }}
									</template>
								</el-table-column>

								<template v-if="tableData.param.orgType != 'Staff Member'">
									<el-table-column :label="$t('message.userFields.masterUser')" :min-width="120">
										<template #default="{ row }">
											<el-icon v-if="row.master_User === true" style="color: #67c23a">
												<ele-SuccessFilled />
											</el-icon>
											<el-icon v-if="row.master_User === false" style="color: red">
												<ele-CircleCloseFilled />
											</el-icon>
										</template>
									</el-table-column>
								</template>

								<el-table-column fixed="right" align="left" :label="$t('message.page.actions')"
									:min-width="160">
									<template #default="{ row }">
										<el-button v-if="0 > 1" size="mini" type="text" @click="onDetail(row)">{{
											$t('message.page.actionsView') }}</el-button>

										<el-row>
											<el-col :xs="24" :sm="24" :md="24" :lg="6">
												<el-button v-auth="'systemUser.Edit'" size="mini" type="text"
													@click="onEdit(row)">
													{{ $t('message.page.actionsEdit') }}
												</el-button>
											</el-col>
											<el-col :xs="24" :sm="24" :md="24" :lg="6">
												<el-button v-auth="'systemUser.Delete'" size="mini" type="text"
													style="color: #ff3a3a" @click="onDelete(row)">
													{{ $t('message.page.actionsDelete') }}
												</el-button>
											</el-col>
											<el-col :xs="24" :sm="24" :md="24" :lg="12"
												v-if="row.userType == 'Staff Member'">
												<el-button v-auth="'systemUser.SetDelegate'" size="mini" type="text"
													style="color: #67c23a" @click="onDelegate(row)">
													{{ $t('message.userButtons.Delegate') }}
												</el-button>
											</el-col>
										</el-row>
									</template>
								</el-table-column>
							</el-table>
						</div>
						<div class="scTable-page">
							<el-pagination @size-change="onSizechange" @current-change="onCurrentChange"
								:pager-count="5" :page-sizes="[15, 20, 30]"
								v-model:current-page="tableData.param.pageIndex" background
								v-model:page-size="tableData.param.pageSize"
								layout="total, sizes, prev, pager, next, jumper" :total="tableData.total" small>
							</el-pagination>
						</div>
					</div>
					<ResetPassword ref="resetPasswordRef" />
					<CreateOrEdit ref="createOrEditRef" @fetchData="onInit" />
					<delegateList ref="delegateRef" />

					<el-drawer v-model="infoDrawer" :title="$t('message.page.detailTitle')" :size="600"
						destroy-on-close>
						<Detail ref="detailRef" :info="detailObj"></Detail>
					</el-drawer>
				</el-main>
			</el-container>
		</el-container>
	</div>
</template>

<script lang="ts">
//import { useRouter } from 'vue-router';
import { nextTick, toRefs, reactive, onMounted, ref, defineComponent, watch,computed, getCurrentInstance } from 'vue';
import { ElMessageBox, ElMessage, FormInstance } from 'element-plus';
import { formatStrDate, formatDateTime } from '/@/utils/formatTime';
import print from '/@/utils/print.js';

import userApi from '/@/api/user/index';
import organizationApi from '/@/api/organization';

import Detail from './components/detail.vue';
import CreateOrEdit from './components/createOrEdit.vue';
import ResetPassword from './components/resetPassword.vue';
//import delegateList from '/@/views/bpts/userDelegate/index.vue';
import delegateList from '/@/views/bpts/userDelegate/indexDialog.vue';
import { useI18n } from 'vue-i18n';

import dictItemApi from '/@/api/dictItem/index';

import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import MyDate from '/@/components/ticket/ticketDate.vue';

export default defineComponent({
	name: 'systemUser',
	components: { Detail, CreateOrEdit, ResetPassword, delegateList,MyDate },
	data() {
		return {
			localData: this.tableData.data,
		};
	},
	setup() {
		const collapse = ref(true);
		const collapseUp = ref(true);
		const visible = ref(true);

		const treeRef = ref();
		const filterTreeVal = ref();

		watch(filterTreeVal, (val) => {
			if (val === '') {
				state.tableData.param.orgId = undefined;
				onInit();
			} else {
				treeRef.value!.filter(val);
			}
		});

		const { t } = useI18n();
		//const router = useRouter();
		const delegateRef = ref();
		const createOrEditRef = ref();
		const resetPasswordRef = ref();

		const changePwdRef = ref(false);
		const printMain = ref(null);
		const tableRef = ref();
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					status: '',
					email: '',
					userName: '',
					realName: '',
					lastName: '',
					orgType: 'Staff Member',
					orgId: undefined,
					pageIndex: 1,
					pageSize: 15,
					searchKey: '',
					order: 'id',
					sort: 'desc', // asc or desc
					ids: [],
					ischeckType: 0,
					userTypeList: [],
					companyId:undefined,
					startTime: '',
					endTime:'',
					escalation:null,
				},
			},
			orgTreeData: [],
			infoDrawer: false,
			detailObj: {},
			statusOpts: [],
			priorityData: [],
			countData: [],
			pickerDate: [],
			categoryOpts: [],
			customerOpts: [],
			userTypeData: [],
			userBptsData:[],
			orgData: [],
			userTypeCheckAll:false,
		});

		const displayDate = (val: string) => {
			let time_split_arr = val.split(' ');
			let timeVal = time_split_arr[0]; //+ '\\r\\n' + time_split_arr[1] + ' ' + time_split_arr[2]

			return timeVal;
		};

		const displayTime = (val: string) => {
			let time_split_arr = val.split(' ');
			let timeVal = time_split_arr[1] + ' ' + time_split_arr[2];

			return timeVal;
		};

		const InitOrgTree = () => {
			organizationApi.Tree().then((rs) => {
				state.orgTreeData = rs.data;
			});
		};

		//初始化
		const onInit = () => {
			//避免在过滤Tree后，选中又刷新页面
			if (filterTreeVal.value === '' || filterTreeVal.value == undefined) {
				// InitOrgTree();
			}

			organizationApi.Tree().then((rs) => {
					state.orgData = rs.data;
			});
			state.tableData.loading = true;
			let param=state.tableData.param;
			console.log("param:",param);
			userApi
				.Query(param)
				.then((rs) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));

			userApi.GetStaffMemberList().then((rs) => {
				state.userBptsData = rs.data;
			});



		};

		//搜索
		const onSearch = () => {
			onInit();
		};

		const onSearchReSet = () => {
			state.tableData.param.status = '';
			state.tableData.param.userName = '';
			state.tableData.param.lastName = '';
			state.tableData.param.realName = '';
			state.tableData.param.email = '';
			state.tableData.param.userTypeList = [];
			state.tableData.param.orgId = undefined;
			state.tableData.param.companyId=undefined;
			state.tableData.param.startTime='';
			state.tableData.param.endTime='';
			state.tableData.param.escalation = null;
			state.userTypeCheckAll = false;
			InitOrgTree();
			onInit();
		};

		const convertStatusVal = (val: any) => {
			if (val === 0) {
				return 'Active';
			} else {
				return 'InActive';
			}
		};

		const onExportAllRecord = (selectAll: number) => {
			let currPageSize = state.tableData.param.pageSize;
			state.tableData.param.pageSize = 10000;

			var checkSelection = state.tableData.selection;
			let ids_arr = [] as any;

			checkSelection.forEach(function (item, index) {
				let idjson = {} as any;
				idjson = item.id;
				ids_arr.push(idjson);
			});
			state.tableData.param.ischeckType = selectAll;
			if (selectAll == 1 && ids_arr.length == 0) {
				ElMessage.error(t('message.userFields.SelectTheDataYouWantToExport'));
				return;
			}
			if (selectAll == 0) {
				state.tableData.param.ids = [];
			} else {
				state.tableData.param.ids = ids_arr;
			}

			userApi
				.Export(state.tableData.param)
				.then((rs) => {
					downloadCallback(rs);
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));

			state.tableData.param.pageSize = currPageSize;
		};

		const downloadCallback = (rs) => {
			let data = rs;
			var newBlob = new Blob([data], { type: 'text/plain;charset=UTF-8' });
			var anchor = document.createElement('a');
			anchor.download = 'Users_' + formatDateTime() + '.xlsx';
			anchor.href = window.URL.createObjectURL(newBlob);
			anchor.click();
		};

		// 添加
		const onAdd = () => {
			createOrEditRef.value.openDialog({ action: 'Create' });
			//router.push('/user/Create');
		};

		// 修改
		const onEdit = (row: Object) => {
			createOrEditRef.value.openDialog({ action: 'Edit', id: row.id });
			//router.push('/user/edit/' + row.id);
		};

		//user Delegate
		const onDelegate = (row: object) => {
			delegateRef.value.openDialog({ action: 'Edit', id: row.id });
		};

		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				userApi
					.DeleteByKey(row.id)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((rs) => {
						nextTick(() => {
							ElMessageBox.alert(t('message.page.existCanNotDelete'), t('message.page.dlgTip'), {
								type: 'warning',
							});
						});
					});
			});
		};

		//批量删除
		const onDeleteByList = () => {
			ElMessageBox.confirm(
				t('message.page.dlgDeleteSelectText1') + ` ${state.tableData.selection.length} ` + t('message.page.dlgDeleteSelectText2'),
				t('message.page.dlgTip'),
				{
					type: 'warning',
					closeOnClickModal:false,
				}
			).then(() => {
				var items = state.tableData.selection.map((a) => {
					return a.id;
				});
				userApi.DeleteMany(items).then((rs) => {
					if (rs.data == 0) {
						ElMessage.error(rs.resultMsg);
						return;
					}
					onInit();
				});
			});
		};

		const onSelectMasterUser = (value: any, row: any) => {
			ElMessageBox.confirm(t('message.userFields.dlgMasterUser'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal:false,
			})
				.then(() => {
					var obj = { UserId: row.id, IsMasterUser: value };
					userApi.UpdateMasterUserType(obj).then((rs) => {
						if (rs.data == false) {
							ElMessage.error(rs.resultMsg);
							return;
						} else {
							ElMessage.success(t('message.page.saveSuccess'));
						}
						onInit();
					});
				})
				.catch((action: Action) => {
					if (action === 'cancel') {
						row.master_User = !value;
					}
				});
		};

		//详细信息
		const onDetail = (row: any) => {
			state.detailObj = {};

			userApi.Detail(row.id).then((rs) => {
				state.detailObj = rs.data;
				state.infoDrawer = true;
			});
		};

		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection;
		};

		//打印
		const onPrint = () => {
			print(printMain.value);
		};

		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};

		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			// if (state.tableData.param.pageIndex != val) {
			state.tableData.param.pageIndex = val;
			onInit();
			// }
		};

		const onNodeClick = (data, node) => {
			state.tableData.param.orgType = data.organizationType;
			state.tableData.param.orgId = data.orgId;
			onInit();
		};

		const filterNode = (value: string, data: Tree) => {
			if (!value) return true;
			return data.name.toLowerCase().includes(value.toLowerCase());
		};
		const filteredUserBptsData = computed(() => {
			return state.userBptsData.filter(item => parseInt(item.nodeId) !== 0);
		});

	//全选开始--------------------------------------

//返回所有节点值
const GetTreeAllNode = (nodes, allNodesIds) => {
			nodes.forEach(item => {
				allNodesIds.push(item.orgId);
				if (item.children && item.children.length) {
					GetTreeAllNode(item.children, allNodesIds);
				}
			});
		};

		const handleUserTypeCheckChange = (value: any) => {
			if (value.length == 0) {
				state.userTypeCheckAll = false;
			}
		};

		const handleUserTypeCheckAll = (value: boolean) => {
			if (value) {
				state.tableData.param.userTypeList = state.userTypeData.map((item: any) => item.value);
			}
			else {
				state.tableData.param.userTypeList = [];
			}
		};
		
		
//全选结束---------------------------------------
		// 页面加载时
		onMounted(() => {
			InitOrgTree();
			onInit();

			var dictArr = ['TicketStatus', 'UserType'];
			dictItemApi.Many(dictArr).then((rs: any) => {
				state.statusOpts = rs.data?.find((a: any) => {
					return a.dictValue === 'TicketStatus';
				})?.items;

				let userTypeArr = rs.data?.find((a: any) => {
					return a.dictValue === 'UserType';
				})?.items;

				for (let item of userTypeArr) {
					state.userTypeData.push({ value: item.itemValue, label: item.itemName });
				}
			});
		});

		return {
			filterTreeVal,
			treeRef,
			printMain,
			tableRef,
			formatStrDate,
			formatDateTime,
			onNodeClick,
			onPrint,
			createOrEditRef,
			resetPasswordRef,
			changePwdRef,
			delegateRef,
			selectionChange,
			onInit,
			onAdd,
			onEdit,
			onDetail,
			onDelete,
			onDeleteByList,
			onSizechange,
			onCurrentChange,
			onSearch,
			onSearchReSet,
			filteredUserBptsData,
			onExportAllRecord,
			onSelectMasterUser,
			filterNode,
			collapse,
			collapseUp,
			visible,
			displayDate,
			displayTime,
			...toRefs(state),
			isNotEmptyOrNull,
			onDelegate,
			handleUserTypeCheckChange,
			handleUserTypeCheckAll,
		};
	},
});
</script>

<style scoped>
.aside {
	margin: 5px;
	background-color: #fff;
}

.dep-tree {
	margin-top: 10px;
}
</style>
