<template>
	<div class="mailSettings-container">
		<el-dialog :title="title" v-model="isShowDialog" width="769px">
			<el-form ref="tableRulesRef" :model="tableData" size="small">
				<el-table ref="tableRef" :data="tableData.data" border class="module-table-uncollected" max-height="400" style="min-height: 400px">
					<el-table-column
						v-for="(item, index) in tableData.header"
						:key="index"
						show-overflow-tooltip
						:prop="item.prop"
						:width="item.width"
						:label="item.label"
					>
						<template v-slot:header>
							<span v-if="item.isRequired" class="color-danger">*</span>
							<span class="pl5">{{ item.label }}</span>
						</template>
						<template v-slot="scope">
							<el-form-item
								:prop="`data.${scope.$index}.${item.prop}`"
								:rules="[{ required: item.isRequired, message: '不能为空', trigger: `${item.type}` == 'input' ? 'blur' : 'change' }]"
							>
								<el-select
									ref="selectRef"
									v-if="item.type === 'select' && item.dsFromOption"
									v-model="scope.row[item.prop]"
									placeholder="请选择"
									:multiple="item.isMultiple"
									collapse-tags
									@focus="onVisible(item.prop, scope.$index)"
								>
									<el-option v-for="sel in item.option" :key="sel.value" :label="sel.label" :value="sel.value" />
								</el-select>

								<el-select
									ref="selectRef"
									v-if="item.type === 'select' && !item.dsFromOption"
									v-model="scope.row[item.prop]"
									placeholder="请选择"
									:multiple="item.isMultiple"
									collapse-tags
									@focus="onVisible(item.prop, scope.$index)"
								>
									<el-option v-for="sel in scope.row.sourceOption" :key="sel.id" :label="sel.name" :value="sel.id" />
								</el-select>

								<el-date-picker
									v-else-if="item.type === 'date'"
									v-model="scope.row[item.prop]"
									type="date"
									placeholder="选择日期"
									style="width: 100%"
								/>
								<el-checkbox v-else-if="item.type === 'checkbox'" v-model="scope.row[item.prop]" />
								<el-input v-else-if="item.type === 'input'" v-model="scope.row[item.prop]" placeholder="请输入内容" />
								<el-input v-else-if="item.type === 'dialog'" v-model="scope.row[item.prop]" readonly placeholder="请输入内容">
									<template v-slot:suffix>
										<i class="iconfont icon-shouye_dongtaihui" />
									</template>
								</el-input>
							</el-form-item>
						</template>
					</el-table-column>
					<el-table-column fixed="right" align="center" :label="$t('message.page.actions')" width="80">
						<template #default="{ row }">
							<el-button size="mini" type="text" style="color: #ff3a3a">{{ $t('message.page.actionsDelete') }}</el-button>
						</template>
					</el-table-column>
				</el-table>
			</el-form>

			<el-row class="flex mt15">
				<div class="flex-margin">
					<el-button size="small" type="primary" @click="onAddRow">新增一行1</el-button>
					<el-button size="small" type="success" @click="onValidate(tableRulesRef)">保存</el-button>
				</div>
			</el-row>
		</el-dialog>
		<ApproverDlg ref="approverDlgRef" @fetchData="fetchSelectData" />
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent, nextTick, ref } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';
import emailTemplateApi from '/@/api/emailTemplate/index';
import type { FormInstance } from 'element-plus';

import ApproverDlg from './ApproverDlg.vue';

interface DialogParams {
	action: string;
	vendorId: number;
}

interface FormModel {
	templateId: string;
	setType: string; //设置类型 TO：表示主接收人；CC:表示抄送
	source: string; //1：指定成员;2:发起人本人;3:部门主管;4:表单变量;5:调用接口;6:流程发起人;7:上一个节点的审批人;8:指定节点的相关人;9:流程的所有人
	sourceExtend: string; //拓展参数 如当 source=8时，{lastApprover:表示最后的审批人}
	sourceData: any[]; //用户id
	sourceOption: any[]; //用户id
}

export default defineComponent({
	name: 'MailSettings',
	components: { ApproverDlg },
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		// 定义变量内容
		const tableRulesRef = ref<FormInstance>();
		const tableRef = ref();
		const selectRef = ref();
		const approverDlgRef = ref();
		const state = reactive({
			title: 'Mail Settings',
			isShowDialog: false,
			saveLoading: false,
			deleteLoading: false,
			dialogParams: {
				action: '',
				id: -1,
				dataType: '',
			},
			ruleForm: {} as FormModel,
			rules: {
				vendorId: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},

			emailOptions: [] as any[], //邮件可选择的模板
			tableData: {
				data: [] as any[],
				header: [
					{
						prop: 'setType',
						width: '',
						label: '设置类型',
						isRequired: true,
						type: 'select',
						isMultiple: false,
						dsFromOption: true,
						option: [
							{ value: 'TO', label: '收件人' },
							{ value: 'CC', label: '抄送' },
						],
					},
					{
						prop: 'source',
						width: '200',
						label: '数据来源',
						isRequired: true,
						type: 'select',
						isMultiple: false,
						dsFromOption: true,
						option: [
							{ value: '1', label: '指定成员' },
							{ value: '2', label: '节点发起人' },
							{ value: '3', label: '节点审批人' },
							{ value: '4', label: '直系主管' },
							{ value: '5', label: '部门主管' },
							{ value: '6', label: '表单变量' },
							{ value: '7', label: '调用接口' },
							{ value: '8', label: '上一个节点的审批人' },
							{ value: '9', label: '指定节点的相关人' },
							{ value: '10', label: '流程的所有人' },
							{ value: '11', label: '自定义邮箱' },
						],
					},
					{
						prop: 'sourceData',
						width: '230',
						label: '设置数据',
						isRequired: false,
						type: 'select',
						isMultiple: true,
						dsFromOption: false,
						option: [
							{ value: '选项1', label: '黄金糕' },
							{ value: '选项2', label: '双皮奶' },
							{ value: '选项3', label: '蚵仔煎' },
						],
					},
					{
						prop: 'send',
						width: '60',
						label: '覆盖',
						isRequired: false,
						type: 'checkbox',
					},
				],

				option: [
					{ value: '选项1', label: '黄金糕' },
					{ value: '选项2', label: '双皮奶' },
					{ value: '选项3', label: '蚵仔煎' },
				],
			},
			rowIndex: -1,
			userOption: [],
		});

		const fetchSelectData = (dataType: any, items: any) => {
			state.tableData.data[state.rowIndex]['sourceOption'] = items;
			state.tableData.data[state.rowIndex]['sourceData'] = items.map((a) => {
				return a.id;
			});
		};

		const onVisible = (type: any, index: number) => {
			if (type === 'sourceData') {
				//使选择器的输入框失去焦点，并隐藏下拉框
				state.rowIndex = index;
				selectRef.value.forEach((el) => el.blur());
				approverDlgRef.value.setDialogParams({ multiple: true, dataType: 'User', initData: [] });
			}
		};

		const onSelect = () => {
			//approverDlgRef.value.setDialogParams({ multiple: true, dataType:'User', initData: [] });
		};

		// 表格验证
		const onValidate = (formEl: FormInstance | undefined) => {
			if (state.tableData.data.length <= 0) return ElMessage.warning('请先点击增加一行');
			if (!formEl) return;
			formEl.validate((valid) => {
				if (!valid) return ElMessage.warning('表格项必填未填');
				//ElMessage.success('全部验证通过');
				context.emit('fethData', state.dialogParams.dataType, state.tableData.data);
				closeDialog();
			});

			//console.log('state.tableData.data', state.tableData.data);
		};
		// 新增一行
		const onAddRow = () => {
			var item = {} as FormModel;
			state.tableData.data.push(item);
			tableRef.value.doLayout();
			nextTick(() => {
				tableRef.value.setScrollTop(1000000);
			});
		};
		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			state.dialogParams = parmas;

			onInitForm();

			getEmailOptions();
		};
		// 关闭弹窗
		const closeDialog = () => {
			state.isShowDialog = false;
		};

		const getEmailOptions = () => {
			emailTemplateApi.GetList({}).then((rs) => {
				state.emailOptions = rs.data;
			});
		};

		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			//console.log('state.tableData.data', state.tableData.data);
		};

		//删除纪录
		const onDelete = () => {
			ElMessageBox.confirm(`Are you sure to delete this?`, 'Tips', {
				confirmButtonText: 'OK',
				cancelButtonText: 'No,Thanks',
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				state.deleteLoading = true;
			});
		};
		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			onValidate,
			onAddRow,
			onVisible,
			tableRulesRef,
			tableRef,
			approverDlgRef,
			selectRef,
			fetchSelectData,
			onSelect,
			...toRefs(state),
		};
	},
});
</script>
