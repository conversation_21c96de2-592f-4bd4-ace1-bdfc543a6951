﻿<template>
	<el-main style="padding: 0 20px">
		<el-descriptions :column="1" border size="small">
			<el-descriptions-item label="id">{{ info.id }}</el-descriptions-item>
			<el-descriptions-item label="title">{{ info.title }}</el-descriptions-item>
			<el-descriptions-item label="instancesId">{{ info.instancesId }}</el-descriptions-item>
			<el-descriptions-item label="startTime">{{ info.startTime }}</el-descriptions-item>
			<el-descriptions-item label="approvalNode">{{ info.approvalNode }}</el-descriptions-item>
			<el-descriptions-item label="level">{{ info.level }}</el-descriptions-item>
			<el-descriptions-item label="status">{{ info.status }}</el-descriptions-item>
			<el-descriptions-item label="progress">{{ info.progress }}</el-descriptions-item>
		</el-descriptions>
	</el-main>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent } from 'vue';
import { formatStrDate } from '/@/utils/formatTime';

export default defineComponent({
	name: 'apiDetail',
	props: {
		info: Object,
	},
	setup(props) {
		const state = reactive({
			isShowDialog: false,
			obj: {},
		});
		// 页面加载时
		onMounted(() => {
			window.console.log('props', props.info);
		});
		return {
			...toRefs(state),
		};
	},
});
</script>


                        
        
        