﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>YunLanCrm</title>
    <script src="https://cdn.bootcss.com/jquery/1.10.2/jquery.min.js"></script>
    <style>
        div {
            margin: 10px;
            word-wrap: break-word;
        }
    </style>
    <script>

        $(document).ready(function () {
            $("#jsonp").click(function () {

                $.getJSON("http://localhost:9291/api/Login/jsonp?callBack=?", function (data) {
                    $("#data-jsonp").html("数据: " + data.value);
                });
            });

            $("#cors").click(function () {
                $.get("http://localhost:9291/api/Login/Token", function (data, status) {
                    console.log(data);
                    $("#status-cors").html("状态: " + status);
                    $("#data-cors").html("数据: " + data? data.token:"失败");
                });
            });

            $("#cors-post").click(function () {
                let postdata = {
                    "bID": 10,
                    "bsubmitter": "222",
                    "btitle": "33333",
                    "bcategory": "4444",
                    "bcontent": "5555",
                    "btraffic": 0,
                    "bcommentNum": 0,
                    "bUpdateTime": "2018-11-08T02:36:26.557Z",
                    "bCreateTime": "2018-11-08T02:36:26.557Z",
                    "bRemark": "string"
                };
                $.ajax({
                    type: 'post',
                    url: 'http://localhost:9291/api/Values',
                    contentType: 'application/json',
                    data: JSON.stringify(postdata),
                    success: function (data, status) {
                        console.log(data);
                        $("#status-cors-post").html("状态: " + status);
                        $("#data-cors-post").html("数据: " + JSON.stringify(data));
                    }
                });



                //$.ajax({
                //    type: "POST",
                //    url: "/api/Values",
                //    success: function (data, status) {
                //        console.log(data);
                //        $("#status-cors-post").html("状态: " + status);
                //        $("#data-cors-post").html("数据: " + data);
                //    }
                //});


            });

        });
    </script>
</head>
<body>

    <h3>通过JsonP实现跨域请求</h3>
    <button id="jsonp">发送一个 GET </button>

    <div id="status-jsonp"></div>
    <div id="data-jsonp"></div>
    <hr />

    <h3>添加请求头实现跨域</h3>
    无
    <hr />


    <h3>通过CORS实现跨域请求，另需要在服务器段配置CORE</h3>
    <button id="cors">发送一个 GET </button>

    <div id="status-cors"></div>
    <div id="data-cors"></div>
    <hr />
    <button id="cors-post">发送一个 POST </button>

    <div id="status-cors-post"></div>
    <div id="data-cors-post"></div>
    <hr />
</body>
</html>