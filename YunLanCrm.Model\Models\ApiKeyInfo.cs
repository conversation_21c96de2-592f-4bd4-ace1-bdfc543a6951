using System;
using SqlSugar;

namespace YunLanCrm.Model.Models
{
    /// <summary>
    /// API Key信息表
    /// </summary>
    [SugarTable("ApiKeyInfo")]
    public class ApiKeyInfo
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public long Id { get; set; }

        /// <summary>
        /// API Key名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string Name { get; set; }

        /// <summary>
        /// API Key值（加密存储）
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = false)]
        public string KeyValue { get; set; }

        /// <summary>
        /// 外部系统名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string ExternalSystemName { get; set; }

        /// <summary>
        /// 外部系统描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string Description { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 过期时间（可选）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// 允许的IP地址列表（JSON格式，可选）
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        public string AllowedIPs { get; set; }

        /// <summary>
        /// 允许访问的API端点列表（JSON格式，可选）
        /// </summary>
        [SugarColumn(Length = 2000, IsNullable = true)]
        public string AllowedEndpoints { get; set; }

        /// <summary>
        /// 创建者用户ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public long CreatedByUserId { get; set; }

        /// <summary>
        /// 最后使用时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? LastUsedTime { get; set; }

        /// <summary>
        /// 使用次数
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public long UsageCount { get; set; } = 0;
    }

    /// <summary>
    /// API Key使用日志表
    /// </summary>
    [SugarTable("ApiKeyUsageLog")]
    public class ApiKeyUsageLog
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public long Id { get; set; }

        /// <summary>
        /// API Key ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public long ApiKeyId { get; set; }

        /// <summary>
        /// 请求路径
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = false)]
        public string RequestPath { get; set; }

        /// <summary>
        /// HTTP方法
        /// </summary>
        [SugarColumn(Length = 10, IsNullable = false)]
        public string HttpMethod { get; set; }

        /// <summary>
        /// 客户端IP地址
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        public string ClientIP { get; set; }

        /// <summary>
        /// 用户代理
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string UserAgent { get; set; }

        /// <summary>
        /// 请求时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime RequestTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 响应状态码
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ResponseStatusCode { get; set; }

        /// <summary>
        /// 处理时间（毫秒）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? ProcessingTimeMs { get; set; }
    }
}
