﻿using Microsoft.AspNetCore.Mvc;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.User;
using YunLanCrm.Model.Dto.User;
using YunLanCrm.Common.Helper;
using YunLanCrm.Common.Extensions;
using MongoDB.Driver;
using YunLanCrm.Services;
using SkyWalking.NetworkProtocol.V3;
using Microsoft.AspNetCore.Authorization;
using YunLanCrm.Model;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class UserController : ControllerBase
    {
        private readonly ILogger<UserInfo> _logger;
        private readonly IUserService _userService;
        private readonly IEmailsService _emailsService;
        private readonly IConfigService _configService;
        private readonly IOnlineUserService _onlineUserService;
        private readonly ICmTicketsService _cmTicketService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userService"></param>
        /// <param name="emailsService"></param>
        /// <param name="configService"></param>
        /// <param name="onlineUserService"></param>
        /// <param name="logger"></param>
        /// <param name="cmTicketService"></param>
        public UserController(IUserService userService, IEmailsService emailsService, IConfigService configService, ILogger<UserInfo> logger, IOnlineUserService onlineUserService, ICmTicketsService cmTicketService)
        {
            _logger = logger;
            _userService = userService;
            _emailsService = emailsService;
            _configService = configService;
            _onlineUserService = onlineUserService;
            _cmTicketService = cmTicketService;
        }

        /// <summary>
        /// 获取 Approver 角色的用户
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        [HttpGet("GetApproverUsers")]
        public async Task<List<SelectUserDto>> GetApproverUsers(long? companyId)
        {
            return await _userService.GetApproverUsers(companyId);
        }

        [HttpGet("GetSelectUsers")]
        public async Task<List<SelectUserDto>> GetSelectUsers()
        {
            return await _userService.GetSelectUsers();
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(UserAddOrUpdateDto req)
        {
            return await _userService.AddUser(req);
        }

        /// <summary>
        /// 为外部系统提供的接口（使用API Key认证）
        /// </summary>
        [HttpPost("External/Add")]
        [Authorize(Policy = "ApiKeyPolicy")]
        public async Task<long> AddForExternal(UserAddOrUpdateDto req)
        {
            return await _userService.AddUser(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(UserAddOrUpdateDto req)
        {
            return await _userService.UpdateUser(req);
        }

        /// <summary>
        /// 为外部系统提供的接口（使用API Key认证）
        /// </summary>
        [HttpPost("External/Update")]
        [Authorize(Policy = "ApiKeyPolicy")]
        public async Task<bool> UpdateForExternal(UserAddOrUpdateDto req)
        {
            return await _userService.UpdateUser(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(int id)
        {
            //超级管理员不能删除
            if (id == Consts.SuperAdminUserId)
            {
                return false;
            }

            bool exists = await _cmTicketService.HasCurrenlyInUseByUserId(id);

            if (!exists)
            {
                int result = await _userService.Delete(a => a.Id == id);

                if (result > 0)
                {
                    await _onlineUserService.Delete(a => a.UserId == (long)id);
                }

                return result > 0;
            }
            else
            {
                throw new Exception("You're deleting a record that has been applied to a ticket,please click 'Ok' to cancel this action");
            }
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            var filteredItems = new List<int>();
            var id = Consts.SuperAdminUserId;
            foreach (var item in items)
            {
                var userId = item.ObjToInt();
                if (userId > 0 && userId != id)
                {
                    filteredItems.Add(userId);
                }
            }

            if (filteredItems.Count == 0)
            {
                return false;
            }

            int result = await _userService.Delete(a => filteredItems.Contains(a.Id));
            if (result > 0)
            {
                foreach (var item in filteredItems)
                {
                    await _onlineUserService.Delete(a => a.UserId == item);
                }
            }

            return result > 0;
        }

        [HttpPost("PermanentlyDelete/{id}")]
        public async Task<bool> PermanentlyDelete(int id)
        {
            bool result = await _userService.PermanentlyDelete(id);

            if (result)
            {
                await _onlineUserService.Delete(a => a.UserId == (long)id);
            }

            return result;
        }

        [HttpPost("Reserve/{id}")]
        public async Task<bool> Reserve(int id)
        {
            int result = await _userService.Reserve(a => a.Id == id);

            return result > 0;
        }

        [HttpPost("UserUnLock/{id}")]
        public async Task<bool> UserUnLock(int id)
        {
            var result = await _userService.UserUnLock(id);

            return result;
        }
        
        [HttpPost("MasterUserType")]
        public async Task<bool> UpdateMasterUserType([FromBody] UpdateMasterUserDto req)
        {
            bool result = await _userService.UpdateMasterUserType(req);

            return result;
        }  

        [HttpPost("AdminResetPassword")]
        public async Task<bool> AdminResetPassword(ResetPasswordDto req)
        {
            return await _userService.AdminResetPassword(req);
        }

        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<UserDto> Get(int id)
        {
            return await _userService.QueryInfo<UserDto>(a => a.Id == id);
        }
        [HttpPost("ChangePassword")]
        public async Task<bool> ChangePassword([FromBody] ChangePasswordDto req)
        {
            return await _userService.ChangePassword(req);
        }

        [HttpPost("UserResetPassword")]
        public async Task<bool> UserResetPassword(ResetPasswordDto req)
        {
            return await _userService.UserResetPassword(req);
        }
        [HttpPost("SetUserDefalutEmail")]
        public async Task<bool> SetUserDefalutEmail(UserDto req)
        {
            return await _userService.SetUserDefalutEmail(req);
        }
        [HttpPost("UpdateUserEmail")]
        public async Task<bool> UpdateUserEmail(UserDto req)
        {
            return await _userService.UpdateUserEmail(req);
        }
        [HttpPost("UpdateUserName")]
        public async Task<bool> UpdateUserName(UserDto req)
        {
            return await _userService.UpdateUserName(req);
        }
        [HttpPost("UpdateLanguage")]
        public async Task<bool> UpdateLanguage(UserDto req)
        {
            return await _userService.UpdateLanguage(req);
        }

        [HttpPost("ForgetEmail")]
        [AllowAnonymous]
        public async Task<bool> ForgetEmail(UserDto req)
        {
            return await _userService.ForgetEmail(_emailsService, _configService, req);
        }
        [HttpPost("SaveProfileChanges")]
        public async Task<bool> SaveProfileChanges([FromBody] SaveProfileChangesDto req)
        {
            return await _userService.SaveProfileChanges(req);
        }
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<UserDto>> GetList([FromQuery] UserListQueryDto req)
        {
            return await _userService.GetList(req);
        }

        /// <summary>
        /// 根据Category和Customer筛选出符合条件的用户和组
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("GetListByCategoryAndCustomer")]
        public async Task<UserGroupDto> GetListByCategoryAndCustomer([FromQuery] UserListQueryDto req)
        {
            return await _userService.GetListByCategoryAndCustomer(req);
        }

        /// <summary>
        /// 取出所有组下的用户
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("GetAllGroupUserList")]
        public async Task<UserGroupDto> GetAllGroupUserList([FromQuery] UserListQueryDto req)
        {
            return await _userService.GetAllGroupUserListV4(req);
        }

        /// <summary>
        /// 取出所有组下的用户
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("GetAllGroupUserListV2")]
        public async Task<UserGroupDto> GetAllGroupUserListV2(UserListQueryDto req)
        {
            return await _userService.GetAllGroupUserListV3(req);
        }

        [HttpPost("GetAllGroupUserListV3")]
        public async Task<UserGroupDto> GetAllGroupUserListV3(UserListQueryDto req)
        {
            return await _userService.GetAllGroupUserListV5(req);
        }
        [HttpPost("GetAllGroupUserListV5")]
        public async Task<UserGroupDto> GetAllGroupUserListV5(UserListQueryDto req)
        {
            return await _userService.GetAllGroupUserListV5(req);
        }
        /// <summary>
        /// 取出所有内部用户
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("GetStaffMemberList")]
        public async Task<List<UserNode>> GetStaffMemberList([FromQuery] UserListQueryDto req)
        {
            return await _userService.GetStaffMemberList(req);
        }
        
        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<UserDetailDto> Detail(int id)
        {
            return await _userService.Detail(id);
        }

        [HttpGet("GetDetailByEmail/{email}")]
        public async Task<UserDetailDto> GetDetailByEmail(string email)
        {
            return await _userService.GetDetailByEmail(email);
        }

        /// <summary>
        /// 为外部系统提供的用户详情查询接口（使用API Key认证）
        /// </summary>
        /// <param name="email">用户邮箱</param>
        /// <returns>用户详情</returns>
        [HttpGet("External/GetDetailByEmail/{email}")]
        [Authorize(Policy = "ApiKeyPolicy")]
        public async Task<UserDetailDto> GetDetailByEmailForExternal(string email)
        {
            return await _userService.GetDetailByEmail(email);
        }

        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<UserDetailDto>> DetailList([FromQuery] UserListQueryDto req)
        {
            return await _userService.DetailList(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        [NonAction]
        public async Task<PageQueryResult<UserDetailDto>> Query([FromQuery] UserPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<UserDetailDto>> QueryPost(UserPageQueryDto req)
        {
            return await PageQuery(req);
        }

        [HttpPost("Export")]
        public async Task<IActionResult> Export(UserPageQueryDto req)
        {
            var isAllCheck = req.ischeckType == 0 ? true : false;
            return await ExcelHelper.Export<Task<PageQueryResult<UserDetailDto>>>(_userService, typeof(IUserService).Name, "PageQueryView", this, isAllCheck, req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<UserDetailDto>> PageQuery([FromQuery] UserPageQueryDto req)
        {
            return await _userService.PageQueryView(req);
        }
    }
}





