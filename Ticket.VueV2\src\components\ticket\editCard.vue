<template>
	<el-form label-position="top" label-width="100px" :model="formModel">
		<el-row>
			<el-col>
				<el-form-item label="Ticket Number">
					<el-input v-model="formModel.ticket_Number" />
				</el-form-item>
			</el-col>
		</el-row>
		<el-row>
			<el-col :span="12">
				<el-form-item label="Category">
					<el-select v-model="formModel.ticket_Category" placeholder="Category">
						<el-option label="BIZD - Business" value="0" />
						<el-option label="CBD - Anything" value="1" />
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span="10" :offset="2">
				<el-form-item label="Priority">
					<el-select v-model="formModel.ticket_Priority" placeholder="Priority">
						<el-option label="Normal" value="0" />
						<el-option label="Low" value="1" />
					</el-select>
				</el-form-item>
			</el-col>
		</el-row>
		<el-row>
			<el-col>
				<el-form-item label="First Name">
					<el-input v-model="formModel.firstName" />
				</el-form-item>
			</el-col>
		</el-row>
		<el-row>
			<el-col>
				<el-form-item label="Last Name">
					<el-input v-model="formModel.lastName" />
				</el-form-item>
			</el-col>
		</el-row>
		<el-row>
			<el-col>
				<el-form-item label="ticket_Customer">
					<el-input v-model="formModel.customer" />
				</el-form-item>
			</el-col>
		</el-row>
		<el-row>
			<el-col>
				<el-form-item label="Customer Email">
					<el-input v-model="formModel.email" />
				</el-form-item>
			</el-col>
		</el-row>
		<el-row>
			<el-col>
				<el-form-item label="Telephone">
					<el-input v-model="formModel.telephone" />
				</el-form-item>
			</el-col>
		</el-row>
		<el-row>
			<el-col>
				<el-form-item label="Assignee">
					<el-select v-model="formModel.assignee" style="width: 100%">
						<el-option label="0" value="0" />
						<el-option label="1" value="1" />
						<el-option label="2" value="2" />
						<el-option label="3" value="3" />
						<el-option label="4" value="4" />
						<el-option label="5" value="5" />
						<el-option label="6" value="6" />
						<el-option label="7" value="7" />
						<el-option label="8" value="8" />
					</el-select>
				</el-form-item>
			</el-col>
		</el-row>
		<el-row>
			<el-col :span="12">
				<el-form-item label="Open Date">
					<el-date-picker v-model="formModel.openDate" type="date" placeholder="Open Date" clearable />
				</el-form-item>
			</el-col>
			<el-col :span="10" :offset="2">
				<el-form-item label="Close Date">
					<el-date-picker v-model="formModel.closeDate" type="date" placeholder="Close Date" clearable />
				</el-form-item>
			</el-col>
		</el-row>
	</el-form>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
	props: ['formData'],
	data() {
		return { formModel: this.formData };
	},
	setup(props, context) {
		return {};
	},
});
</script>
<style scoped>
.cardContainer {
	margin-top: 5px;
	margin-bottom: 5px;
	align-items: center;
	justify-content: center;
	border: 1px solid lightgrey;
	border-radius: 4px;
}
</style>
