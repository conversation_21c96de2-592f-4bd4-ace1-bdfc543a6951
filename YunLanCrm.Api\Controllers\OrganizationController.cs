﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Furion.DatabaseAccessor;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.Organization;
using YunLanCrm.Common.HttpContextUser;
using YunLanCrm.Services;
using YunLanCrm.Model.Dto.User;
using YunLanCrm.IRepositories;
using SqlSugar;
using YunLanCrm.Common.Helper;
using YunLanCrm.Dto.User;
using Org.BouncyCastle.Ocsp;
using YunLanCrm.Common.WebApiClients.HttpApis;
using NPOI.POIFS.Properties;
using System.Data;
using NetBox.Extensions;
using YunLanCrm.Dto.CmUserCompany;
using YunLanCrm.Model.Views;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class OrganizationController : ControllerBase
    {
        private readonly ILogger<OrganizationInfo> _logger;

        private readonly IOrganizationService _organizationService;
        private readonly ICompanyService _companyService;
        private readonly IDepartmentService _departmentService;
        private readonly IUserOrgService _userOrgService;
        private readonly ICmUserCompanyService _cmUserCompanyService;
        private readonly IUser CurrentUser;
        private readonly ICmUserCompanyService _userCompanyService;
        private readonly IUserService _userService;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly ICmTicketsService _cmTicketsService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="organizationService"></param>
        /// <param name="companyService"></param>
        /// <param name="departmentService"></param>
        /// <param name="logger"></param>
        public OrganizationController(IOrganizationService organizationService, ICompanyService companyService
                                    , IDepartmentService departmentService, ILogger<OrganizationInfo> logger
                                    , IUserOrgService userOrgService, IUser currentUser
                                    , ICmUserCompanyService cmUserCompanyService, ICmUserCompanyService userCompanyService, IOrganizationRepository organizationRepository
                                    , IUserService userService, ICmTicketsService cmTicketsService)
        {
            this._organizationService = organizationService;
            this._logger = logger;
            _userOrgService = userOrgService;
            CurrentUser = currentUser;
            _cmUserCompanyService = cmUserCompanyService;
            this._companyService = companyService;
            this._departmentService = departmentService;
            _userCompanyService = userCompanyService;
            _organizationRepository = organizationRepository;
            _userService = userService;
            _cmTicketsService = cmTicketsService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        [HttpGet("Tree")]
        //public async Task<List<OrganizationTreeDto>> Tree(long? parentId)
        public async Task<List<OrganizationTreeDto>> Tree([FromQuery] OrganizationTreeDto req)
        {
            req.OrganizationType = !string.IsNullOrEmpty(req.IncludeProject)
                ? string.Empty
                : Consts.OrganizationType_Project;

            List<OrganizationTreeDto> organizationList = null;

            //处理查询子级的时候，无法带出查询数据
            if (!string.IsNullOrEmpty(req.ListSearch))
            {
                OrganizationTreeDto reqSearch = new OrganizationTreeDto();

                reqSearch.OrganizationType = !string.IsNullOrEmpty(req.IncludeProject)
                   ? string.Empty
                   : Consts.OrganizationType_Project;


                //string searchName = req.Name;

                //查询出所有organization表的数据
                //req.Name = null;

                //organizationList = await _organizationService.GetOrganizationList(req);

                //根据Name过滤出相应的Org数据
                //var searchOrgList = organizationList.Where(a => a.Name.Contains(req.Name)).ToList();

                int status = 0;

                if (!string.IsNullOrEmpty(req.StatusSearch))
                {
                    status = Convert.ToInt32(req.StatusSearch);
                }

                DateTime createAtStart = DateTime.MinValue;

                if (req.createAtStart.HasValue)
                {
                    createAtStart = DateTime.Parse(req.createAtStart.Value.ToString("yyyyy-MM-dd"));
                }

                DateTime createAtEnd = DateTime.MinValue;

                if (req.createAtEnd.HasValue)
                {
                    createAtEnd = DateTime.Parse(req.createAtEnd.Value.ToString("yyyy-MM-dd") + " 23:59:59.000");
                }

                //查询出所有organization表的数据//查询出所有organization表的数据
                organizationList = await _organizationRepository.Db.Queryable<OrganizationInfo>()
                    .Where(a => a.IsDeleted == false)
                    //.WhereIF(!string.IsNullOrEmpty((req.StatusSearch)), a => a.Status == status)
                    .WhereIF(!string.IsNullOrEmpty((req.OrganizationType)), a => a.OrganizationType != req.OrganizationType)
                    .WhereIF(createAtStart != DateTime.MinValue && createAtEnd != DateTime.MinValue, a => a.CreateAt >= createAtStart && a.CreateAt <= createAtEnd)
                    .WhereIF(createAtStart != DateTime.MinValue && createAtEnd == DateTime.MinValue, a => a.CreateAt >= createAtStart)
                    .WhereIF(createAtStart == DateTime.MinValue && createAtEnd != DateTime.MinValue, a => a.CreateAt <= createAtEnd)
                    .Select(a => new OrganizationTreeDto { })
                    .ToListAsync();


                //根据Name过滤出相应的Org数据
                var searchOrgList = await _organizationService.GetOrganizationList(req);



                var parentIds = searchOrgList
                                .Where(x => x.ParentId > 0)
                                .Select(x => x.ParentId.ToString())
                                .Distinct()
                                .ToList();

                var orgIds = searchOrgList
                               .Where(x => x.OrgId > 0)
                               .Select(x => x.OrgId.ToString())
                               .Distinct()
                               .ToList();

                var diffParentIds = parentIds.Except(orgIds).ToList();

                if (diffParentIds.Count > 0)
                {
                    var allFatherIds = new List<string>();

                    for (int i = 0; i < diffParentIds.Count; i++)
                    {
                        var fatherIds = _organizationService.GetFatherList(organizationList, Convert.ToInt64(diffParentIds[i]));

                        var fatherOrgIds = fatherIds
                            .Where(x => x.OrgId > 0)
                            .Select(x => x.OrgId.ToString())
                            .Distinct()
                            .ToList();

                        allFatherIds.AddRange(fatherOrgIds);
                    }

                    diffParentIds = diffParentIds
                    .Concat(allFatherIds)
                    .Distinct()
                    .ToList();

                    diffParentIds = diffParentIds.Except(orgIds).ToList();

                    //req.ids = diffParentIds;

                    reqSearch.ids = diffParentIds;

                    var parentOrgList = await _organizationService.GetOrganizationList(reqSearch);

                    organizationList = searchOrgList.Concat(parentOrgList).ToList();
                }
                else
                {
                    //req.Name = searchName;

                    organizationList = await _organizationService.GetOrganizationList(req);
                }
            }
            else
            {
                organizationList = await _organizationService.GetOrganizationList(req);
            }

            var parentId = req.IncludeOrgId ?? 0;
            var isFirst = req.IncludeOrgId.HasValue ? true : false;

            var companyList = await _organizationRepository.Db.Queryable<CompanyInfo>()
                .OrderBy(a => a.Name)
                .ToListAsync();

            var orgResultList =
                _organizationService.ParseTree(organizationList, parentId, isFirst, null, req.editCompanyId,
                    companyList);

            return orgResultList;
        }

        [HttpPost("TreePost")]
        public async Task<List<OrganizationTreeDto>> TreePost(OrganizationTreeDto req)
        {
            var organizationList = await _organizationService.GetOrganizationList(new OrganizationTreeDto
            {
                OrganizationType = !string.IsNullOrEmpty(req.IncludeProject) ? string.Empty : Consts.OrganizationType_Project
            });

            var includeParentId = _organizationService.GetIncludeParentId(req, organizationList);

            var parentId = req.IncludeOrgId ?? 0;
            var isFirst = req.IncludeOrgId.HasValue ? true : false;

            var companyList = _organizationRepository.Db.Queryable<CompanyInfo>().ToList();

            return _organizationService.ParseTree(organizationList, parentId, isFirst, includeParentId, 0, companyList);
        }

        [HttpGet("UserTree")]
        public async Task<List<OrganizationUserTreeDto>> UserTree(long? parentId)
        {
            var where = PredicateBuilder.New<OrganizationInfo>(true);
            if (parentId.HasValue)
            {
                // where.And(a => a.ParentId == parentId.Value);
            }
            var orderBy = new OrderBy<OrganizationInfo>(a => a.Sort, SortDirection.Ascending);
            var data = await _organizationService.QueryList<OrganizationTreeDto>(where, orderBy);

            var treeData = new List<OrganizationUserTreeDto>();
            foreach (var item in data)
            {
                treeData.Add(new OrganizationUserTreeDto()
                {
                    Id = Guid.NewGuid(),
                    OrgId = item.OrgId.Value,
                    ItemName = item.Name,
                    ItemCode = item.Code,
                    ItemId = item.OrgId.Value,
                    ItemType = item.OrgType.Value,
                    ParentId = item.ParentId.Value,
                });
            }

            return _organizationService.ParseUserTree(treeData, 0);
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(OrganizationAddDto req)
        {
            if ((await _organizationService.AnyAsync(a => a.Name == req.Name && a.ParentId == req.ParentId)))
            {
                throw new Exception("Name already exists");
            }

            if (!string.IsNullOrWhiteSpace(req.Code))
            {
                if ((await _organizationService.AnyAsync(a => a.Code == req.Code)))
                {
                    throw new Exception("Code already exists");
                }
            }

            return await _organizationService.AddIdentity(req.Adapt<OrganizationInfo>());
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(OrganizationEditDto req)
        {
            return await _organizationService.Update(req.Adapt<OrganizationInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="orgId">orgId</param>
        /// <returns></returns>
        [HttpPost("Delete/{orgId}")]
        public async Task<bool> Delete(long orgId)
        {
            var exists = await _cmTicketsService.HasCurrenlyInUseByCompanyId(orgId);

            if (!exists)
            {
                var organizationList = await _organizationService.GetOrganizationList(new OrganizationTreeDto { });
                var allOrgIds = _organizationService.GetAllOrgIds(orgId, organizationList);

                var org = await _organizationService.QueryInfo(a => a.OrgId == orgId);

                int result = await _organizationService.Delete(a => a.OrgId == orgId);

                if (result > 0)
                {
                    await _organizationService.Delete(a => allOrgIds.Contains(a.OrgId));

                    if (org.OrgType == 1)
                    {
                        await _companyService.Delete(a => a.OrgId == org.OrgId);
                        await _companyService.Delete(a => allOrgIds.Contains(a.OrgId));
                    }
                }

                return result > 0;
            }
            else
            {
                throw new Exception("You're deleting a record that has been applied to a ticket or user,please click 'Ok' to cancel this action");
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        public async Task<bool> DeleteMany(object[] items)
        {
            foreach (var item in items)
            {
                await Delete(long.Parse(item.ToString()));
            }

            return true;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        [HttpGet("Get/{orgId}")]
        public async Task<OrganizationDto> Get(long orgId)
        {
            return await _organizationService.QueryInfo<OrganizationDto>(a => a.OrgId == orgId);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        [HttpGet("Detail/{orgId}")]
        public async Task<OrganizationDetailDto> Detail(long orgId)
        {
            var obj = await _organizationService.QueryInfo(a => a.OrgId == orgId);

            if (obj != null)
            {
                return _organizationService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<OrganizationListDto>> List([FromQuery] OrganizationListQueryDto req)
        {
            var list = new List<OrganizationListDto>();
            var where = PredicateBuilder.New<OrganizationInfo>(true);
            var orderBy = new OrderBy<OrganizationInfo>(req.order, req.sort);
            var data = await _organizationService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _organizationService.Join(item);
                list.Add(detail.Adapt<OrganizationListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("GetEndUserOrganization")]
        public async Task<List<OrganizationListDto>> GetEndUserOrganizationList([FromQuery] OrganizationListQueryDto req)
        {
            var list = new List<OrganizationListDto>();
            var where = PredicateBuilder.New<OrganizationInfo>(true);

            //where.And(a => a.OrganizationType == Consts.UserType_EndUser);

            var userOrgObj = await _userOrgService.QueryList<UserOrgInfo>(a => a.UserId == CurrentUser.UserId);

            var userOrgList = new List<long>();
            var orgList = new List<string>();

            foreach (var info in userOrgObj)
            {
                userOrgList.Add(info.OrgId);
            }

            var orgObj = await _organizationService.QueryList<OrganizationInfo>(a => userOrgList.Contains(a.OrgId));

            foreach (var info in orgObj)
            {
                orgList.Add(info.Name);
            }

            if (!CurrentUser.RoleIds.Contains(4))
            {
                where.And(a => orgList.Contains(a.Name));
            }

            var orderBy = new OrderBy<OrganizationInfo>(req.order, req.sort);
            var data = await _organizationService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法

            foreach (var item in data)
            {
                var detail = _organizationService.Join(item);
                list.Add(detail.Adapt<OrganizationListDto>());
            }

            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("GetCompanyProjectTree")]
        public async Task<TreeNodeDto> GetCompanyProjectTree(OrganizationListQueryDto req)
        {
            //将查询的组数据封装成子树结构返回到页面
            TreeNodeDto treeNodeDto = new TreeNodeDto();

            List<TreeNode> treeNodeList = treeNodeDto.TreeNodeList;

            var companyList = await _organizationRepository.Db.Queryable<CompanyInfo>()
                .Where(a => a.Status == 0)
                .WhereIF(req.orgId != null && req.orgId.Count > 0, a => req.orgId.Contains(a.OrgId))
                .OrderBy(a => a.Name)
                .ToListAsync();

            var projectList = await _organizationRepository.Db.Queryable<CmProjectsInfo>()
                .Where(a => a.IsDeleted == false && a.Project_Status == false)
                .OrderBy(a => a.Project_Name)
                .ToListAsync();

            var companyProjectList = await _organizationRepository.Db.Queryable<CmProjectCompanyInfo>()
                .Where(a => a.IsDeleted == false).ToListAsync();

            foreach (var company in companyList)
            {
                TreeNode companyNode = new TreeNode
                {
                    value = SqlFunc.ToString(company.OrgId),
                    nodeId = SqlFunc.ToString(company.OrgId),
                    label = company.Name,
                    nodeType = "CompanyNode"
                };

                var companyProjectInfo = companyProjectList.Where(a => a.OrgId == company.OrgId).ToList();

                foreach (var projectCompany in companyProjectInfo)
                {
                    var projectInfo = projectList.Where(a => a.Id == projectCompany.cmProjectId).ToList();

                    foreach (var project in projectInfo)
                    {
                        TreeNode projectNode = new TreeNode
                        {
                            value = SqlFunc.ToString(company.OrgId) + "@@@" + SqlFunc.ToString(project.Id),
                            nodeId = SqlFunc.ToString(project.Id),
                            label = project.Project_Name,
                            nodeType = "ProjectNode"
                        };

                        companyNode.children.Add(projectNode);
                    }
                }

                treeNodeList.Add(companyNode);
            }

            return treeNodeDto;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("GetCompanyProjectTreeV2")]
        public async Task<TreeNodeDto> GetCompanyProjectTreeV2(OrganizationListQueryDto req)
        {
            //将查询的组数据封装成子树结构返回到页面
            TreeNodeDto treeNodeDto = new TreeNodeDto();

            var companyList = await _organizationRepository.Db.Queryable<CompanyInfo>()
               .Where(a => a.Status == 0)
               .WhereIF(req.orgId != null && req.orgId.Count > 0, a => req.orgId.Contains(a.OrgId))
               .OrderBy(a => a.Name)
               .ToListAsync();

            var projectList = await _organizationRepository.Db.Queryable<CmProjectsInfo>()
                .Where(a => a.Project_Status == false && a.IsDeleted == false)
                .OrderBy(a => a.Project_Name)
                .ToListAsync();

            var organizationList = await _organizationRepository.Db.Queryable<OrganizationInfo>()
                .Where(a => a.Status == 0 && a.IsDeleted == false)
                .OrderBy(a => a.Name)
                .ToListAsync();

            treeNodeDto.TreeNodeList = ParseAllCustomerTree(companyList, projectList, organizationList, 0);

            return treeNodeDto;
        }

        [HttpPost("GetCompanyProjectTreeV3")]
        public async Task<TreeNodeDto> GetCompanyProjectTreeV3(OrganizationListQueryDto req)
        {
            //将查询的组数据封装成子树结构返回到页面
            TreeNodeDto treeNodeDto = new TreeNodeDto();

            var companyList = await _organizationRepository.Db.Queryable<CompanyInfo>()
               .Where(a => a.Status == 0)
               .WhereIF(req.orgId != null && req.orgId.Count > 0, a => req.orgId.Contains(a.OrgId))
               .OrderBy(a => a.Name)
               .ToListAsync();

            var projectList = await _organizationRepository.Db.Queryable<CmProjectsInfo>()
                .Where(a => a.Project_Status == false && a.IsDeleted == false)
                .OrderBy(a => a.Project_Name)
                .ToListAsync();

            var organizationList = await _organizationRepository.Db.Queryable<OrganizationInfo>()
                .Where(a => a.Status == 0 && a.IsDeleted == false)
                .OrderBy(a => a.Name)
                .ToListAsync();

            treeNodeDto.TreeNodeList = ParseAllCustomerTreeV2(companyList, projectList, organizationList, 0);

            return treeNodeDto;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("GetEndUserOrganizationByUserId")]
        public async Task<List<OrganizationListDto>> GetEndUserOrganizationByUserId([FromQuery] OrganizationListQueryDto req)
        {
            var list = new List<OrganizationListDto>();
            var where = PredicateBuilder.New<OrganizationInfo>(true);

            //where.And(a => a.OrganizationType == Consts.UserType_EndUser);

            var whereUserOrg = PredicateBuilder.New<CmUserCompanyInfo>(a => a.userId == CurrentUser.UserId);

            var userOrg = _cmUserCompanyService.QueryList(whereUserOrg).Result;

            var orderBy = new OrderBy<OrganizationInfo>(req.order, req.sort);
            var data = await _organizationService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data.Where(a => userOrg.Select(u => u.CompanyId).Contains(a.OrgId)))
            {
                var detail = _organizationService.Join(item);
                list.Add(detail.Adapt<OrganizationListDto>());
            }
            #endregion

            return list;
        }

        [HttpPost("GetCompanyProjectTreeByUserId")]
        public async Task<TreeNodeDto> GetCompanyProjectTreeByUserId(OrganizationListQueryDto req)
        {
            //将查询的组数据封装成子树结构返回到页面
            TreeNodeDto treeNodeDto = new TreeNodeDto();

            List<TreeNode> treeNodeList = treeNodeDto.TreeNodeList;

            //获取所有公司信息
            var companyList = await _organizationRepository.Db.Queryable<CompanyInfo>()
                .Where(a => a.Status == 0)
                .ToListAsync();

            //获取用户所分配的project
            var userCompanyList = await _organizationRepository.Db.Queryable<CmUserCompanyInfo>()
                .Where((a) => a.IsDeleted == false && a.userId == CurrentUser.UserId && a.CheckType != "BackEnd Insert")
                .Select((a) => new CmUserCompanyInfo { })
                .ToListAsync();

            //获取所有project
            var projectList = await _organizationRepository.Db.Queryable<CmProjectsInfo>()
                .Where(a => a.IsDeleted == false && a.Project_Status == false).ToListAsync();

            //获取project所关联的公司
            var companyProjectList = await _organizationRepository.Db.Queryable<CmProjectCompanyInfo>()
                .Where(a => a.IsDeleted == false).ToListAsync();

            //根据用户所分配的公司，过滤companyList这个列表的数据
            foreach (var company in companyList.Where(a => userCompanyList.Select(u => u.CompanyId).Contains(a.OrgId)))
            {
                TreeNode companyNode = new TreeNode
                {
                    value = SqlFunc.ToString(company.OrgId),
                    nodeId = SqlFunc.ToString(company.OrgId),
                    label = company.Name,
                    nodeType = "CompanyNode"
                };

                //先根据循环的company，过滤companyProjectList这个列表的数据
                //再根据用户所分配的公司某些project，过滤companyProjectList这个列表的数据
                var getProjectData = userCompanyList.Where(a => a.cmProjectId > 0 && a.CompanyId == company.OrgId)
                    .Select(a => a.cmProjectId).ToList();

                var companyProjectInfo = companyProjectList
                    .Where(a => a.OrgId == company.OrgId)
                    .WhereIF(getProjectData.Count > 0, a => getProjectData.Contains(a.cmProjectId))
                    .ToList();

                //如果用户所分配的project和公司下的project数量不是一致的话，则要禁用父节点
                if (getProjectData.Count > 0)
                {
                    companyNode.disabled = true;
                }

                foreach (var projectCompany in companyProjectInfo)
                {
                    var projectInfo = projectList.Where(a => a.Id == projectCompany.cmProjectId).ToList();

                    foreach (var project in projectInfo)
                    {
                        TreeNode projectNode = new TreeNode
                        {
                            value = SqlFunc.ToString(company.OrgId) + "@@@" + SqlFunc.ToString(project.Id),
                            nodeId = SqlFunc.ToString(project.Id),
                            label = project.Project_Name + " (" + company.Name + ")",
                            nodeType = "ProjectNode"
                        };

                        companyNode.children.Add(projectNode);
                    }
                }

                treeNodeList.Add(companyNode);
            }

            return treeNodeDto;
        }

        [HttpPost("GetCompanyProjectTreeByUserIdV2")]
        public async Task<TreeNodeDto> GetCompanyProjectTreeByUserIdV2(OrganizationListQueryDto req)
        {
            //将查询的组数据封装成子树结构返回到页面
            TreeNodeDto treeNodeDto = new TreeNodeDto();

            int userId = req.UserTrusteeId > 0 ? req.UserTrusteeId : CurrentUser.UserId;

            //获取工单的指定人
            if (req.TicketId.HasValue && req.TicketId > 0)
            {
                var ticketInfo = await _organizationRepository.Db.Queryable<CmTicketsView>()
                    .Where(a => a.IsDeleted == false && a.Id == req.TicketId)
                    .Select(a => new { a.AssigToType, a.AssigToId })
                    .ToListAsync();

                if (ticketInfo.Count > 0)
                {
                    string assignToType = ticketInfo[0].AssigToType;
                    long assignToId = ticketInfo[0].AssigToId;

                    if (assignToType == Consts.AssigneeType_Users)
                    {
                        userId = SqlFunc.ToInt32(assignToId);
                    }
                    else if (assignToType == Consts.AssigneeType_Groups)
                    {

                    }
                }
            }

            //获取用户基本信息
            var userObj = await _userService.QueryById(userId);

            var organizationList = await _organizationRepository.Db.Queryable<OrganizationInfo>()
               .Where(a => a.Status == 0 && a.IsDeleted == false)
               .Select(a => new OrganizationInfo { OrgId = a.OrgId, OrgType = a.OrgType, Name = a.Name, ParentId = a.ParentId, BusinessUnit = a.BusinessUnit })
               .OrderBy(a => a.Name)
               .ToListAsync();

            //var data = await _organizationService.GetOrganizationList(new OrganizationTreeDto { });

            var data = organizationList.Adapt<List<OrganizationTreeDto>>();

            var userCompanyList = await _userCompanyService.GetListByUserId(new CmUserCompanyListQueryDto
            {
                userId = userId
            });

            //找出需要加载到树形中的project数据
            List<long> projectId = userCompanyList.Where(a => a.CheckType == "Project").Select(a => a.cmProjectId)
                .ToList();

            //找出要禁用的节点
            List<long> disableParentId = new List<long>();

            //找出需要加载到树形里面的节点
            List<long> topParentId = new List<long>();

            var companyList = await _organizationRepository.Db.Queryable<CompanyInfo>()
              .Where(a => a.Status == 0 && a.IsDeleted == false)
              .Select(a => new CompanyInfo { CompanyId = a.CompanyId, OrgId = a.OrgId, Code = a.Code, Status = a.Status, Name = a.Name })
              .OrderBy(a => a.Code)
              .ToListAsync();

            _organizationService.GetDisableAndLoadParentId(userCompanyList, data, topParentId, disableParentId,
                projectId, req.MasterUserTypeFlag, userObj, companyList);

            var projectList = await _organizationRepository.Db.Queryable<CmProjectsInfo>()
                .Where(a => a.Project_Status == false && a.IsDeleted == false)
                .Select(a => new CmProjectsInfo { Id = a.Id, OrgId = a.OrgId, Project_Name = a.Project_Name })
                .OrderBy(a => a.Project_Name)
                .ToListAsync();



            var organizationParentList = organizationList
                .Where(a => (disableParentId.Contains(a.OrgId) || topParentId.Contains(a.OrgId)) && a.ParentId == 0)
                .ToList();

            var companyParentList = companyList
                .Where(a => organizationParentList.Select(b => b.OrgId).Contains(a.OrgId))
                .ToList();

            treeNodeDto.TreeNodeList = _organizationService.ParseCustomerTree(companyList, companyParentList,
                projectList, disableParentId, organizationList, true, 0, userCompanyList, topParentId, projectId);

            return treeNodeDto;
        }

        [HttpPost("GetCompanyProjectTreeByUserIdV3")]
        public async Task<TreeNodeDto> GetCompanyProjectTreeByUserIdV3(OrganizationListQueryDto req)
        {
            //将查询的组数据封装成子树结构返回到页面
            TreeNodeDto treeNodeDto = new TreeNodeDto();

            int userId = req.UserTrusteeId > 0 ? req.UserTrusteeId : CurrentUser.UserId;

            //获取工单的指定人
            if (req.TicketId.HasValue && req.TicketId > 0)
            {
                var ticketInfo = await _organizationRepository.Db.Queryable<CmTicketsView>()
                    .Where(a => a.IsDeleted == false && a.Id == req.TicketId)
                    .Select(a => new CmTicketsView() { })
                    .ToListAsync();

                if (ticketInfo.Count > 0)
                {
                    string assignToType = ticketInfo[0].AssigToType;
                    long assignToId = ticketInfo[0].AssigToId;

                    if (assignToType == Consts.AssigneeType_Users)
                    {
                        userId = SqlFunc.ToInt32(assignToId);
                    }
                    else if (assignToType == Consts.AssigneeType_Groups)
                    {

                    }
                }
            }

            //获取用户基本信息
            var userObj = await _userService.QueryById(userId);

            var data = await _organizationService.GetOrganizationList(new OrganizationTreeDto { });

            var userCompanyList = await _userCompanyService.GetListByUserId(new CmUserCompanyListQueryDto
            {
                userId = userId
            });

            //找出需要加载到树形中的project数据
            List<long> projectId = userCompanyList.Where(a => a.CheckType == "Project").Select(a => a.cmProjectId)
                .ToList();

            //找出要禁用的节点
            List<long> disableParentId = new List<long>();

            //找出需要加载到树形里面的节点
            List<long> topParentId = new List<long>();

            var companyList = await _organizationRepository.Db.Queryable<CompanyInfo>()
               .Where(a => a.Status == 0 && a.IsDeleted == false)
               .OrderBy(a => a.Code)
               .ToListAsync();

            _organizationService.GetDisableAndLoadParentId(userCompanyList, data, topParentId, disableParentId,
                projectId, req.MasterUserTypeFlag, userObj, companyList);

            var projectList = await _organizationRepository.Db.Queryable<CmProjectsInfo>()
                .Where(a => a.Project_Status == false && a.IsDeleted == false)
                .OrderBy(a => a.Project_Name)
                .ToListAsync();

            var organizationList = await _organizationRepository.Db.Queryable<OrganizationInfo>()
                .Where(a => a.Status == 0 && a.IsDeleted == false)
                .OrderBy(a => a.Name)
                .ToListAsync();

            var organizationParentList = organizationList
                .Where(a => (disableParentId.Contains(a.OrgId) || topParentId.Contains(a.OrgId)) && a.ParentId == 0)
                .ToList();



            var companyParentList = companyList
                .Where(a => organizationParentList.Select(b => b.OrgId).Contains(a.OrgId))
                .ToList();

            treeNodeDto.TreeNodeList = _organizationService.ParseCustomerTreeV2(companyList, companyParentList,
                projectList, disableParentId, organizationList, true, 0, userCompanyList, topParentId, projectId);

            return treeNodeDto;
        }

        private List<TreeNode> ParseAllCustomerTree(List<CompanyInfo> companyList, List<CmProjectsInfo> projectList, List<OrganizationInfo> organizationList, long parentId)
        {
            List<OrganizationInfo> topData = null;

            List<TreeNode> topTreeNodeData = new List<TreeNode>();

            topData = organizationList.Where(a => a.ParentId == parentId).OrderByDescending(a => a.OrgType).ToList();

            foreach (var organizationInfo in topData)
            {
                TreeNode treeNode = null;

                if (organizationInfo.OrgType == 1)
                {
                    CompanyInfo companyInfo = companyList.FirstOrDefault(a => a.OrgId == organizationInfo.OrgId);

                    //if (companyInfo == null)
                    //{
                    //    _logger.LogToFile(LogLevel.Information, "CompanyNode:" + organizationInfo.OrgId.ToString(), "ParseAllCustomerTree.log");
                    //}

                    if (companyInfo != null)
                    {
                        treeNode = new TreeNode
                        {
                            value = SqlFunc.ToString(organizationInfo.OrgId),
                            nodeId = SqlFunc.ToString(organizationInfo.OrgId),
                            label = companyInfo.Name,
                            nodeType = "CompanyNode",
                            BusinessUnit = SqlFunc.ToString(organizationInfo.BusinessUnit),
                        };

                        treeNode.children = ParseAllCustomerTree(companyList, projectList, organizationList,
                            companyInfo.OrgId);

                        topTreeNodeData.Add(treeNode);
                    }
                }
                else if (organizationInfo.OrgType == 3)
                {
                    CmProjectsInfo projectInfo = projectList.FirstOrDefault(a => a.OrgId == organizationInfo.OrgId);

                    //if (projectInfo == null)
                    //{
                    //    _logger.LogToFile(LogLevel.Information, "ProjectNode:" + organizationInfo.OrgId.ToString(), "ParseAllCustomerTree.log");
                    //}

                    if (projectInfo != null)
                    {
                        treeNode = new TreeNode
                        {
                            value = SqlFunc.ToString(organizationInfo.OrgId),
                            nodeId = SqlFunc.ToString(organizationInfo.OrgId),
                            label = projectInfo.Project_Name,
                            nodeType = "ProjectNode",
                            BusinessUnit = SqlFunc.ToString(organizationInfo.BusinessUnit),
                        };

                        topTreeNodeData.Add(treeNode);
                    }
                }
            }

            return topTreeNodeData;
        }

        private List<TreeNode> ParseAllCustomerTreeV2(List<CompanyInfo> companyList, List<CmProjectsInfo> projectList, List<OrganizationInfo> organizationList, long parentId)
        {
            List<OrganizationInfo> topData = null;

            List<TreeNode> topTreeNodeData = new List<TreeNode>();

            topData = organizationList.Where(a => a.ParentId == parentId).OrderByDescending(a => a.OrgType).ToList();

            foreach (var organizationInfo in topData)
            {
                TreeNode treeNode = null;

                if (organizationInfo.OrgType == 1)
                {
                    CompanyInfo companyInfo = companyList.FirstOrDefault(a => a.OrgId == organizationInfo.OrgId);

                    if (companyInfo != null)
                    {
                        treeNode = new TreeNode
                        {
                            value = SqlFunc.ToString(organizationInfo.OrgId),
                            nodeId = SqlFunc.ToString(organizationInfo.OrgId),
                            label = companyInfo.Name,
                            nodeType = "CompanyNode"
                        };

                        treeNode.children = ParseAllCustomerTreeV2(companyList, projectList, organizationList,
                            companyInfo.OrgId);

                        topTreeNodeData.Add(treeNode);
                    }
                }
            }

            return topTreeNodeData;
        }

        [HttpPost("GetCompanyProjectTreeByUserIdForListSearch")]
        public async Task<TreeNodeDto> GetCompanyProjectTreeByUserIdForListSearch(OrganizationListQueryDto req)
        {
            //获取用户基本信息
            var userObj = await _userService.QueryById(CurrentUser.UserId);

            //将查询的组数据封装成子树结构返回到页面
            TreeNodeDto treeNodeDto = new TreeNodeDto();

            List<TreeNode> treeNodeList = treeNodeDto.TreeNodeList;

            //获取所有公司信息
            var companyList = await _organizationRepository.Db.Queryable<CompanyInfo>()
                .Where(a => a.Status == 0)
                .ToListAsync();

            //获取用户所分配的project
            var userCompanyList = await _organizationRepository.Db.Queryable<CmUserCompanyInfo>()
                .Where((a) => a.IsDeleted == false && a.userId == CurrentUser.UserId && a.CheckType != "BackEnd Insert")
                .Select((a) => new CmUserCompanyInfo { })
                .ToListAsync();

            //获取所有project
            var projectList = await _organizationRepository.Db.Queryable<CmProjectsInfo>()
                .Where(a => a.IsDeleted == false && a.Project_Status == false).ToListAsync();

            //获取project所关联的公司
            var companyProjectList = await _organizationRepository.Db.Queryable<CmProjectCompanyInfo>()
                .Where(a => a.IsDeleted == false).ToListAsync();

            //根据用户所分配的公司，过滤companyList这个列表的数据
            foreach (var company in companyList.Where(a => userCompanyList.Select(u => u.CompanyId).Contains(a.OrgId)))
            {
                TreeNode companyNode = new TreeNode
                {
                    value = SqlFunc.ToString(company.OrgId),
                    nodeId = SqlFunc.ToString(company.OrgId),
                    label = company.Name,
                    nodeType = "CompanyNode"
                };

                //先根据循环的company，过滤companyProjectList这个列表的数据
                //再根据用户所分配的公司某些project，过滤companyProjectList这个列表的数据
                var getProjectData = userCompanyList.Where(a => a.cmProjectId > 0 && a.CompanyId == company.OrgId)
                    .Select(a => a.cmProjectId).ToList();

                List<CmProjectCompanyInfo> companyProjectInfo = null;

                if (userObj.UserType.Equals(Consts.UserType_EndUser) && userObj.Master_User == true)
                {
                    companyProjectInfo = companyProjectList
                        .Where(a => a.OrgId == company.OrgId)
                        .ToList();
                }
                else
                {
                    companyProjectInfo = companyProjectList
                        .Where(a => a.OrgId == company.OrgId)
                        .WhereIF(getProjectData.Count > 0, a => getProjectData.Contains(a.cmProjectId))
                        .ToList();

                    //如果用户所分配的project和公司下的project数量不是一致的话，则要禁用父节点
                    if (getProjectData.Count > 0)
                    {
                        companyNode.disabled = true;
                    }
                }

                foreach (var projectCompany in companyProjectInfo)
                {
                    var projectInfo = projectList.Where(a => a.Id == projectCompany.cmProjectId).ToList();

                    foreach (var project in projectInfo)
                    {
                        TreeNode projectNode = new TreeNode
                        {
                            value = SqlFunc.ToString(company.OrgId) + "@@@" + SqlFunc.ToString(project.Id),
                            nodeId = SqlFunc.ToString(project.Id),
                            label = project.Project_Name + " (" + company.Name + ")",
                            nodeType = "ProjectNode"
                        };

                        companyNode.children.Add(projectNode);
                    }
                }

                treeNodeList.Add(companyNode);
            }

            return treeNodeDto;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<OrganizationListDto>> Query([FromQuery] OrganizationPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<OrganizationListDto>> QueryPost(OrganizationPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<OrganizationListDto>> PageQuery([FromQuery] OrganizationPageQueryDto req)
        {
            var list = new List<OrganizationListDto>();
            var where = PredicateBuilder.New<OrganizationInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            var totalCount = await _organizationService.CountAsync(where);
            var orderBy = new OrderBy<OrganizationInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _organizationService.QueryList(where, orderBy, paging);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _organizationService.Join(item);
                list.Add(detail.Adapt<OrganizationListDto>());
            }
            #endregion

            return new PageQueryResult<OrganizationListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

        [HttpPost("Export")]
        public async Task<IActionResult> Export(OrganizationTreeDto req)
        {
            var isAllCheck = req.ischeckType == 0 ? true : false;
            return await ExcelHelper.Export<Task<List<OrganizationTreeDto>>>(this, typeof(IOrganizationService).Name, "Tree", this, isAllCheck, req);
        }
    }
}