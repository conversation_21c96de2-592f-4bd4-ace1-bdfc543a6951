﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class workflowInitiateApi extends BaseApi {

    QueryWorkflowInitiate(params: any) {
        return request({
            url: this.baseurl + 'QueryWorkflowInitiate',
            method: 'get',
            params,
        });
    }

}

export default new workflowInitiateApi('/api/workflowInitiate/','id');




                        
        
        