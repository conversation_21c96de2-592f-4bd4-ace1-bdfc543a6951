﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.Permission;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    [NonController]
    public class PermissionController : ControllerBase
    {
        private readonly ILogger<PermissionInfo> _logger;

        private readonly IPermissionService _permissionService;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="permissionService"></param>
        /// <param name="logger"></param>
        public PermissionController(IPermissionService permissionService, ILogger<PermissionInfo> logger)
        {
            this._permissionService = permissionService;
            this._logger = logger;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(PermissionAddDto req)
        {
            return await _permissionService.AddIdentity(req.Adapt<PermissionInfo>());
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(PermissionEditDto req)
        {
            return await _permissionService.Update(req.Adapt<PermissionInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(int id)
        {
            int result = await _permissionService.Delete(a => a.Id == id);

            return result > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _permissionService.DeleteByIds(items);

            return result;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<PermissionDto> Get(int id)
        {
            return await _permissionService.QueryInfo<PermissionDto>(a => a.Id == id);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<PermissionDetailDto> Detail(int id)
        {
            var obj = await _permissionService.QueryInfo(a => a.Id == id);

            if (obj != null)
            {
                return  _permissionService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<PermissionListDto>> List([FromQuery] PermissionListQueryDto req)
        {
            var list = new List<PermissionListDto>();
            var where = PredicateBuilder.New<PermissionInfo>(true);
            var orderBy = new OrderBy<PermissionInfo>(req.order, req.sort);
            var data = await _permissionService.QueryList(where, orderBy);
            
            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _permissionService.Join(item);
                list.Add(detail.Adapt<PermissionListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<PermissionListDto>> Query([FromQuery] PermissionPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<PermissionListDto>> QueryPost(PermissionPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<PermissionListDto>> PageQuery([FromQuery] PermissionPageQueryDto req)
        {
            var list = new List<PermissionListDto>();
            var where = PredicateBuilder.New<PermissionInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            var totalCount = await _permissionService.CountAsync(where);
            var orderBy = new OrderBy<PermissionInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _permissionService.QueryList(where, orderBy, paging);
            
            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _permissionService.Join(item);
                list.Add(detail.Adapt<PermissionListDto>());
            }
            #endregion

            return new PageQueryResult<PermissionListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

    }
}