﻿using Microsoft.AspNetCore.Mvc;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.WorkflowDesign;
using YunLanCrm.Model.Dto.WorkflowDesign;
using Microsoft.AspNetCore.Authorization;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class WorkflowDesignController : ControllerBase
    {
        private readonly ILogger<WorkflowDesignInfo> _logger;
        private readonly IWorkflowDesignService _workflowDesignService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="workflowDesignService"></param>
        /// <param name="logger"></param>
        public WorkflowDesignController(IWorkflowDesignService workflowDesignService, ILogger<WorkflowDesignInfo> logger)
        {
            _logger = logger;
            _workflowDesignService = workflowDesignService;

        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<Guid> Add(WorkflowDesignAddOrUpdateDto req)
        {
            return await _workflowDesignService.AddWorkflowDesign(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(WorkflowDesignAddOrUpdateDto req)
        {
            return await _workflowDesignService.UpdateWorkflowDesign(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="designId">designId</param>
        /// <returns></returns>
        [HttpPost("Delete/{designId}")]
        public async Task<bool> Delete(Guid designId)
        {
            int result = await _workflowDesignService.Delete(a => a.DesignId == designId);

            return result > 0;
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _workflowDesignService.Delete(a => items.Contains(a.DesignId));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="designId"></param>
        /// <returns></returns>
        [HttpGet("Get/{designId}")]
        public async Task<WorkflowDesignDto> Get(Guid designId)
        {
            return await _workflowDesignService.QueryInfo<WorkflowDesignDto>(a => a.DesignId == designId);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("WorkflowDesignSimples")]
        public async Task<List<WorkflowDesignSimpleDto>> WorkflowDesignSimples()
        {
            var list = new List<WorkflowDesignSimpleDto>();
            var items = await _workflowDesignService.QueryList<WorkflowDesignDto>(a => a.IsDeleted == false && a.Status == 0);
            items = items.OrderByDescending(a => a.CreatedAt).ToList();
            foreach (var item in items)
            {
                list.Add(new WorkflowDesignSimpleDto()
                {
                    DesignId = item.DesignId,
                    Name = item.WorkflowName,
                    WorkflowId = item.WorkflowId,
                    DefinitionId = item.WorkflowDefinitionId
                });
            }
            return list;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<WorkflowDesignDto>> GetList([FromQuery] WorkflowDesignListQueryDto req)
        {
            return await _workflowDesignService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="designId"></param>
        /// <returns></returns>
        [HttpGet("Detail/{designId}")]
        public async Task<WorkflowDesignDetailDto> Detail(Guid designId)
        {
            return await _workflowDesignService.Detail(designId);
        }

        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<WorkflowDesignDetailDto>> DetailList([FromQuery] WorkflowDesignListQueryDto req)
        {
            return await _workflowDesignService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<WorkflowDesignDetailDto>> Query([FromQuery] WorkflowDesignPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<WorkflowDesignDetailDto>> QueryPost(WorkflowDesignPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<WorkflowDesignDetailDto>> PageQuery([FromQuery] WorkflowDesignPageQueryDto req)
        {
            return await _workflowDesignService.PageQueryView(req);
        }

    }
}