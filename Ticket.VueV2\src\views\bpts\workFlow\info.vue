<template>
	<el-container class="workflow-info-container tablelist">
		<el-header class="mb10">
			<div class="modules-index-search">
				<el-button size="small" v-if="btnLimits.approval" type="primary" @click="onApprove">同意</el-button>
				<el-button size="small" v-if="btnLimits.approval" type="primary" @click="onRefuse">拒绝</el-button>
				<el-button size="small" v-if="btnLimits.transfer" type="primary" @click="onTransfer">转办</el-button>
				<el-button size="small" v-if="btnLimits.urge" type="primary" @click="onWorkflowUrge">催办</el-button>
				<el-button size="small" v-if="btnLimits.withdraw" type="warning" @click="onWithdraw">撤回</el-button>
				<el-button size="small" v-if="btnLimits.close" type="danger" @click="onWorkflowClose">终止</el-button>
				<el-button size="small" type="primary" @click="onBack">返回</el-button>
			</div>
		</el-header>
		<el-main class="nopadding" ref="printMain">
			<div class="scTable" style="height: 100%" ref="scTableMain">
				<el-tabs v-model="activeName" type="border-card" style="height: 100%">
					<el-tab-pane label="流程信息" name="upload" style="height: 100%">
						<div style="height: 100%">
							<iframe style="height: 100%; border: 0px" height="100%" width="100%" :src="iframeUrl"></iframe>
						</div>
					</el-tab-pane>
					<el-tab-pane label="流转记录" name="security">
						<div style="height: 100%; padding: 50px; width: 500px">
							<el-timeline>
								<el-timeline-item v-for="item in nodeLogs" :key="item.nodeId" :timestamp="item.createdAt" placement="top">
									<el-card v-if="item.nodeType === 'InitWorkflow'">
										<p>发起人：{{ item.userName }}</p>
									</el-card>
									<el-card v-if="item.nodeType === 'Approve'">
										<div>
											<h4>审核结果</h4>
											<p></p>
											<p>审核人员：{{ item.userName }}</p>
											<el-tag type="success">同意</el-tag>
										</div>
									</el-card>

									<el-card v-if="item.nodeType === 'Refuse'">
										<div>
											<h4>审核结果</h4>
											<p></p>
											<p>审核人员：{{ item.userName }}</p>
											<el-tag type="danger">拒绝</el-tag>
											<p>审核意见：{{ item.msg }}</p>
										</div>
									</el-card>

									<el-card v-if="item.nodeType === 'Transfer'">
										<div>
											<h4>审核结果</h4>
											<p></p>
											<p>执行人员：{{ item.userName }}</p>
											<p>执行动作： 转审</p>
											<el-divider />
											<p>转审人员：{{ item.data }}</p>
											<p>转审意见：{{ item.msg }}</p>
										</div>
									</el-card>

									<el-card v-if="item.nodeType === 'Ceancel'">
										<p style="color: red">流程已终止</p>
									</el-card>

									<el-card v-if="item.nodeType === 'Finish'">
										<p>流程结束</p>
									</el-card>
								</el-timeline-item>
							</el-timeline>
						</div>
					</el-tab-pane>
				</el-tabs>
			</div>
		</el-main>
		<Transfer ref="transferRef" @fetchData="onInit"></Transfer>
		<Approval ref="approvalRef" @fetchData="onInit"></Approval>
	</el-container>
</template>

<script lang="ts">
import { useRouter, useRoute } from 'vue-router';
import { toRefs, reactive, onMounted, ref, defineComponent, computed, getCurrentInstance } from 'vue';
// import { useStore } from '/@/store/index';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate } from '/@/utils/formatTime';

import workflowInstancesApi from '/@/api/workflowInstances/index';
import workflowExecutionLogRecordsApi from '/@/api/workflowExecutionLogRecords/index';
import workflowCrmApi from '/@/api/workflowCrm/index';

import Transfer from './components/transfer.vue';
import Approval from './components/approval.vue';

import { Local } from '/@/utils/storage';
import mittBus from '/@/utils/mitt';

export default defineComponent({
	name: 'apiIndex',
	components: { Transfer, Approval },
	setup() {
		const { proxy } = getCurrentInstance() as any;
		const router = useRouter();
		// const store = useStore();
		const route = useRoute();
		const transferRef = ref();
		const approvalRef = ref();
		const printMain = ref(null);
		const state: any = reactive({
			activeName: 'basic',
			nodeLogs: [],
			instancesId: '',
			nodeId: 0,
			iframeUrl: '',
			workflowCrmData: {},
			btnLimits: {
				urge: false,
				withdraw: false,
				close: false,
				approval: false,
				transfer: false,
			},
		});

		// 获取用户信息 vuex
		// const currentUser = computed(() => {
		// 	return store.state.userInfos.userInfos;
		// });

		const onInit = () => {
			console.log('route', route.params);
			workflowCrmApi.GetByKey(route.params.crmWorkflowId).then((rs) => {
				state.workflowCrmData = rs.data;
				// state.iframeUrl = 'http://127.0.0.1:7000/workflow-instances/' + state.workflowCrmData.elsaInstancesId;
				state.iframeUrl = Local.get('SystemUrl') + '/workflow-instances/' + state.workflowCrmData.elsaInstancesId;

				checkLimits();

				loadHandleLogs();
			});
		};

		const loadHandleLogs = () => {
			var obj = {
				crmWorkflowId: state.workflowCrmData.crmWorkflowId,
				workflowInstanceId: state.workflowCrmData.elsaInstancesId,
			};
			workflowExecutionLogRecordsApi.GetWorkflowNodeLogs(obj).then((rs) => {
				state.nodeLogs = rs.data;
			});
		};

		const checkLimits = () => {
			var params = {
				crmWorkflowId: state.workflowCrmData.crmWorkflowId,
				workflowInstanceId: state.workflowCrmData.elsaInstancesId,
			};
			workflowCrmApi.CheckWorkflowLimits(params).then((rs) => {
				state.btnLimits = rs.data;
			});
		};

		const onApprove = () => {
			var obj = {
				executionType: 'Approve',
				crmWorkflowId: state.workflowCrmData.crmWorkflowId,
				elsaInstancesId: state.workflowCrmData.elsaInstancesId,
			};
			approvalRef.value.openDialog(obj);
		};

		const onRefuse = () => {
			var obj = {
				executionType: 'Refuse',
				crmWorkflowId: state.workflowCrmData.crmWorkflowId,
				elsaInstancesId: state.workflowCrmData.elsaInstancesId,
			};
			approvalRef.value.openDialog(obj);
		};

		const onTransfer = (row: any) => {
			var obj = {
				executionType: 'Transfer',
				crmWorkflowId: state.workflowCrmData.crmWorkflowId,
				elsaInstancesId: state.workflowCrmData.elsaInstancesId,
			};
			transferRef.value.openDialog(obj);
		};

		const onWithdraw = (row: any) => {
			ElMessageBox.confirm(`此操作将撤回该流程，是否继续？`, '提示', {
				type: 'warning',
			}).then(() => {
				ElMessage.success('未实现...');

				return;

				var obj = {
					workflowInstanceId: state.workflowCrmData.elsaInstancesId,
				};
				workflowInstancesApi.WorkflowInstanceWithdraw(obj).then((rs) => {
					onInit();
					ElMessage.success('Succeed');
				});
			});
		};

		const onWorkflowClose = () => {
			ElMessageBox.confirm(`此操作将终止该流程，是否继续？`, '提示', {
				type: 'warning',
			}).then(() => {
				var obj = {
					workflowInstanceId: state.workflowCrmData.elsaInstancesId,
				};
				workflowInstancesApi.WorkflowInstanceCeancel(obj).then((rs) => {
					onInit();
					ElMessage.success('Succeed');
				});
			});
		};

		const onWorkflowUrge = () => {
			workflowExecutionLogRecordsApi
				.WorkflowUrge({
					crmWorkflowId: state.workflowCrmData.crmWorkflowId,
				})
				.then((rs) => {
					ElMessage.success('Succeed');
				});
		};

		const onBack = () => {
			//router.go(-1);
			//mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 0, ...route }));
			mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));
		};

		// 页面加载时
		onMounted(() => {
			onInit();
		});
		return {
			printMain,
			formatStrDate,
			transferRef,
			approvalRef,
			onInit,
			onApprove,
			onRefuse,
			onTransfer,
			onWithdraw,
			onWorkflowUrge,
			onWorkflowClose,
			onBack,
			...toRefs(state),
		};
	},
});
</script>

<style scoped lang="scss">
.action-tag {
	margin-left: 10px;
}
</style>
