﻿using Elsa;
using Elsa.Activities.Signaling;
using Elsa.Expressions;
using Elsa.Models;
using Elsa.Persistence;
using Elsa.Services;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using YunLanCrm.Common.Extensions;
using YunLanCrm.Common.HttpContextUser;
using YunLanCrm.IRepositories;
using YunLanCrm.Model;
using YunLanCrm.Model.Dto.WorkflowCrm;
using YunLanCrm.Model.Models;

namespace YunLanCrm.Extensions
{
    /// <summary>
    /// 
    /// </summary>
    public class MyBookmarkProvider : IBookmarkProvider
    {
        protected readonly IUser _user;
        private readonly IBookmarkProvider _inner;
        private readonly IWorkflowDefinitionStore _workflowDefinitionStore;
        protected readonly IWorkflowCrmRepository _workflowCrmRepository;

        public MyBookmarkProvider(IBookmarkProvider inner, IWorkflowDefinitionStore workflowDefinitionStore, IUser user, IWorkflowCrmRepository workflowCrmRepository)
        {
            _user = user;
            _inner = inner;
            _workflowDefinitionStore = workflowDefinitionStore;
            _workflowCrmRepository = workflowCrmRepository;
        }

        /// <summary>
        /// 添加任务
        /// 阻塞活动添加到 表 Elsa.Bookmarks时执行拦截，拦截对 GetBookmarksAsync 方法的调用
        /// </summary>
        /// <param name="context"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async ValueTask<IEnumerable<BookmarkResult>> GetBookmarksAsync(BookmarkProviderContext context, CancellationToken cancellationToken = default)
        {
            // Call the inner bookmark provider to get the bookmarks.
            var bookmarks = await _inner.GetBookmarksAsync(context, cancellationToken);
            if (bookmarks.Count() > 0)
            {

                var workflowInstance = context.ActivityExecutionContext.WorkflowInstance;
                var workflowDefinition = await _workflowDefinitionStore.FindByDefinitionIdAsync(workflowInstance.DefinitionId, VersionOptions.LatestOrPublished);
                if (workflowDefinition != null)
                {
                    var targetActivity = await GetTaskActivity(context, context.ActivityExecutionContext.ActivityId);
                    if (targetActivity != null)
                    {
                        //检查当前信号是否来自分支 Fork
                        var connection = workflowDefinition.Connections.Where(a => a.TargetActivityId == context.ActivityExecutionContext.ActivityId).FirstOrDefault();

                        var config = targetActivity.Properties.Where(a => a.Name == "Config").First();
                        var json = config.GetExpression(SyntaxNames.Literal);
                        var designProperty = JsonConvert.DeserializeObject<DesignNodeProperty>(json);
                        var owners = GetTaskOwners(new List<DesignNodeProperty>() { designProperty });
                        if (owners != null && owners.Count > 0)
                        {
                          
                        }
                    }
                }

            }
            return bookmarks;
        }

        public ValueTask<bool> SupportsActivityAsync(BookmarkProviderContext context, CancellationToken cancellationToken = default)
        {
            return new ValueTask<bool>(context.ActivityType.TypeName == nameof(SignalReceived));
        }

        private async Task<ActivityDefinition> GetTaskActivity(BookmarkProviderContext context, string sourceActivityId)
        {
            var workflowInstance = context.ActivityExecutionContext.WorkflowInstance;
            var workflowDefinition = await _workflowDefinitionStore.FindByDefinitionIdAsync(workflowInstance.DefinitionId, VersionOptions.LatestOrPublished);
            var targetActivityId = workflowDefinition.Connections.Where(a => a.SourceActivityId == sourceActivityId).Select(a => a.TargetActivityId).First();
            var targetActivity = workflowDefinition.GetActivityById(targetActivityId);
            if (targetActivity.Type == Consts.Activity_Type_Approved)
            {
                var config = targetActivity.Properties.Where(a => a.Name == "Config").First();
                var json = config.GetExpression(SyntaxNames.Literal);
                var designProperty = JsonConvert.DeserializeObject<DesignNodeProperty>(json);
                if (designProperty.autoApproval == 0)
                {
                    return targetActivity;
                }
            }
            return null;
        }

        private async Task<DesignNodeProperty> GetTaskActivityConfig(BookmarkProviderContext context, string sourceActivityId)
        {
            var workflowInstance = context.ActivityExecutionContext.WorkflowInstance;
            var workflowDefinition = await _workflowDefinitionStore.FindByDefinitionIdAsync(workflowInstance.DefinitionId, VersionOptions.LatestOrPublished);
            var targetActivityId = workflowDefinition.Connections.Where(a => a.SourceActivityId == sourceActivityId).Select(a => a.TargetActivityId).First();
            var targetActivity = workflowDefinition.GetActivityById(targetActivityId);
            if (targetActivity.Type == Consts.Activity_Type_Approved)
            {
                var config = targetActivity.Properties.Where(a => a.Name == "Config").First();
                var json = config.GetExpression(SyntaxNames.Literal);
                var designProperty = JsonConvert.DeserializeObject<DesignNodeProperty>(json);
                if (designProperty.autoApproval == 0)
                {
                    return designProperty;
                }
            }
            return null;
        }


        /// <summary>
        /// 获取下一个节点的审批人
        /// </summary>
        /// <param name="designProperties"></param>
        /// <returns></returns>
        public List<string> GetTaskOwners(List<DesignNodeProperty> designProperties)
        {
            var list = new List<string>();
            foreach (var designProperty in designProperties)
            {

              
            }
            return list;
        }

        public async Task<List<ActivityDefinition>> GetTaskActivity1(BookmarkProviderContext context)
        {
            var list = new List<ActivityDefinition>();
            var workflowInstance = context.ActivityExecutionContext.WorkflowInstance;
            var workflowDefinition = await _workflowDefinitionStore.FindByDefinitionIdAsync(workflowInstance.DefinitionId, VersionOptions.LatestOrPublished);
            var workflowBlueprint = context.ActivityExecutionContext.WorkflowExecutionContext.WorkflowBlueprint;
            var connections = workflowBlueprint.Connections.Where(a => a.Source.Activity.Id == context.ActivityExecutionContext.ActivityId);
            foreach (var connection in connections)
            {
                // nextActivity 是与当前 SignalReceived 活动连接的下一个活动
                var nextActivity = workflowBlueprint.GetActivity(connection.Target.Activity.Id);
                if (nextActivity != null)
                {
                    var activityDefinition = workflowDefinition.Activities.Where(a => a.ActivityId == nextActivity.Id).FirstOrDefault();

                }

            }
            return list;
        }
    }

}
