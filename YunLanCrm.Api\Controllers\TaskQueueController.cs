﻿using Microsoft.AspNetCore.Mvc;

using LinqKit;
using Mapster;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.TaskQueue;
using YunLanCrm.Tasks;
using YunLanCrm.IRepository.UnitOfWork;
using YunLanCrm.Common.LogHelper;
using Microsoft.AspNetCore.Authorization;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class TaskQueueController : ControllerBase
    {
        private readonly ILogger<TaskQueueInfo> _logger;
        private readonly ISchedulerCenter _schedulerCenter;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogEventService _logEventService;
        private readonly ITaskQueueService _taskQueueService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="taskQueueService"></param>
        /// <param name="logger"></param>
        /// <param name="schedulerCenter"></param>
        /// <param name="unitOfWork"></param>
        /// <param name="logEventService"></param>
        public TaskQueueController(ITaskQueueService taskQueueService,
            ILogger<TaskQueueInfo> logger,
            ISchedulerCenter schedulerCenter,
            IUnitOfWork unitOfWork,
            ILogEventService logEventService)
        {
            this._taskQueueService = taskQueueService;
            this._logger = logger;
            _unitOfWork = unitOfWork;
            _schedulerCenter = schedulerCenter;
            _logEventService = logEventService;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(TaskQueueAddDto req)
        {
            var obj = req.Adapt<TaskQueueInfo>();

            if (obj != null)
            {
                obj.IsDeleted = false;
                obj.CreateTime = DateTime.Now;
                if (!obj.BeginTime.HasValue)
                {
                    obj.BeginTime = DateTime.Now;
                    obj.EndTime = DateTime.Now;
                }
            }

            var data = new MessageModel<long>();
            _unitOfWork.BeginTran();
            obj.Id = (await _taskQueueService.Add(obj));
            try
            {
                if (obj.Id > 0)
                {
                    data.ResultMsg = "添加成功";
                    if (req.IsStart)
                    {



                        //如果是启动自动
                        var ResuleModel = await _schedulerCenter.AddScheduleJobAsync(obj);
                        data.success = ResuleModel.success;
                        if (ResuleModel.success)
                        {
                            data.ResultMsg = $"{data.ResultMsg}=>启动成功=>{ResuleModel.ResultMsg}";
                        }
                        else
                        {
                            data.ResultMsg = $"{data.ResultMsg}=>启动失败=>{ResuleModel.ResultMsg}";
                        }
                    }
                }
                else
                {
                    data.ResultMsg = "添加失败";

                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (obj.Id > 0)
                    _unitOfWork.CommitTran();
                else
                    _unitOfWork.RollbackTran();
            }
            return data.Data;
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(TaskQueueEditDto req)
        {
            //  return await _taskQueueService.Update(req.Adapt<TaskQueueInfo>());


            _unitOfWork.BeginTran();
            bool result = await _taskQueueService.Update(req);
            try
            {
                if (result)
                {
                    var obj = req.Adapt<TaskQueueInfo>();
                    if (req.IsStart)
                    {
                        var ResuleModelStop = await _schedulerCenter.StopScheduleJobAsync(obj);
                        //data.ResultMsg = $"{data.ResultMsg}=>停止:{ResuleModelStop.ResultMsg}";
                        var ResuleModelStar = await _schedulerCenter.AddScheduleJobAsync(obj);
                        //data.success = ResuleModelStar.success;
                        //data.ResultMsg = $"{data.ResultMsg}=>启动:{ResuleModelStar.ResultMsg}";
                    }
                    else
                    {
                        var ResuleModelStop = await _schedulerCenter.StopScheduleJobAsync(obj);
                        // data.ResultMsg = $"{data.ResultMsg}=>停止:{ResuleModelStop.ResultMsg}";
                    }
                }

            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (result)
                    _unitOfWork.CommitTran();
                else
                    _unitOfWork.RollbackTran();
            }

            return result;
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(int id)
        {
            int result = await _taskQueueService.Delete(a => a.Id == id);

            return result > 0;
        }

        /// <summary>
        /// 开启、重启、暂停、恢复 等操作
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("OperationTask")]
        public async Task<bool> OperationTask(OperationTaskDto req)
        {
            switch (req.type)
            {
                case "StartJob":
                    return await StartJob(req.id);
                case "StopJob":
                    return await StopJob(req.id);
                default:
                    break;
            }

            return false;
        }

        /// <summary>
        /// 启动计划任务
        /// </summary>
        /// <param name="jobId"></param>
        /// <returns></returns>        
        [HttpPost("StartJob")]
        public async Task<bool> StartJob(int jobId)
        {
            var model = await _taskQueueService.QueryById(jobId);
            TaskRunLogInfo log = new TaskRunLogInfo();
            log.TaskId = model.Id;
            log.GroupName = model.JobGroup;
            log.LogTime = DateTime.Now;
            log.TotalSeconds = 1;

            if (model == null)
            {
                throw new Exception("Task 不存在.");
            }
            model.IsStart = true;
            bool result = await _taskQueueService.Update(model);
            if (!result)
            {
                throw new Exception("更新Task信息失败");
            }


            var ResuleModel = await _schedulerCenter.AddScheduleJobAsync(model);
            if (ResuleModel.success)
            {
                log.Status = "Run";
                log.Message = $"启动成功=>{ResuleModel.ResultMsg}";
            }
            else
            {
                log.Status = "RunFailed";
                log.Message = $"启动失败=>{ResuleModel.ResultMsg}";
            }

            SerilogServer.WriteLog("Task", log, dataKey: model.Id.ToString());

            return ResuleModel.success;

        }

        /// <summary>
        /// 停止一个计划任务
        /// </summary>
        /// <param name="jobId"></param>
        /// <returns></returns>        
        [HttpPost("StopJob")]
        public async Task<bool> StopJob(int jobId)
        {
            var model = await _taskQueueService.QueryById(jobId);
            TaskRunLogInfo log = new TaskRunLogInfo();
            log.TaskId = model.Id;
            log.GroupName = model.JobGroup;
            log.LogTime = DateTime.Now;
            log.TotalSeconds = 1;

            if (model == null)
            {
                throw new Exception("Task 不存在.");
            }
            model.IsStart = false;
            bool result = await _taskQueueService.Update(model);
            if (!result)
            {
                throw new Exception("更新Task信息失败");
            }


            var ResuleModel = await _schedulerCenter.StopScheduleJobAsync(model);
            if (ResuleModel.success)
            {
                log.Status = "Stoped";
                log.Message = $"停止成功=>{ResuleModel.ResultMsg}";
            }
            else
            {
                log.Status = "StopedFailed";
                log.Message = $"停止失败=>{ResuleModel.ResultMsg}";
            }

            SerilogServer.WriteLog("Task", log, dataKey: model.Id.ToString());

            return ResuleModel.success;

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        [NonAction]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _taskQueueService.DeleteByIds(items);

            return result;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<TaskQueueDto> Get(int id)
        {
            return await _taskQueueService.QueryInfo<TaskQueueDto>(a => a.Id == id);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        [NonAction]
        public async Task<TaskQueueDetailDto> Detail(int id)
        {
            var obj = await _taskQueueService.QueryInfo(a => a.Id == id);

            if (obj != null)
            {
                return _taskQueueService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<TaskQueueListDto>> List([FromQuery] TaskQueueListQueryDto req)
        {
            var list = new List<TaskQueueListDto>();
            var where = PredicateBuilder.New<TaskQueueInfo>(true);
            var orderBy = new OrderBy<TaskQueueInfo>(req.order, req.sort);
            var data = await _taskQueueService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _taskQueueService.Join(item);
                list.Add(detail.Adapt<TaskQueueListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        [NonAction]
        public async Task<PageQueryResult<TaskQueueListDto>> Query([FromQuery] TaskQueuePageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<TaskQueueListDto>> QueryPost(TaskQueuePageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<TaskQueueListDto>> PageQuery([FromQuery] TaskQueuePageQueryDto req)
        {
            var list = new List<TaskQueueListDto>();
            var where = PredicateBuilder.New<TaskQueueInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            var totalCount = await _taskQueueService.CountAsync(where);
            var orderBy = new OrderBy<TaskQueueInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _taskQueueService.QueryList(where, orderBy, paging);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _taskQueueService.Join(item);
                list.Add(detail.Adapt<TaskQueueListDto>());
            }
            #endregion

            return new PageQueryResult<TaskQueueListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

    }
}