<template>
	<el-header class="table_header" style="display: flex">
		<div class="left-panel">
			<el-button size="small" type="primary" class="ml10" @click="onAdd"> New Ticket </el-button>
		</div>
		<div class="right-panel">
			<el-dropdown>
				<el-button type="primary" size="small" class="ml10">
					<el-icon>
						<ele-ArrowDownBold />
					</el-icon>
					{{ $t('message.page.buttonExport') }}
				</el-button>
				<template #dropdown>
					<el-dropdown-menu class="user-dropdown">
						<el-dropdown-item @click="onExportAllRecord(0)">{{ $t('message.page.buttonExportEntireList')
							}}</el-dropdown-item>
						<el-dropdown-item @click="onExportAllRecord(1)">{{
							$t('message.page.buttonExportSelectedRecords') }}</el-dropdown-item>
					</el-dropdown-menu>
				</template>
			</el-dropdown>
		</div>
	</el-header>
	<div class="scTable" style="height: 100%" ref="scTableMain">
		<div class="scTable-table">
			<el-table :data="tableData.data" v-loading="tableData.loading" height="calc(100%)" table-layout="fixed"
				row-key="id" lazy highlight-current-row scrollbar-always-on :load="loadChildrenTickets"
				@selection-change="selectionChange" @sort-change="onSortChange"
				:tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
				<template #empty>
					<el-empty :description="$t('message.page.emptyDescription')" :image-size="100"></el-empty>
				</template>
				<el-table-column type="selection" />

				<el-table-column fixed sortable label="Status" prop="ticket_Status" width="130" show-overflow-tooltip>
					<template #default="{ row }">
						{{ row.ticket_Status }}
					</template>
				</el-table-column>

				<el-table-column fixed sortable label="Ticket Number" prop="ticket_Number" width="150"
					show-overflow-tooltip>
					<template #default="{ row }">
						<el-link @click="onEdit(row)" type="primary">{{ row.ticket_Number }}</el-link>
					</template>
				</el-table-column>

				<el-table-column sortable label="Assigned To" prop="AssigToName" width="150" show-overflow-tooltip>
					<template #default="{ row }">
						<span>{{ row.assigToName }}</span>
					</template>
				</el-table-column>

				<el-table-column sortable label="Delegate To" prop="delegateId" width="150" show-overflow-tooltip>
					<template #default="{ row }">
						<span>{{ row.delegateName }}</span>
					</template>
				</el-table-column>

				<el-table-column sortable label="Priority" prop="ticket_Priority" width="130" show-overflow-tooltip>
					<template #default="{ row }">
						<el-tag type="success" v-if="row.ticket_Priority === 'Low'" style="width: 70px">{{
							row.ticket_Priority }}</el-tag>
						<el-tag type="info" v-if="row.ticket_Priority === 'Normal'" style="width: 70px">{{
							row.ticket_Priority }}</el-tag>
						<el-tag type="warning" v-if="row.ticket_Priority === 'High'" style="width: 70px">{{
							row.ticket_Priority }}</el-tag>
						<el-tag type="danger" v-if="row.ticket_Priority === 'Urgent'" style="width: 70px">{{
							row.ticket_Priority }}</el-tag>
					</template>
				</el-table-column>

				<el-table-column sortable label="Category" prop="ticket_Category" width="130" show-overflow-tooltip>
					<template #default="{ row }">
						{{ row.ticket_Category }}
					</template>
				</el-table-column>
				<el-table-column sortable label="Project" prop="ticket_Customer" width="130" show-overflow-tooltip>
					<template #default="{ row }">
						{{ row.ticket_Customer_Value }}
					</template>
				</el-table-column>
				<el-table-column sortable label="Attachments" prop="isAttachment" width="150" show-overflow-tooltip>
					<template #default="{ row }">
						<el-icon v-if="row.isAttachment === 'Y'" style="color: #67c23a">
							<ele-SuccessFilled />
						</el-icon>
						<el-icon v-if="row.isAttachment === 'N'" style="color: red">
							<ele-CircleCloseFilled />
						</el-icon>
					</template>
				</el-table-column>

				<el-table-column sortable label="Opened By" prop="ticket_Open_By" width="130" show-overflow-tooltip>
					<template #default="{ row }">
						{{ row.ticket_Open_By_Name }}
					</template>
				</el-table-column>

				<el-table-column label="Solved By" width="130" show-overflow-tooltip>
					<template #default="{ row }">
						{{ row.ticket_Close_By_Name }}
					</template>
				</el-table-column>

				<el-table-column sortable label="Created Date" prop="createdAt" width="200" show-overflow-tooltip>
					<template #default="{ row }">
						<span>{{ row.createdAt }}</span>
					</template>
				</el-table-column>

				<el-table-column sortable label="Created By" prop="createdByName" width="150" show-overflow-tooltip>
					<template #default="{ row }">
						{{ row.createdByName }}
					</template>
				</el-table-column>

				<el-table-column sortable label="Modified Date" prop="modifiedAt" width="200" show-overflow-tooltip>
					<template #default="{ row }">
						{{ row.modifiedAt }}
					</template>
				</el-table-column>

				<el-table-column sortable label="Modified By" prop="modifiedByName" width="150" show-overflow-tooltip>
					<template #default="{ row }">
						{{ row.modifiedByName }}
					</template>
				</el-table-column>

				<el-table-column sortable label="Ticket From" prop="ticket_From" width="150" show-overflow-tooltip>
					<template #default="{ row }">
						{{ row.ticket_From }}
					</template>
				</el-table-column>
			</el-table>
			<div class="scTable-page">
				<el-pagination @size-change="onSizechange" @current-change="onCurrentChange" :pager-count="5"
					:page-sizes="[10, 20, 30]" v-model:current-page="tableData.params.pageIndex" background
					v-model:page-size="tableData.params.pageSize" layout="total, sizes, prev, pager, next, jumper"
					:total="tableData.total" small>
				</el-pagination>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { useRouter } from 'vue-router';
import {
	toRefs,
	reactive,
	onMounted,
	ref,
	defineComponent,
	computed,
	onBeforeMount,
	getCurrentInstance,
	onUnmounted,
	defineExpose,
	inject,
} from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate } from '/@/utils/formatTime';
import print from '/@/utils/print.js';
import { useI18n } from 'vue-i18n';

import cmTicketsApi from '/@/api/cmTickets/index';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import { formatDateTime } from '/@/utils/formatTime';
import mittBus from '/@/utils/mitt';

export default defineComponent({
	name: 'TicketList',
	components: {},

	setup(props, content) {
		const { proxy } = <any>getCurrentInstance();
		const { t } = useI18n();
		const router = useRouter();
		const createOrEditRef = ref();
		const printMain = ref(null);
		const stores = useUserInfo();
		const { userInfos } = storeToRefs(stores);
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				paramsType: 0, //默认使用params的参数进行查询 1：表示使用topParams来查询
				params: {
					groupId: '' as any,
					ticket_Parent_Id: 0,
					ticket_Status: '' as any,
					ticket_Number: '',
					ticket_Priority: '',
					ticket_From: '',
					ticket_Category: '',
					ticket_Customer: '',
					pageIndex: 1,
					pageSize: 10,
					searchKey: '',
					startTime: '',
					endTime: '',
					assigToId: '' as any,
					createdById: '' as any,
					ticketTab: '',
					fromPage: 'personal',
					order: 'CreatedAt',
					sort: 'desc', // asc or desc
					ids: [],
					ischeckType: 0,
				},
				topParams: {
					groupId: '' as any,
					ticket_Status: '' as any,
					assigToId: '' as any,
					ticket_From: '',
				},
			},
			pickerDate: [],
			buttonSelected: {} as any,
		});

		// 获取用户信息 vuex
		const currentUser = computed(() => {
			return userInfos.value;
		});

		//初始化
		const onInit = (params: any) => {
			state.tableData.params = params;

			// if (state.tableData.paramsType === 0) {
			// 	if (state.pickerDate && state.pickerDate.length == 2) {
			// 		state.tableData.params.startTime = state.pickerDate[0];
			// 		state.tableData.params.endTime = state.pickerDate[1];

			// 	}
			// }

			if (params.ticketTab == '') {
				state.tableData.params.ticketTab = '0';
			}

			if (state.tableData.params.ticketTab == '0') {
				state.tableData.params.createdById = currentUser.value.userId;
				state.tableData.params.assigToId = '';
			} else {
				state.tableData.params.createdById = '';
				state.tableData.params.assigToId = currentUser.value.userId;
			}

			const query = { ...state.tableData.params };

			state.tableData.loading = true;
			cmTicketsApi
				.Query(query)
				.then((rs: any) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
				})
				.catch(() => { })
				.finally(() => (state.tableData.loading = false));
		};
		defineExpose({
			onInit,
		});

		const loadChildrenTickets = async (row: any, treeNode: unknown, resolve: (data: any[]) => void) => {
			var params = {
				Ticket_Parent_Id: row.id,
			};
			const result = await cmTicketsApi.Query(params);

			resolve(result.data);
		};

		// 添加
		const onAdd = () => {
			router.push('/dashboard/ticket/create');
		};
		// 修改
		const onEdit = (row: any) => {
			router.push('/dashboard/editTicket/' + row.ticket_Number);
		};
		// 删除
		const onDelete = (row: any) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				cmTicketsApi
					.DeleteByKey(row.id)
					.then((rs: any) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit(state.tableData.params);
					})
					.catch(() => { });
			});
		};
		//批量删除
		const onDeleteByList = () => {
			ElMessageBox.confirm(
				t('message.page.dlgDeleteSelectText1') + ` ${state.tableData.selection.length} ` + t('message.page.dlgDeleteSelectText2'),
				t('message.page.dlgTip'),
				{
					type: 'warning',
					closeOnClickModal:false,
				}
			).then(() => {
				cmTicketsApi.DeleteMany({ keys: state.tableData.selection.join(',') }).then((rs) => {
					if (rs.data == 0) {
						ElMessage.error(rs.resultMsg);
						return;
					}
					onInit(state.tableData.params);
				});
			});
		};
		const onSortChange = (column: any) => {
			state.tableData.params.order = column.prop;
			state.tableData.params.sort = column.order;
			onInit(state.tableData.params);
		};
		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection;
		};
		//打印
		const onPrint = () => {
			print(printMain.value);
		};
		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.params.pageSize = val;
			content.emit('onChildSearch', val);
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.params.pageIndex = val;
			content.emit('onChildSearch', val);
		};

		// 页面加载时
		onMounted(() => {
			onInit(state.tableData.params);
			//事件监听器
			mittBus.on('RefreshTicketList', () => {
				onInit(state.tableData.params);
			});
		});
		// 页面加载前
		onBeforeMount(() => { });
		// 页面销毁时
		onUnmounted(() => {
			mittBus.off('RefreshTicketList');
		});
		const onExportAllRecord = (selectAll: number) => {
			let currPageSize = state.tableData.params.pageSize;
			state.tableData.params.pageSize = 10000;

			var checkSelection = state.tableData.selection;
			let ids_arr = [] as any;

			checkSelection.forEach(function (item, index) {
				let idjson = {} as any;
				idjson = item.id;
				ids_arr.push(idjson);
			});

			state.tableData.params.ischeckType = selectAll;
			if (selectAll == 1 && ids_arr.length == 0) {
				ElMessage.error(t('message.userFields.SelectTheDataYouWantToExport'));
				return;
			}
			if (selectAll == 0) {
				state.tableData.params.ids = [];
			} else {
				state.tableData.params.ids = ids_arr;
			}
			cmTicketsApi
				.Export(state.tableData.params)
				.then((rs) => {
					downloadCallback(rs);
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));

			state.tableData.params.pageSize = currPageSize;
		};
		const downloadCallback = (rs) => {
			let data = rs;
			var newBlob = new Blob([data], { type: 'text/plain;charset=UTF-8' });
			var anchor = document.createElement('a');
			anchor.download = 'Ticket_' + formatDateTime() + '.xlsx';
			anchor.href = window.URL.createObjectURL(newBlob);
			anchor.click();
		};

		return {
			printMain,
			formatStrDate,
			onPrint,
			createOrEditRef,
			selectionChange,
			onInit,
			loadChildrenTickets,
			onAdd,
			onEdit,
			onDelete,
			onDeleteByList,
			onSizechange,
			onCurrentChange,
			onSortChange,
			...toRefs(state),
			onExportAllRecord,
		};
	},
});
</script>
