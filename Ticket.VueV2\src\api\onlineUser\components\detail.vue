﻿<template>
    <el-main style="padding:0 20px;">
        <el-descriptions :column="1" border size="small">
           
 <el-descriptions-item label="id">{{ info.id }}</el-descriptions-item>
 <el-descriptions-item label="userId">{{ info.userId }}</el-descriptions-item>
 <el-descriptions-item label="clientIP">{{ info.clientIP }}</el-descriptions-item>
 <el-descriptions-item label="loginTime">{{ info.loginTime }}</el-descriptions-item>
 <el-descriptions-item label="device">{{ info.device }}</el-descriptions-item>
 <el-descriptions-item label="modifyTime">{{ info.modifyTime }}</el-descriptions-item>
 <el-descriptions-item label="description">{{ info.description }}</el-descriptions-item>
        </el-descriptions>
    </el-main>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent } from 'vue';
import { formatStrDate } from '/@/utils/formatTime';

export default defineComponent({
    name: 'apiDetail',
    props: {
        info: Object
    },
    setup(props) {
        const state = reactive({
            isShowDialog: false,
            obj: {},
        });
        // 页面加载时
        onMounted(() => {
           window.console.log("props", props.info)
        });
        return {
            ...toRefs(state),
        };
    },
});
</script>


                        
        
        