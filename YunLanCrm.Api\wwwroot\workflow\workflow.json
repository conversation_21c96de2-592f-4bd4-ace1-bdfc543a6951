{"$id": "1", "activities": [{"$id": "2", "activityId": "39a9dd1a-2069-41f2-a27f-1108d3d34eab", "type": "SignalReceived", "name": "SignalReceived1", "displayName": "", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "3", "name": "Signal", "expressions": {"$id": "4", "Literal": "SignalReceived1"}}], "propertyStorageProviders": {}}, {"$id": "5", "activityId": "b7eee6e3-0fdc-4cbe-8470-7b8f374a6f0b", "type": "StartActivity", "name": "node1", "displayName": "node1", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "6", "name": "Content", "expressions": {"$id": "7", "Literal": ""}}, {"$id": "8", "name": "Config", "expressions": {"$id": "9", "Literal": "{\"nodeName\":\"node1\",\"nodeDisplayName\":\"\",\"activityType\":\"\",\"approvalType\":1,\"approvalData\":[],\"approvalMethod\":\"1\",\"returnType\":\"1\",\"approvalAction\":true,\"approvalActionText\":\"同意\",\"returnAction\":true,\"returnActionText\":\"退回\",\"assignToAction\":true,\"assignToActionText\":\"转办\",\"rejectAction\":true,\"rejectActionText\":\"拒绝\",\"approveNotice\":\"\",\"returnNotice\":\"\",\"assignToNotice\":\"\",\"rejectNotice\":\"\",\"timedoutNotice\":\"\",\"Execute\":{\"Id\":\"39a9dd1a-2069-41f2-a27f-1108d3d34eab\",\"MOde\":\"SignalReceived\",\"Name\":\"SignalReceived1\",\"DisplayName\":\"\"},\"CC\":{\"Users\":[],\"Roles\":[],\"Departments\":[],\"Organizations\":[]}}"}}], "propertyStorageProviders": {}}, {"$id": "10", "activityId": "c97ea20b-b3e7-44a3-8e92-aa0ee48dc838", "type": "SignalReceived", "name": "SignalReceived2", "displayName": "", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "11", "name": "Signal", "expressions": {"$id": "12", "Literal": "SignalReceived2"}}], "propertyStorageProviders": {}}, {"$id": "13", "activityId": "fb6b782b-f950-490a-9677-94669f6c0f7f", "type": "Approved", "name": "node2", "displayName": "node2", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "14", "name": "Content", "expressions": {"$id": "15", "Literal": "Approved"}}, {"$id": "16", "name": "Config", "expressions": {"$id": "17", "Literal": "{\"nodeName\":\"node2\",\"nodeDisplayName\":\"审批节点\",\"activityType\":\"\",\"approvalType\":1,\"approvalData\":[{\"itemName\":\"admin\",\"itemId\":1},{\"itemName\":\"Approver_user1\",\"itemId\":2},{\"itemName\":\"BPO_user1\",\"itemId\":7},{\"itemName\":\"Accounting_user1\",\"itemId\":8}],\"approvalMethod\":\"1\",\"returnType\":\"1\",\"approvalAction\":true,\"approvalActionText\":\"同意\",\"returnAction\":true,\"returnActionText\":\"退回\",\"assignToAction\":true,\"assignToActionText\":\"转办\",\"rejectAction\":true,\"rejectActionText\":\"拒绝\",\"approveNotice\":\"\",\"returnNotice\":\"\",\"assignToNotice\":\"\",\"rejectNotice\":\"\",\"timedoutNotice\":\"\",\"Execute\":{\"Id\":\"c97ea20b-b3e7-44a3-8e92-aa0ee48dc838\",\"MOde\":\"SignalReceived\",\"Name\":\"SignalReceived2\",\"DisplayName\":\"\"},\"CC\":{\"Users\":[],\"Roles\":[],\"Departments\":[],\"Organizations\":[]}}"}}], "propertyStorageProviders": {}}, {"$id": "18", "activityId": "999b6198-9c0a-417e-8acc-f7066cd09982", "type": "Finish", "name": "node3", "displayName": "node3", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "19", "name": "Content", "expressions": {"$id": "20", "Literal": "Finish"}}], "propertyStorageProviders": {}}], "connections": [{"$id": "21", "sourceActivityId": "39a9dd1a-2069-41f2-a27f-1108d3d34eab", "targetActivityId": "b7eee6e3-0fdc-4cbe-8470-7b8f374a6f0b", "outcome": "Done"}, {"$id": "22", "sourceActivityId": "b7eee6e3-0fdc-4cbe-8470-7b8f374a6f0b", "targetActivityId": "c97ea20b-b3e7-44a3-8e92-aa0ee48dc838", "outcome": "Done"}, {"$id": "23", "sourceActivityId": "c97ea20b-b3e7-44a3-8e92-aa0ee48dc838", "targetActivityId": "fb6b782b-f950-490a-9677-94669f6c0f7f", "outcome": "Done"}, {"$id": "24", "sourceActivityId": "fb6b782b-f950-490a-9677-94669f6c0f7f", "targetActivityId": "999b6198-9c0a-417e-8acc-f7066cd09982", "outcome": "Done"}], "variables": {"$id": "25", "data": {}}, "contextOptions": {"$id": "26", "contextType": "System.Object, mscorlib", "contextFidelity": "<PERSON><PERSON><PERSON>"}, "customAttributes": {"$id": "27", "data": {}}, "channel": "Channel5eb89de9-7672-4ab7-978a-876a588d24c3"}