﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hczb.Tools.HolidayOperation
{
    /// <summary>
    /// DateTimeExtension
    /// </summary>
    public static class DateTimeExtension
    {
        /// <summary>
        /// If the given date on this Weekday it can be shifted
        /// </summary>
        /// <param name="value">The date</param>
        /// <param name="saturday">shift for Saturday</param>
        /// <param name="sunday">shift for Sunday</param>
        /// <param name="monday">shift for Monday</param>
        /// <returns></returns>
        public static DateTime Shift(this DateTime value, Func<DateTime, DateTime> saturday, Func<DateTime, DateTime> sunday, Func<DateTime, DateTime> monday = null)
        {
            switch (value.DayOfWeek)
            {
                case DayOfWeek.Saturday:
                    return saturday.Invoke(value);

                case DayOfWeek.Sunday:
                    return sunday.Invoke(value);

                case DayOfWeek.Monday:
                    if (monday != null)
                    {
                        return monday.Invoke(value);
                    }
                    break;

                default:
                    break;
            }

            return value;
        }
    }
}
