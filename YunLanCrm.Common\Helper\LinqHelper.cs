﻿using System;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace YunLanCrm.Common.Helper
{
    /// <summary>
    /// Linq操作帮助类
    /// </summary>
    public static class LinqHelper
    {
        /// <summary>
        /// 创建初始条件为True的表达式
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static Expression<Func<T, bool>> True<T>()
        {
            return x => true;
        }

        /// <summary>
        /// 创建初始条件为False的表达式
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static Expression<Func<T, bool>> False<T>()
        {
            return x => false;
        }

        /// <summary>
        /// 创建初始条件为属性（字段）的表达式
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="propertyName"></param>
        /// <returns></returns>
        public static Expression<Func<T, object>> Property<T>(string propertyName)
        {
            try
            {
                var type = typeof(T);
                BindingFlags flag = BindingFlags.Public | BindingFlags.IgnoreCase | BindingFlags.Instance;
                var property = type.GetProperty(propertyName, flag);
                // Target expression: (T obj) => (object)obj.Property;
                ParameterExpression objParam = Expression.Parameter(typeof(T), "obj");
                Expression body = Expression.Convert(Expression.MakeMemberAccess(objParam, property), typeof(object));

                return Expression.Lambda<Func<T, object>>(body, objParam);
            }
            catch (Exception ex)
            {

            }

            return null;
        }



    }
}
