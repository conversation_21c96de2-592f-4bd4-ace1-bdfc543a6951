﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmOpenItemDetailComment;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmOpenItemDetailCommentController : ControllerBase
    {
        private readonly ILogger<CmOpenItemDetailCommentInfo> _logger;
        private readonly ICmOpenItemDetailCommentService _cmOpenItemDetailCommentService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmOpenItemDetailCommentService"></param>
        /// <param name="logger"></param>
        public CmOpenItemDetailCommentController(ICmOpenItemDetailCommentService cmOpenItemDetailCommentService, ILogger<CmOpenItemDetailCommentInfo> logger)
        {
            _logger = logger;
            _cmOpenItemDetailCommentService = cmOpenItemDetailCommentService;
            
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CmOpenItemDetailCommentAddOrUpdateDto req)
        {
            return await _cmOpenItemDetailCommentService.AddCmOpenItemDetailComment(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmOpenItemDetailCommentAddOrUpdateDto req)
        {
            return await _cmOpenItemDetailCommentService.UpdateCmOpenItemDetailComment(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _cmOpenItemDetailCommentService.Delete(a => a.Id == id);

            return result > 0;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _cmOpenItemDetailCommentService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmOpenItemDetailCommentDto> Get(long id)
        {
            return await _cmOpenItemDetailCommentService.QueryInfo<CmOpenItemDetailCommentDto>(a => a.Id == id);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmOpenItemDetailCommentDto>> GetList([FromQuery] CmOpenItemDetailCommentListQueryDto req)
        {
            return await _cmOpenItemDetailCommentService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<CmOpenItemDetailCommentDto> Detail(long id)
        {
            return await _cmOpenItemDetailCommentService.Detail(id);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmOpenItemDetailCommentDto>> DetailList([FromQuery] CmOpenItemDetailCommentListQueryDto req)
        {
            return await _cmOpenItemDetailCommentService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmOpenItemDetailCommentDto>> Query([FromQuery] CmOpenItemDetailCommentPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmOpenItemDetailCommentDto>> QueryPost(CmOpenItemDetailCommentPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmOpenItemDetailCommentDto>> PageQuery([FromQuery] CmOpenItemDetailCommentPageQueryDto req)
        {
            return await _cmOpenItemDetailCommentService.PageQueryView(req);
        }

    }
}