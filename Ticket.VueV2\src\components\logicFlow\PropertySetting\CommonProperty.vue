<template>
	<div class="approval-setting">
		<el-form label-position="top" v-model="form" :rules="formRules">
			<el-form-item label="节点名称" class="form-lable">
				<el-input v-model="form.name" placeholder="Please input node name"></el-input>
			</el-form-item>
			<el-form-item label="显示名称" class="form-lable">
				<el-input v-model="form.displayName" placeholder="Please input node name"></el-input>
			</el-form-item>
			<el-form-item label="节点类型" v-if="type === 'UserTask'" class="form-lable">
				<el-select v-model="form.activityType" class="form-select" placeholder="Select" size="large" clearable>
					<el-option v-for="item in activityTypeOptions" :key="item" :label="item.label" :value="item.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="选择分支" v-if="type === 'Fork'" class="form-lable">
				<el-select v-model="form.forkMode" class="form-select" placeholder="Select" size="large" clearable>
					<el-option label="不需要选择分支" value="1" />
					<el-option label="审批时必须选择其中一个分支" value="2" />
				</el-select>
			</el-form-item>
			<el-form-item label="合并模式" v-if="type === 'Join'" class="form-lable">
				<el-select v-model="form.joinMode" class="form-select" placeholder="Select" size="large" clearable>
					<el-option label="其中一条分支完成即可" value="WaitAny" />
					<el-option label="所有分支都需要完成" value="WaitAll" />
				</el-select>
			</el-form-item>
		</el-form>
		<div style="margin: 20px">
			<el-button size="small" @click="onClosed">Cancel</el-button>
			<el-button type="primary" size="small" @click="onSubmit">Save</el-button>
		</div>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, ref, onMounted } from 'vue';
import workflowCrmApi from '/@/api/workflowCrm/index';

export default defineComponent({
	name: 'approvalSetting',
	props: {
		nodeData: Object,
		lf: Object || String,
	},
	components: {},
	setup(props, context) {
		const selectUserRef = ref();
		const approvalTypeRef = ref();
		const state = reactive({
			type: '',
			form: {
				name: '', //节点名称
				displayName: '',
				activityType: '', //自定义Elsa Activity
				joinMode: 'WaitAny',
				forkMode: '1',
			},
			formRules: {
				workflowName: [{ required: true, message: 'Please select', trigger: 'blur' }],
			},
			activityTypeOptions: [],
		});

		const onSubmit = () => {
			const nodeData = props.nodeData;
			nodeData.properties = state.form;
			const { id } = props.nodeData;
			props.lf.updateText(id, state.form.displayName);
			props.lf.setProperties(nodeData.id, state.form);
			context.emit('onClose');
		};

		const onClosed = () => {
			context.emit('onClose');
		};

		onMounted(() => {
			const { properties, text, type, id } = props.nodeData;
			var graphData = props.lf.getGraphData();
			const nodeTypes = ['ApprovalNode', 'StartNode'];
			state.nodeOptions = graphData.nodes
				.filter((item) => nodeTypes.indexOf(item.type) >= 0 && item.id !== id)
				.map((item) => ({ id: item.id, value: item.properties.displayName ?? item.properties.name }));

			if (properties) {
				state.form = Object.assign({}, state.form, properties);
			}

			if (text && text.value) {
				state.form.displayName = text.value;
			}
			state.type = type;

			workflowCrmApi.GetActivityTypes({}).then((rs) => {
				let options = rs.data;
				let defaultOption = '默认';
				options.unshift(defaultOption);
				let optionsArray = options.map((x: string) => (x === '默认' ? { label: x, value: '' } : { label: x, value: x }));
				state.activityTypeOptions = optionsArray;
			});
		});

		return {
			selectUserRef,
			approvalTypeRef,
			onSubmit,
			onClosed,
			...toRefs(state),
		};
	},
});
</script>

<style scoped lang="scss">
.approval-setting {
	.form-lable {
		font-weight: bold;
	}
	.per-cell {
		width: 100%;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		margin-bottom: 10px;
	}
	.form-select {
		width: 100%;
	}
}
</style>
