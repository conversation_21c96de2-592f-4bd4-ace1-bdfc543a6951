﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using YunLanCrm.Dto.Token;
using YunLanCrm.IRepository.Base;
using YunLanCrm.IServices;
using YunLanCrm.Model.Models;
using YunLanCrm.Model.Views;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 测试的控制
    /// </summary>
    [Route("Api/[controller]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ATestController : ControllerBase
    {
        private readonly ILogger<ATestController> _logger;
        private readonly IBaseRepository<UserInfo> _userRepository;
        private readonly IATestService _testService;

        public ATestController(ILogger<ATestController> logger, IBaseRepository<UserInfo> userRepository, IATestService testService)
        {
            _logger = logger;
            _userRepository = userRepository;
            _testService = testService;
        }

        /// <summary>
        /// 把日志写入数据库
        /// </summary>
        /// <returns></returns>
        [HttpGet("LogToDatabase")]
        [NonAction]
        public void LogToDatabase()
        {
            _logger.LogToDatabase(LogLevel.Information, "把相关的信息写入数据库LogEvent表", "", "");
        }

        /// <summary>
        /// 把日志写入默认文件application.log
        /// </summary>
        [HttpGet("LogToFile")]
        [NonAction]
        public void LogToFile()
        {
            _logger.LogToFile(LogLevel.Information, "把相关的信息写入到默认文件/log/application.log");
        }

        /// <summary>
        /// 把日志写入默认文件application.log
        /// </summary>
        [HttpGet("VueLog")]
        [NonAction]
        public void VueLog(string message)
        {
            _logger.LogToFile(LogLevel.Information, message, "VueLog.log");
        }

        /// <summary>
        /// 把日志写入指定文件
        /// </summary>
        [HttpGet("LogToFileName")]
        [NonAction]
        public void LogToFileName()
        {
            _logger.LogToFile(LogLevel.Information, "把日志写入指定文件", "test.log");
        }

        /// <summary>
        /// 获取用户id
        /// </summary>
        /// <param name="category">所属类别</param>
        /// <param name="company">所属公司</param>
        /// <returns></returns>
        [HttpGet("GetUserId")]
        [NonAction]
        public async Task<List<int>> GetUserId(string category, string company)
        {
            var list = await _userRepository.Db.Queryable<UserInfo>()
                .Where(a => a.Status == 0 && a.IsDeleted == false)
                .Take(5)
                .ToListAsync();

            return list.Select(a => a.Id).ToList();
        }

        [HttpGet("GenerateCompanyData")]
        [NonAction]
        public async Task<bool> GenerateCompanyData(bool isGenerateParent = false, int generateDataCount = 100)
        {
            await _testService.GenerateCompanyData(isGenerateParent, generateDataCount);

            return true;
        }

        [HttpGet("GenerateProjectData")]
        [NonAction]
        public async Task<bool> GenerateProjectData()
        {
            await _testService.GenerateProjectData();

            return true;
        }

        [HttpGet("GenerateUserData")]
        [NonAction]
        public async Task<bool> GenerateUserData()
        {
            await _testService.GenerateUserData();

            return true;
        }

        [HttpGet("GenerateGroupData")]
        [NonAction]
        public async Task<bool> GenerateGroupData()
        {
            await _testService.GenerateGroupData();

            return true;
        }

        [HttpGet("GenerateCategoryData")]
        [NonAction]
        public async Task<bool> GenerateCategoryData()
        {
            await _testService.GenerateCategoryData();

            return true;
        }

        [HttpGet("GenerateTicketData")]
        [NonAction]
        public async Task<bool> GenerateTicketData()
        {
            await _testService.GenerateTicketData();

            return true;
        }
    }
}
