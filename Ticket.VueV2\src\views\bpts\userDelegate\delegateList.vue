﻿<template>
	<el-container class="userDelegate-index-container tablelist" style="height: 100%">
			<el-header style="height: auto; padding: 0px; border: #e7e7e7 solid 1px">
				<div class="list-index-search">
					<el-form label-width="120px" style="padding: 5px; margin: 0px" @keyup.enter="onSearch">
						<el-row :gutter="35">
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
								<el-form-item :label="$t('message.userFields.delegateTo')">
									<el-input v-model="tableData.param.userDelegateName" clearable></el-input>
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8" class="mb6">
								<el-form-item :label="$t('message.userFields.approverType')" prop="approverType">
									<el-select v-model="tableData.param.approverType" :placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w-20">
										<el-option v-for="item in approverTypeData" :label="item.itemName" :value="item.itemName" :key="item.itemId"> </el-option>
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8" class="mb6">
								<el-form-item :label="$t('message.ticketLabels.projects')">
									<el-tree-select
										v-model="tableData.param.ticket_Customer_List"
										:data="customerOpts"
										node-key="value"
										multiple
										:render-after-expand="false"
										filterable
										:filter-node-method="filterNodeMethod"
										clearable
										show-checkbox
										:check-on-click-node="true"
										:check-strictly="true"
										:default-expand-all="true"
										class="w-20 tag-select-input"
										collapse-tags
										:placeholder="$t('message.page.selectKeyPlaceholder')"
									/>
								</el-form-item>
							</el-col>

							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
								<el-form-item :label="$t('message.userFields.delegateDate')">
									<el-row style="width: 100%">
										<el-col>
											<MyDate v-model:input="tableData.param.dateStart" :placeholder="$t('message.page.selectKeyPlaceholder')" style="width: 130px" />
											&nbsp;{{ $t('message.userFields.to') }}&nbsp;
											<MyDate v-model:input="tableData.param.dateEnd" :placeholder="$t('message.page.selectKeyPlaceholder')" style="width: 130px" />
										</el-col>
									</el-row>
								</el-form-item>
							</el-col>

							<el-col :xs="24" :sm="22" :md="22" :lg="22" :xl="22" class="mb6">
								<el-form-item>
									<el-button size="small" type="primary" @click="onSearch"> {{ $t('message.page.buttonSearch') }} </el-button>
									<el-button size="small" type="danger" @click="onSearchReSet">
										{{ $t('message.page.buttonReset') }}
									</el-button>
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
				</div>
			</el-header>
			<el-header class="table_header" style="margin-top: 10px; padding: 0px">
				<div class="left-panel">
					<el-button v-auth="'systemUser.AddDelegate'" size="small" type="primary" class="ml10" @click="onAdd" v-if="false">
						{{ $t('message.userButtons.buttonAdd') }}
					</el-button>

					<el-button
						v-auth="'systemUser.BatchDeleteDelegate'"
						type="danger"
						size="small"
						:disabled="tableData.selection.length == 0"
						@click="onDeleteByList"
						v-if="false"
					>
						{{ $t('message.page.buttonDeleteBatch') }}
					</el-button>

					<el-button v-auth="'systemUser.DelegateActionHistory'" type="info" size="small" @click="onDelegateAudit">
						{{ $t('message.userButtons.actionHistory') }}
					</el-button>

					<el-switch
						ref="refOnoff"
						v-auth="'delegateUser.OnOff'"
						v-model="onOffbatch"
						class="ml10"
						size="large"
						:disabled="tableData.selection.length == 0"
						@change="onDelegateOnOff"
					/>
				</div>
			</el-header>
			<el-main class="nopadding" ref="printMain">
				<div class="scTable" style="height: 100%; border: #e7e7e7 solid 1px" ref="scTableMain">
					<div class="scTable-table">
						<el-table
							:data="tableData.data"
							v-loading="tableData.loading"
							height="calc(100%)"
							@row-dblclick="onDetail"
							@selection-change="selectionChange"
							stripe border
						>
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')" :image-size="100"></el-empty>
							</template>
							<el-table-column type="selection" />

							<el-table-column :label="$t('message.userFields.approverType')" :min-width="150" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.approverType }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.userFields.delegateTo')" :min-width="140" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.userDelegateName }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.userFields.projects')" prop="projectNames" :min-width="250" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.projectNames }}</span>
								</template>
							</el-table-column>

							<el-table-column label="On/Off" width="200" show-overflow-tooltip v-auth="'delegateUser.OnOff'">
								<template #default="{ row }">
									<el-switch v-model="row.isApprove" class="ml-2" :label="$t('message.groupFields.Default')" @click="onoff(row.isApprove, row.id)" />
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.userFields.startDate')" width="200">
								<template #default="{ row }">
									<span>{{ formatDay(row.dateStart) }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.userFields.endDate')" width="200">
								<template #default="{ row }">
									<span>{{ formatDay(row.dateEnd) }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.userFields.email')" width="200" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.userDeletgateEmail }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.userFields.canEmail')" width="200" show-overflow-tooltip>
								<template #default="{ row }">
									<el-icon v-if="row.isEmail" style="color: #67c23a">
										<ele-SuccessFilled />
									</el-icon>
									<el-icon v-if="!row.isEmail" style="color: red">
										<ele-SuccessFilled />
									</el-icon>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.userFields.createTime')" width="200">
								<template #default="{ row }">
									<span>{{ formatStrDate(row.createdAt) }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.userFields.modifiedTime')" width="200">
								<template #default="{ row }">
									<span>{{ formatStrDate(row.modifiedAt) }}</span>
								</template>
							</el-table-column>

							<el-table-column fixed="right" align="left" :label="$t('message.page.actions')" :min-width="120">
								<template #default="{ row }">
									<el-button v-if="0 > 1" size="mini" type="text" @click="onDetail(row)">{{ $t('message.page.actionsView') }}</el-button>

									<el-row>
										<el-col :xs="24" :sm="24" :md="24" :lg="12">
											<el-button size="mini" type="text" @click="onEdit(row)">{{ $t('message.page.actionsEdit') }}</el-button>
										</el-col>
										<el-col :xs="24" :sm="24" :md="24" :lg="12" v-if="false">
											<el-button size="mini" type="text" style="color: #ff3a3a" @click="onDelete(row)">{{
												$t('message.page.actionsDelete')
											}}</el-button>
										</el-col>
									</el-row>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div class="scTable-page">
						<el-pagination
							@size-change="onSizechange"
							@current-change="onCurrentChange"
							:pager-count="5"
							:page-sizes="[10, 20, 30]"
							v-model:current-page="tableData.param.pageIndex"
							background
							v-model:page-size="tableData.param.pageSize"
							layout="total, sizes, prev, pager, next, jumper"
							:total="tableData.total"
							small
						>
						</el-pagination>
					</div>
				</div>

				<CreateOrEdit ref="createOrEditRef" @fetchData="onInit"></CreateOrEdit>
				<DelegateAudit ref="delegateAuditRef"></DelegateAudit>

				<el-drawer v-model="infoDrawer" title="信息详情" :size="600" destroy-on-close v-if="false">
					<Detail ref="detailRef" :info="detailObj"></Detail>
				</el-drawer>
			</el-main>
	</el-container>
</template>

<script lang="ts">
//import { useRouter } from 'vue-router';
import { toRefs, reactive, onMounted, ref, defineComponent, computed } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate, formatDay } from '/@/utils/formatTime';
import print from '/@/utils/print.js';
import { useI18n } from 'vue-i18n';
import userDelegateApi from '/@/api/userDelegate/index';
import Detail from './components/detail.vue';
import CreateOrEdit from './components/createOrEdit.vue';
import MyDate from '/@/components/ticket/ticketDate.vue';
import dictItemApi from '/@/api/dictItem/index';
import DelegateAudit from '/@/views/bpts/userDelegateAudit/index.vue';
import organizationApi from '/@/api/organization/index';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
interface DialogParams {
	action: string;
	id: number;
}


export default defineComponent({
	name: 'userDelegateIndex',
	props: {
		info: Object,
	},
	components: { Detail, CreateOrEdit, DelegateAudit, MyDate },
	setup(props, context) {
		const onOffbatch = ref(false);
		const { t } = useI18n();
		const storesUserInfo = useUserInfo();
		const { userInfos } = storeToRefs(storesUserInfo);
		//const router = useRouter();
		const delegateAuditRef = ref();
		const createOrEditRef = ref();
		const printMain = ref(null);
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'id',
					sort: 'desc', // asc or desc
					userDelegateName: '',
					userTrusteeName: '',
					dateStart: '',
					dateEnd: '',
					ticket_Customer_List: [],
					approverType: '',
					userTrusteeId:0,
				},
			},
			dialogParams: {
				action: '',
				id: -1,
			},
			infoDrawer: false,
			detailObj: {},
			isApprove: 0,
			customerOpts: [],
			approverTypeData: [],
		});
		// 获取用户信息 vuex
		const currentUser = computed(() => {
			return userInfos.value;
		});
		//初始化
		const onInit = () => {
			console.log("aaaaaaaaaaaaaaaaa");
			
			var dictArr = ['ApproverType'];
			dictItemApi.Many(dictArr).then((rs: any) => {
				state.approverTypeData = rs.data?.find((a: any) => {
					return a.dictValue === 'ApproverType';
				})?.items;
			});
			console.log("props.info:",props.info);
			if (props.info==undefined){
				state.tableData.param.userTrusteeId =state.dialogParams.id;
			}else{
				state.tableData.param.userTrusteeId =props.info.id;// state.dialogParams.id;
			}
			
			state.tableData.loading = true;
			userDelegateApi
				.Query(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
				})
				.catch((rs) => {})
				.finally(() => (state.tableData.loading = false));
		};
		if (currentUser.value.roles.indexOf('Super Admin') > -1) {
			organizationApi.GetCompanyProjectTree({}).then((rs) => {
				state.customerOpts = rs.data.treeNodeList;
			});
		} else {
			organizationApi.GetCompanyProjectTreeByUserId({ MasterUserTypeFlag: 1 }).then((rs) => {
				state.customerOpts = rs.data.treeNodeList;
			});
		}

		//搜索
		const onSearch = () => {
			onInit();
		};
		const onSearchReSet = () => {
			state.tableData.param.userDelegateName = '';
			state.tableData.param.dateStart = '';
			state.tableData.param.dateEnd = '';
			state.tableData.param.userTrusteeName = '';
			state.tableData.param.approverType = '';
			state.tableData.param.ticket_Customer_List = [];
			onInit();
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInit();
			//state.isShowDialog = false;
		};

		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			state.dialogParams = parmas;
			//state.isShowDialog = true;
			console.log("bbbbbbbbbbb")
			onInit();
		};

		// 添加
		const onAdd = () => {
			createOrEditRef.value.openDialog({ action: 'Create', userTrusteeId: state.dialogParams.id, showfield: true });
			//router.push('/userDelegate/Create');
		};
		// 修改
		const onEdit = (row: Object) => {
			createOrEditRef.value.openDialog({ action: 'Edit', userTrusteeId: state.dialogParams.id, id: row.id, showfield: true });
			//router.push('/userDelegate/edit/' + row.id);
		};
		//历史记录
		const onDelegateAudit = (row: object) => {
			delegateAuditRef.value.openDialog({ userTrusteeId: state.dialogParams.id });
		};
		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				userDelegateApi
					.DeleteByKey(row.id)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((rs) => {});
			});
		};
		//批量删除
		const onDeleteByList = () => {
			ElMessageBox.confirm(
				t('message.page.dlgDeleteSelectText1') + ` ${state.tableData.selection.length} ` + t('message.page.dlgDeleteSelectText2'),
				t('message.page.dlgTip'),
				{
					type: 'warning',
					closeOnClickModal:false,
				}
			).then(() => {
				var items = state.tableData.selection.map((a) => {
					return a.id;
				});
				userDelegateApi.DeleteMany(items).then((rs) => {
					if (rs.data == 0) {
						ElMessage.error(rs.resultMsg);
						return;
					}
					onInit();
				});
			});
		};
		//详细信息
		const onDetail = (row: any) => {
			state.detailObj = {};

			userDelegateApi.Detail(row.id).then((rs) => {
				state.detailObj = rs.data;
				state.infoDrawer = true;
			});
		};
		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection;
		};
		//打印
		const onPrint = () => {
			print(printMain.value);
		};
		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};
		// 页面加载时
		onMounted(() => {
			onInit();
		});
		const onoff = (isopen: boolean, id: number) => {
			userDelegateApi.UpdateIsApprove({ isApprove: +isopen, id: id }).then((rs) => {
				if (rs.data == false) {
					onInit();
					ElMessage.error('Please enter the approve date.');
				}
			});
		};
		const filterNodeMethod = (value: string, data: Tree) => {
			return data.label.toLowerCase().includes(value.toLowerCase());
		};

		//批量OnOff
		const onDelegateOnOff = (val: boolean) => {
			ElMessageBox.confirm(
				t('message.page.dlgDeleteSelectText3') + ` ${state.tableData.selection.length} ` + t('message.page.dlgDeleteSelectText2'),
				t('message.page.dlgTip'),
				{
					type: 'warning',
					closeOnClickModal:false,
				}
			)
				.then(() => {
					const objOnOff = {
						items: {},
						onOff: false,
					};

					var items = state.tableData.selection.map((a) => {
						return a.id;
					});

					objOnOff.items = items;
					objOnOff.onOff = val;

					userDelegateApi.UpdateIsApproveBatch(objOnOff).then((rs) => {
						if (rs.data == 0) {
							ElMessage.error('Please enter Start/End Date for Delegate.');
							return;
						}
						onInit();
					});
				})
				.catch((err) => {
					onOffbatch.value = !val;
				});
		};

		return {
			printMain,
			formatStrDate,
			formatDay,
			onPrint,
			createOrEditRef,
			delegateAuditRef,
			selectionChange,
			onInit,
			onAdd,
			onEdit,
			onDetail,
			onDelete,
			onDeleteByList,
			onSizechange,
			onCurrentChange,
			onSearch,
			onSearchReSet,
			closeDialog,
			openDialog,
			onDelegateAudit,
			...toRefs(state),
			onoff,
			filterNodeMethod,
			currentUser,
			onDelegateOnOff,
			onOffbatch,
		};
	},
});
</script>
