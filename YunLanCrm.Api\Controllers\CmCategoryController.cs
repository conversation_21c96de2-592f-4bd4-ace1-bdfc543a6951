﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using SqlSugar;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmCategory;
using YunLanCrm.Common.Helper;
using YunLanCrm.Dto.User;
using YunLanCrm.Services;
using SkyWalking.NetworkProtocol.V3;
using YunLanCrm.Dto.DictItem;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmCategoryController : ControllerBase
    {
        private readonly ILogger<CmCategoryInfo> _logger;
        private readonly ICmCategoryService _cmCategoryService;
        private readonly ICmTicketsService _cmTicketsService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmCategoryService"></param>
        /// <param name="cmTicketsService"></param>
        /// <param name="logger"></param>
        public CmCategoryController(ICmCategoryService cmCategoryService,
                                    ICmTicketsService cmTicketsService,
                                    ILogger<CmCategoryInfo> logger)
        {
            _logger = logger;
            _cmCategoryService = cmCategoryService;
            _cmTicketsService = cmTicketsService;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CmCategoryAddOrUpdateDto req)
        {
            return await _cmCategoryService.AddCmCategory(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmCategoryAddOrUpdateDto req)
        {
            return await _cmCategoryService.UpdateCmCategory(req);
        }

        [HttpPost("UpdateCmCategoryCode")]
        public async Task<bool> UpdateCmCategoryCode(DictItemValueDto req)
        {
            return await _cmCategoryService.UpdateCmCategoryCode(req);
        }
        
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            var detail = await _cmCategoryService.Detail(id);
            bool exists = await _cmTicketsService.HasCurrenlyInUseByCategoryCode(detail);

            int result = 0;

            if (!exists)
            {
                detail.IsDeleted = true;
                return await _cmCategoryService.UpdateAsync(detail);
            }
            else
            {
                throw new Exception("You're deleting a record that has been applied to a ticket,please click 'Ok' to cancel this action");
            }
        }

        [HttpPost("DeleteCategory")]
        public async Task<bool> DeleteCategory(CmCategoryDetailDto req)
        {
            bool exists = await _cmTicketsService.HasCurrenlyInUseByCategoryCode(req);

            if (!exists)
            {
                return await _cmCategoryService.DeleteCategory(req);
            }
            else
            {
                throw new Exception("You're deleting a record that has been applied to a ticket,please click 'Ok' to cancel this action");
            }
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _cmCategoryService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }

        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmCategoryDto> Get(long id)
        {
            return await _cmCategoryService.QueryInfo<CmCategoryDto>(a => a.Id == id);
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmCategoryDto>> GetList([FromQuery] CmCategoryListQueryDto req)
        {
            return await _cmCategoryService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<CmCategoryDetailDto> Detail(long id)
        {
            return await _cmCategoryService.Detail(id);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("GetDetailByIdAndCompanyId/{id}/{companyId}")]
        public async Task<CmCategoryDetailDto> GetDetailByIdAndCompanyId(long id, string companyId)
        {
            return await _cmCategoryService.GetDetailByIdAndCompanyId(id, 0, SqlFunc.ToInt64(companyId));
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("GetDetailByIdAndCompanyId")]
        public async Task<CmCategoryDetailDto> GetDetailByIdAndCompanyId(CmCategoryDetailDto req)
        {
            return await _cmCategoryService.GetDetailByIdAndCompanyId_V2(req);
        }

        [HttpPost("GetCategoryByUserId")]
        public async Task<List<CmCategoryDto>> GetListByUserId(CmCategoryListQueryDto req)
        {
            return await _cmCategoryService.GetListByUserId(req);
        }

        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmCategoryDetailDto>> DetailList([FromQuery] CmCategoryListQueryDto req)
        {
            return await _cmCategoryService.DetailList(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmCategoryDetailDto>> Query([FromQuery] CmCategoryPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmCategoryDetailDto>> QueryPost(CmCategoryPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmCategoryDetailDto>> PageQuery([FromQuery] CmCategoryPageQueryDto req)
        {
            return await _cmCategoryService.PageQueryView_V2(req);
        }

        [HttpPost("QueryForAddCompany")]
        public async Task<List<CmCategoryDetailDto>> QueryForAddCompany(CmCategoryPageQueryDto req)
        {
            return await PageQueryForAddCompany(req);
        }

        private async Task<List<CmCategoryDetailDto>> PageQueryForAddCompany([FromQuery] CmCategoryPageQueryDto req)
        {
            return await _cmCategoryService.PageQueryForAddCompany(req);
        }

        [HttpPost("Export")]
        public async Task<IActionResult> Export(CmCategoryPageQueryDto req)
        {
            var isAllCheck = req.ischeckType == 0 ? true : false;
            return await ExcelHelper.Export<Task<PageQueryResult<CmCategoryDetailDto>>>(_cmCategoryService, typeof(ICmCategoryService).Name, "PageQueryView_V2", this, isAllCheck, req);
        }
    }
}