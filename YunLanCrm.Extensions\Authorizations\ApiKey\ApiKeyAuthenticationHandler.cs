using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Security.Claims;
using System.Text.Encodings.Web;
using System.Threading.Tasks;
using YunLanCrm.IServices;

namespace YunLanCrm.Extensions.Authorizations.ApiKey
{
    /// <summary>
    /// API Key认证处理器
    /// </summary>
    public class ApiKeyAuthenticationHandler : AuthenticationHandler<ApiKeyAuthenticationSchemeOptions>
    {
        private const string ApiKeyHeaderName = "X-API-Key";
        private readonly IApiKeyService _apiKeyService;

        public ApiKeyAuthenticationHandler(
            IOptionsMonitor<ApiKeyAuthenticationSchemeOptions> options,
            ILoggerFactory logger,
            UrlEncoder encoder,
            ISystemClock clock,
            IApiKeyService apiKeyService)
            : base(options, logger, encoder, clock)
        {
            _apiKeyService = apiKeyService;
        }

        protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            // 检查请求头中是否包含API Key
            if (!Request.Headers.TryGetValue(ApiKeyHeaderName, out var apiKeyHeaderValues))
            {
                return AuthenticateResult.Fail("API Key header missing");
            }

            var providedApiKey = apiKeyHeaderValues.FirstOrDefault();
            if (string.IsNullOrWhiteSpace(providedApiKey))
            {
                return AuthenticateResult.Fail("API Key header empty");
            }

            // 验证API Key
            var apiKeyInfo = await _apiKeyService.ValidateApiKeyAsync(providedApiKey);
            if (apiKeyInfo == null)
            {
                return AuthenticateResult.Fail("Invalid API Key");
            }

            // 检查API Key是否已过期
            if (apiKeyInfo.ExpiryDate.HasValue && apiKeyInfo.ExpiryDate.Value < DateTime.Now)
            {
                return AuthenticateResult.Fail("API Key expired");
            }

            // 检查API Key是否被禁用
            if (!apiKeyInfo.IsActive)
            {
                return AuthenticateResult.Fail("API Key disabled");
            }

            // 记录API Key使用情况
            await _apiKeyService.LogApiKeyUsageAsync(apiKeyInfo.Id, Request.Path, Request.Method);

            // 创建身份验证票据
            var claims = new[]
            {
                new Claim(ClaimTypes.Name, apiKeyInfo.Name),
                new Claim("ApiKeyId", apiKeyInfo.Id.ToString()),
                new Claim("ExternalSystem", apiKeyInfo.ExternalSystemName),
                new Claim(ClaimTypes.AuthenticationMethod, "ApiKey")
            };

            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            return AuthenticateResult.Success(ticket);
        }
    }

    /// <summary>
    /// API Key认证方案选项
    /// </summary>
    public class ApiKeyAuthenticationSchemeOptions : AuthenticationSchemeOptions
    {
        public const string DefaultScheme = "ApiKey";
        public string Scheme => DefaultScheme;
        public string AuthenticationType = DefaultScheme;
    }
}
