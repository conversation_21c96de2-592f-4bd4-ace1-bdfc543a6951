<template>
	<div class="branch-wrap">
		<el-divider></el-divider>
		<el-form label-position="top">
			<el-form-item label="条件关系">
				<el-radio-group v-model="form.conditionMode">
					<el-radio :label="1">且</el-radio>
					<el-radio :label="2">或</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-divider></el-divider>
			<el-form-item>
				<el-table :data="form.conditionList">
					<el-table-column prop="field" label="条件字段" width="180">
						<template #default="scope">
							<el-select v-model="scope.row.field" placeholder="Please Select" clearable>
								<el-option v-for="item in properties" :label="item.name" :value="item.name" :key="item.name"></el-option>
							</el-select>
						</template>
					</el-table-column>
					<el-table-column prop="operator" label="运算符" width="130">
						<template #default="scope">
							<el-select v-model="scope.row.operator" placeholder="Select">
								<el-option label="等于" value="=="></el-option>
								<el-option label="不等于" value="!=="></el-option>
								<el-option label="大于" value=">"></el-option>
								<el-option label="大于等于" value=">="></el-option>
								<el-option label="小于" value="<"></el-option>
								<el-option label="小于等于" value="<="></el-option>
								<el-option label="包含" value="include"></el-option>
								<el-option label="不包含" value="notinclude"></el-option>
							</el-select>
						</template>
					</el-table-column>
					<el-table-column prop="value" label="值">
						<template #default="scope">
							<el-input v-model="scope.row.value" placeholder="值"></el-input>
						</template>
					</el-table-column>
					<el-table-column prop="value" label="移除" width="100">
						<template #default="scope">
							<el-link type="danger" :underline="false" @click="deleteConditionList(scope.$index)">移除</el-link>
						</template>
					</el-table-column>
				</el-table>
			</el-form-item>

			<el-form-item>
				<el-button type="primary" icon="el-icon-plus" round @click="addConditionList">增加条件</el-button>
			</el-form-item>
		</el-form>
		<div style="margin-top: 20px">
			<el-button @click="drawer = false">Cancel</el-button>
			<el-button type="primary" @click="save">Save</el-button>
		</div>
	</div>
</template>

<script>
import workflowCrmApi from '/@/api/workflowCrm/index';

export default {
	props: {
		nodeData: Object,
		lf: Object || String,
		entity: String,
	},
	components: {},
	data() {
		return {
			nodeConfig: {},
			drawer: true,
			isEditTitle: false,
			index: 0,
			form: {},
			className: '',
			properties: [],
		};
	},
	watch: {
		modelValue() {
			this.nodeConfig = this.modelValue;
		},
	},
	mounted() {
		const { properties } = this.$props.nodeData;
		this.nodeConfig = properties;
		this.form.conditionMode = properties.conditionMode ?? 1;
		this.form.conditionList = properties.conditionList ?? [];
		console.log("form",this.form);
		workflowCrmApi.GetEntityProperties({ className: this.$props.entity }).then((rs) => {
			this.properties = rs.data;
		});
	},
	methods: {
		show(index) {
			this.index = index;
			this.form = {};
			this.form = JSON.parse(JSON.stringify(this.nodeConfig.conditionNodes[index]));
			this.drawer = true;
		},
		editTitle() {
			this.isEditTitle = true;
			this.$nextTick(() => {
				this.$refs.nodeTitle.focus();
			});
		},
		saveTitle() {
			this.isEditTitle = false;
		},
		save() {
			const { id } = this.$props.nodeData;
			this.$props.lf.setProperties(id, {
				...this.$data.form,
			});
			this.$emit('onClose');
		},
		addTerm() {
			let len = 1;
			if (!this.nodeConfig.conditionNodes) {
				this.nodeConfig.conditionNodes = [];
				len = this.nodeConfig.conditionNodes.length + 1;
			}
			this.nodeConfig.conditionNodes.push({
				nodeName: '条件' + len,
				type: 3,
				priorityLevel: len,
				conditionMode: 1,
				conditionList: [],
			});
		},
		delTerm(index) {
			this.nodeConfig.conditionNodes.splice(index, 1);
			if (this.nodeConfig.conditionNodes.length == 1) {
				if (this.nodeConfig.childNode) {
					if (this.nodeConfig.conditionNodes[0].childNode) {
						this.reData(this.nodeConfig.conditionNodes[0].childNode, this.nodeConfig.childNode);
					} else {
						this.nodeConfig.conditionNodes[0].childNode = this.nodeConfig.childNode;
					}
				}
				this.$emit('update:modelValue', this.nodeConfig.conditionNodes[0].childNode);
			}
		},
		reData(data, addData) {
			if (!data.childNode) {
				data.childNode = addData;
			} else {
				this.reData(data.childNode, addData);
			}
		},
		arrTransfer(index, type = 1) {
			this.nodeConfig.conditionNodes[index] = this.nodeConfig.conditionNodes.splice(index + type, 1, this.nodeConfig.conditionNodes[index])[0];
			this.nodeConfig.conditionNodes.map((item, index) => {
				item.priorityLevel = index + 1;
			});
			this.$emit('update:modelValue', this.nodeConfig);
		},
		addConditionList() {
			this.form.conditionList.push({
				label: '',
				field: '',
				operator: '==',
				value: '',
			});
		},
		deleteConditionList(index) {
			this.form.conditionList.splice(index, 1);
		},
		toText(nodeConfig, index) {
			var { conditionList } = nodeConfig.conditionNodes[index];
			if (conditionList && conditionList.length == 1) {
				const text = conditionList.map((item) => `${item.label}${item.operator}${item.value}`).join(' 和 ');
				return text;
			} else if (conditionList && conditionList.length > 1) {
				const conditionModeText = nodeConfig.conditionNodes[index].conditionMode == 1 ? '且行' : '或行';
				return conditionList.length + '个条件，' + conditionModeText;
			} else {
				if (index == nodeConfig.conditionNodes.length - 1) {
					return '其他条件进入此流程';
				} else {
					return false;
				}
			}
		},
	},
};
</script>

<style>
</style>
