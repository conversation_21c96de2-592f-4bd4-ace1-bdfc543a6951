﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.Role;
using YunLanCrm.Dto.RoleGroup;
using YunLanCrm.Dto.RoleModulePermission;
using SqlSugar;
using YunLanCrm.Common.HttpContextUser;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class RoleController : ControllerBase
    {
        private readonly ILogger<RoleInfo> _logger;

        private readonly IRoleService _roleService;
        private readonly IRoleGroupService _roleGroupService;
        private readonly IRoleModulePermissionService _roleModulePermissionService;
        private readonly IUser CurrentUser;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="roleService"></param>
        /// <param name="logger"></param>
        public RoleController(IRoleService roleService, IRoleGroupService roleGroupService, ILogger<RoleInfo> logger, IRoleModulePermissionService roleModulePermissionService, IUser currentUser)
        {
            this._roleService = roleService;
            this._logger = logger;
            this._roleGroupService = roleGroupService;
            _roleModulePermissionService = roleModulePermissionService;
            CurrentUser = currentUser;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(RoleAddDto req)
        {
            var roleId = await _roleService.AddIdentity(req.Adapt<RoleInfo>());

            int[] defaultPermissionId = new int[] { 112, 957, 975, 1037, 1036, 1025 };

            foreach (int id in defaultPermissionId)
            {
                var roleModulePermission = new RoleModulePermissionAddDto
                {
                    RoleId = SqlFunc.ToInt32(roleId),
                    ModuleId = 0,
                    PermissionId = id,
                    CreateTime = DateTime.Now,
                    ModifyTime = DateTime.Now,
                    CreateId = CurrentUser.UserId
                };

                await _roleModulePermissionService.AddIdentity(roleModulePermission.Adapt<RoleModulePermissionInfo>());
            }

            return roleId;
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(RoleEditDto req)
        {
            return await _roleService.Update(req.Adapt<RoleInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(int id)
        {

            var obj = await _roleService.QueryInfo(a => a.Id == id);
            if (obj == null)
            {
                throw new Exception("No information was found");
            }
            if (!obj.IsCanDelete)
            {
                throw new Exception("Deletion not allowed");
            }

            int result = await _roleService.Delete(a => a.Id == id);

            return result > 0;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("DeleteMany")]
        [NonAction]
        public async Task<bool> DeleteMany(object[] items)
        {
            bool result = await _roleService.DeleteByIds(items);

            return result;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<RoleDto> Get(int id)
        {
            return await _roleService.QueryInfo<RoleDto>(a => a.Id == id);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        [NonAction]
        public async Task<RoleDetailDto> Detail(int id)
        {
            var obj = await _roleService.QueryInfo(a => a.Id == id);

            if (obj != null)
            {
                return _roleService.Join(obj);
            }

            return null;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("GetRoleGroups")]
        public async Task<List<RoleGroupListDto>> GetRoleGroups([FromQuery] RoleListQueryDto req)
        {
            var where = PredicateBuilder.New<RoleGroupInfo>(true);
            if (req.GroupId != null)
            {
                where.And(a => a.GroupId == req.GroupId);
            }

            var groups = await _roleGroupService.QueryList<RoleGroupListDto>(where);

            foreach (var item in groups)
            {
                var roles = await _roleService.QueryList<RoleDto>(a => a.GroupId == item.GroupId

                                    );
                item.Roles = roles.OrderBy(a => a.OrderSort).ToList();
            }

            return groups;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<RoleListDto>> List([FromQuery] RoleListQueryDto req)
        {
            var list = new List<RoleListDto>();
            var where = PredicateBuilder.New<RoleInfo>(true);


            var orderBy = new OrderBy<RoleInfo>(req.order, req.sort);
            var data = await _roleService.QueryList(where, orderBy);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _roleService.Join(item);
                list.Add(detail.Adapt<RoleListDto>());
            }
            #endregion

            return list;
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        [NonAction]
        public async Task<PageQueryResult<RoleListDto>> Query([FromQuery] RolePageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<RoleListDto>> QueryPost(RolePageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<RoleListDto>> PageQuery([FromQuery] RolePageQueryDto req)
        {
            var list = new List<RoleListDto>();
            var where = PredicateBuilder.New<RoleInfo>(true);
            if (!string.IsNullOrWhiteSpace(req.searchKey))
            {
                //where.And(a => a.Name == req.searchKey);
            }

            var totalCount = await _roleService.CountAsync(where);
            var orderBy = new OrderBy<RoleInfo>(req.order, req.sort);
            var paging = req.pageIndex != null ? Paging.Page(req.pageIndex.Value, req.pageSize.Value) : default;
            var data = await _roleService.QueryList(where, orderBy, paging);

            #region 如果需要查询外键，请根据下面的写法
            foreach (var item in data)
            {
                var detail = _roleService.Join(item);
                list.Add(detail.Adapt<RoleListDto>());
            }
            #endregion

            return new PageQueryResult<RoleListDto>()
            {
                Data = list,
                TotalCount = totalCount,
            };
        }

    }
}