﻿
using Furion;
using Furion.DataValidation;
using Furion.DependencyInjection;
using Furion.JsonSerialization;
using Furion.UnifyResult;
using Furion.UnifyResult.Internal;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Dynamic;
using System.Threading.Tasks;
using YunLanCrm.Common.HttpContextUser;
using YunLanCrm.Common.LogHelper;
using YunLanCrm.Extensions.Exceptions;
using YunLanCrm.Extensions.Middlewares;
using YunLanCrm.Model.Api;

namespace YunLanCrm.Api.Filter
{
    /// <summary>
    /// RESTful 风格返回值
    /// </summary>
    [SuppressSniffer, UnifyModel(typeof(RESTfulResult<>))]
    public class RESTfulResultProvider : IUnifyResultProvider
    {
        private readonly ILogger<RESTfulResultProvider> _logger;
        private readonly IUser _user;

        public RESTfulResultProvider(ILogger<RESTfulResultProvider> logger, IUser user)
        {
            _logger = logger;
            _user = user;
        }

        /// <summary>
        /// 异常返回值
        /// </summary>
        /// <param name="context"></param>
        /// <param name="metadata"></param>
        /// <returns></returns>
        public IActionResult OnException(ExceptionContext context, ExceptionMetadata metadata)
        {
            //_logger.LogError(context.Exception, "Exception出错了");

            HttpRequest request = context.HttpContext.Request;

            string content = string.Empty;

            content += $"【用户名】=> {_user.Name}\r\n";
            content += "【访问的Url】=> " + Convert.ToString(request.Headers["AccessUrl"]) + Environment.NewLine;
            content += $"【异常信息】=> \r\n{context.Exception.ObjToString()}\r\n";
            content += $"【堆栈调用】=> \r\n{context.Exception.StackTrace.ObjToString()}\r\n";
            ;
            
            _logger.LogToFile(LogLevel.Error, content);

            string err = string.Empty;

            if (metadata != null && metadata.Errors != null)
            {
                // err = item.Value.FirstOrDefault();
            }

            if (context.Exception is CrmException appException)
            {
                return new JsonResult(RESTfulResult(appException.StatusCode, errors: metadata.Errors));
            }

            return new JsonResult(RESTfulResult(metadata.StatusCode, errors: metadata.Errors));
        }

        /// <summary>
        /// 成功返回值
        /// </summary>
        /// <param name="context"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public IActionResult OnSucceeded(ActionExecutedContext context, object data)
        {
            if (data != null)
            {
                var dataType = data.GetType();

                if (typeof(PageQueryResult<>).Name == dataType.Name)
                {
                    dynamic dy = data;

                    var rs = new ApisPageResult<object>
                    {
                        ResultCode = StatusCodes.Status200OK,
                        Succeeded = true,
                        Data = dy.Data,
                        TotalCount = dy.TotalCount,
                        Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                    };

                    return new JsonResult(rs);
                }
            }

            return new JsonResult(RESTfulResult(StatusCodes.Status200OK, true, data));
        }

        /// <summary>
        /// 验证失败返回值
        /// </summary>
        /// <param name="context"></param>
        /// <param name="metadata"></param>
        /// <returns></returns>
        public IActionResult OnValidateFailed(ActionExecutingContext context, ValidationMetadata metadata)
        {
            string err = string.Empty;

            if (metadata != null && metadata.ValidationResult != null)
            {
                foreach (var item in metadata.ValidationResult)
                {
                    err = item.Value.FirstOrDefault();
                    break;
                }
            }

            return new JsonResult(RESTfulResult(StatusCodes.Status400BadRequest, errors: err));
        }

        /// <summary>
        /// 特定状态码返回值
        /// </summary>
        /// <param name="context"></param>
        /// <param name="statusCode"></param>
        /// <param name="unifyResultSettings"></param>
        /// <returns></returns>
        public async Task OnResponseStatusCodes(HttpContext context, int statusCode, UnifyResultSettingsOptions unifyResultSettings)
        {
            // 设置响应状态码
            UnifyContext.SetResponseStatusCodes(context, statusCode, unifyResultSettings);

            switch (statusCode)
            {
                // 处理 401 状态码
                case StatusCodes.Status401Unauthorized:
                    await context.Response.WriteAsJsonAsync(RESTfulResult(statusCode, errors: "401 Unauthorized")
                        , App.GetOptions<JsonOptions>()?.JsonSerializerOptions);
                    break;
                // 处理 403 状态码
                case StatusCodes.Status403Forbidden:
                    await context.Response.WriteAsJsonAsync(RESTfulResult(statusCode, errors: "403 Forbidden")
                        , App.GetOptions<JsonOptions>()?.JsonSerializerOptions);
                    break;
                default: break;
            }
        }

        /// <summary>
        /// 返回 RESTful 风格结果集
        /// </summary>
        /// <param name="statusCode"></param>
        /// <param name="succeeded"></param>
        /// <param name="data"></param>
        /// <param name="errors"></param>
        /// <returns></returns>
        private static ApisResult<object> RESTfulResult(int statusCode, bool succeeded = default, object data = default, object errors = default)
        {
            return new ApisResult<object>
            {
                ResultCode = statusCode,
                Succeeded = succeeded,
                Data = data,
                ResultMsg = errors?.ObjToString(),
                ResultExtras = UnifyContext.Take(),
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };
        }

        public static bool IsPropertyExist(dynamic data, string propertyname)
        {
            if (data is ExpandoObject)
                return ((IDictionary<string, object>)data).ContainsKey(propertyname);

            return data.GetType().GetProperty(propertyname) != null;
        }
    }
}