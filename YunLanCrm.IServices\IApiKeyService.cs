using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using YunLanCrm.Model.Models;

namespace YunLanCrm.IServices
{
    /// <summary>
    /// API Key服务接口
    /// </summary>
    public interface IApiKeyService
    {
        /// <summary>
        /// 验证API Key
        /// </summary>
        /// <param name="apiKey">API Key值</param>
        /// <returns>API Key信息，如果无效则返回null</returns>
        Task<ApiKeyInfo> ValidateApiKeyAsync(string apiKey);

        /// <summary>
        /// 创建新的API Key
        /// </summary>
        /// <param name="name">API Key名称</param>
        /// <param name="externalSystemName">外部系统名称</param>
        /// <param name="description">描述</param>
        /// <param name="expiryDate">过期时间（可选）</param>
        /// <param name="allowedIPs">允许的IP地址列表（可选）</param>
        /// <param name="allowedEndpoints">允许的端点列表（可选）</param>
        /// <param name="createdByUserId">创建者用户ID</param>
        /// <returns>生成的API Key信息</returns>
        Task<(ApiKeyInfo apiKeyInfo, string plainTextKey)> CreateApiKeyAsync(
            string name,
            string externalSystemName,
            string description = null,
            DateTime? expiryDate = null,
            List<string> allowedIPs = null,
            List<string> allowedEndpoints = null,
            long createdByUserId = 1);

        /// <summary>
        /// 禁用API Key
        /// </summary>
        /// <param name="apiKeyId">API Key ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DisableApiKeyAsync(long apiKeyId);

        /// <summary>
        /// 启用API Key
        /// </summary>
        /// <param name="apiKeyId">API Key ID</param>
        /// <returns>是否成功</returns>
        Task<bool> EnableApiKeyAsync(long apiKeyId);

        /// <summary>
        /// 记录API Key使用情况
        /// </summary>
        /// <param name="apiKeyId">API Key ID</param>
        /// <param name="requestPath">请求路径</param>
        /// <param name="httpMethod">HTTP方法</param>
        /// <param name="clientIP">客户端IP</param>
        /// <param name="userAgent">用户代理</param>
        /// <param name="responseStatusCode">响应状态码</param>
        /// <param name="processingTimeMs">处理时间</param>
        /// <returns></returns>
        Task LogApiKeyUsageAsync(
            long apiKeyId,
            string requestPath,
            string httpMethod,
            string clientIP = null,
            string userAgent = null,
            int? responseStatusCode = null,
            long? processingTimeMs = null);

        /// <summary>
        /// 获取API Key列表
        /// </summary>
        /// <returns>API Key列表</returns>
        Task<List<ApiKeyInfo>> GetApiKeysAsync();

        /// <summary>
        /// 获取API Key使用统计
        /// </summary>
        /// <param name="apiKeyId">API Key ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>使用统计</returns>
        Task<List<ApiKeyUsageLog>> GetApiKeyUsageStatsAsync(long apiKeyId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// 删除API Key
        /// </summary>
        /// <param name="apiKeyId">API Key ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteApiKeyAsync(long apiKeyId);
    }
}
