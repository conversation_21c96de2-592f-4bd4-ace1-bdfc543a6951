﻿using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using YunLanCrm.Dto.WorkflowExecutionLogRecords;

namespace YunLanCrm.Api.ElsaWorkflows.Activities
{
    [Action(Category = "基础节点", DisplayName = "条件判断", Description = "条件判断", Outcomes = new[] { OutcomeNames.True, OutcomeNames.False, OutcomeNames.Done })]
    public class IFElseActivity : Elsa.Activities.ControlFlow.If
    {
        public IFElseActivity()
        {

        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            if (context.Input is WorkflowExecutionDto)
            {
                var input = context.GetInput<WorkflowExecutionDto>();

                context.Output = input;
            }

            var formData = context.GetVariable("FormData");

            return await base.OnExecuteAsync(context);
        }
    }
}
