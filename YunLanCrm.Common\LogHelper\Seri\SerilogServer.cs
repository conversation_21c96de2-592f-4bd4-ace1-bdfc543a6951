using YunLanCrm.Common.Helper;
using YunLanCrm.Serilog.Es;
using YunLanCrm.Serilog.Es.Formatters;
using Serilog;
using Serilog.Events;

using System.IO;
using Serilog.Sinks.MSSqlServer;
using System.Collections.ObjectModel;
using System.Data;
using System;
using Newtonsoft.Json;
using System.Text;
using Serilog.Filters;

namespace YunLanCrm.Common.LogHelper
{
    public class SerilogServer
    {


        /// <summary>
        /// 记录日常日志
        /// </summary>
        /// <param name="filename"></param>
        /// <param name="message"></param>
        /// <param name="info"></param>
        public static void WriteLog_BAK(string filename, string[] dataParas, bool IsHeader = true, string defaultFolder = "", bool isJudgeJsonFormat = false)
        {
            Log.Logger = new LoggerConfiguration()
                // TCPSink 集成Serilog 使用tcp的方式向elk 输出log日志  LogstashJsonFormatter 这个是按照自定义格式化输出内容
                .WriteTo.TCPSink(new LogstashJsonFormatter())
                .MinimumLevel.Debug()
                .MinimumLevel.Override("Microsoft", LogEventLevel.Error)
                //.WriteTo.File(Path.Combine($"log/Serilog/{filename}/", ".log"), rollingInterval: RollingInterval.Day, outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level}] {Message}{NewLine}{Exception}")
                .WriteTo.File(Path.Combine("Log", defaultFolder, $"{filename}.log"),
                rollingInterval: RollingInterval.Infinite,
                outputTemplate: "{Message}{NewLine}{Exception}")

                // 将日志托送到远程ES
                // docker run -p 9200:9200 -p 9300:9300 -e "discovery.type=single-node" -e ES_JAVA_OPTS="-Xms256m -Xmx256m" -d --name ES01 elasticsearch:7.2.0
                //.Enrich.FromLogContext()
                //.WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri("http://x.xxx.xx.xx:9200/"))
                //{
                //    AutoRegisterTemplate = true,
                //})
                .WriteTo.MSSqlServer(
                   connectionString: Appsettings.GetValue("Serilog:ConnectionString"),
                   tableName: Appsettings.GetValue("Serilog:TableName"),
                   batchPostingLimit: Appsettings.GetValue("Serilog:BatchPostingLimit").ObjToInt(),//批量插入数据库条数
                   period: TimeSpan.FromSeconds(5),//执行时间间隔
                   restrictedToMinimumLevel: LogEventLevel.Debug,
                   autoCreateSqlTable: true
               )
                .CreateLogger();

            var now = DateTime.Now;
            string logContent = String.Join("\r\n", dataParas);
            var isJsonFormat = true;
            if (isJudgeJsonFormat)
            {
                var judCont = logContent.Substring(0, logContent.LastIndexOf(","));
                isJsonFormat = JsonHelper.IsJson(judCont);
            }

            if (isJsonFormat)
            {
                if (IsHeader)
                {
                    logContent = (
                       "--------------------------------\r\n" +
                       DateTime.Now + "|\r\n" +
                       String.Join("\r\n", dataParas) + "\r\n"
                       );
                }
                // 展示elk支持输出4种日志级别
                Log.Information(logContent);
                //Log.Warning(logContent);
                //Log.Error(logContent);
                //Log.Debug(logContent);
            }
            else
            {
                Console.WriteLine("【JSON格式异常：】" + logContent + now.ObjToString());
            }
            Log.CloseAndFlush();
        }




        /// <summary>
        /// 记录异常日志
        /// </summary>
        /// <param name="filename"></param>
        /// <param name="message"></param>
        /// <param name="ex"></param>
        public static void WriteErrorLog(string message, Exception ex)
        {
            WriteLog("Error", message, ex);
        }


        public static void WriteApplicationLog(string[] dataParas)
        {
            WriteLog("Application", dataParas);
        }

        /// <summary>
        /// 访问日志
        /// </summary>
        /// <param name="dataParas"></param>
        public static void WriteAccessLog(string dataParas)
        {
            WriteLog("Access", dataParas);
        }
        /// <summary>
        /// 系统异常
        /// </summary>
        /// <param name="dataParas"></param>
        public static void WriteExceptionLog(string[] dataParas)
        {
            WriteLog("Exception", dataParas);
        }

        /// <summary>
        /// Sql 语句的日志
        /// </summary>
        /// <param name="obj"></param>
        public static void WriteSql(object obj)
        {
            WriteLog("Sql", obj);
        }

        public static void WriteLog(string logType, object obj, Exception ex = null, string dataKey = null)
        {
            return;
            string date = DateTime.Now.ToString("yyyy-MM-dd");//按时间创建文件夹
            string outputTemplate = "{NewLine}【{Level:u3}】{Timestamp:yyyy-MM-dd HH:mm:ss.fff}" +
            "{NewLine}#Msg#{Message:lj}" +
            "{NewLine}#Pro #{Properties:j}" +
            "{NewLine}#Exc#{Exception}" +
            new string('-', 50);//输出模板

            var sinkOpts = new MSSqlServerSinkOptions();
            sinkOpts.TableName = "LogEvent";
            sinkOpts.AutoCreateSqlTable = true;

            var columnOpts = new ColumnOptions();
            columnOpts.Store.Remove(StandardColumn.Properties);
            columnOpts.Store.Add(StandardColumn.LogEvent);
            columnOpts.LogEvent.DataLength = 2048;
            columnOpts.TimeStamp.NonClusteredIndex = true;

            columnOpts.AdditionalDataColumns = new Collection<DataColumn>
                {
                    new DataColumn {DataType = typeof (string), ColumnName = "LogType"},
                    new DataColumn {DataType = typeof (string), ColumnName = "DataKey"},
                    new DataColumn {DataType = typeof (string), ColumnName = "DataMessage"},
                };

            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                //.MinimumLevel.Override("Microsoft", LogEventLevel.Error)
                .Filter.ByExcluding(Matching.FromSource("Quartz"))
                .Enrich.WithProperty("LogType", logType)
                .Enrich.WithProperty("DataKey", dataKey)
                .WriteTo.MSSqlServer
                (
                    connectionString: Appsettings.GetValue("Serilog:ConnectionString"),
                    sinkOptions: sinkOpts,
                    columnOptions: columnOpts
                )
                .CreateLogger();

            var str = JsonConvert.SerializeObject(obj);

            // Log.Information("Logged on user {user}", user);
            if (logType == "Exception" || logType == "Error")
            {
                Log.Error(ex, str);
            }
            else
            {
                Log.Information(str);
            }
            Log.CloseAndFlush();

        }
    }
}