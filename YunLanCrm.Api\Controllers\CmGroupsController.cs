﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmGroups;
using YunLanCrm.Common.Helper;
using YunLanCrm.Dto.CmCategory;
using YunLanCrm.Services;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmGroupsController : ControllerBase
    {
        private readonly ILogger<CmGroupsInfo> _logger;
        private readonly ICmGroupsService _cmGroupsService;
        private readonly ICmGroupsUsersService _cmGroupsUsersService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmGroupsService"></param>
        /// <param name="logger"></param>
        public CmGroupsController(ICmGroupsService cmGroupsService, ICmGroupsUsersService cmGroupsUsersService, ILogger<CmGroupsInfo> logger)
        {
            _logger = logger;
            _cmGroupsService = cmGroupsService;
            _cmGroupsUsersService = cmGroupsUsersService;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CmGroupsAddOrUpdateDto req)
        {
            return await _cmGroupsService.AddCmGroups(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmGroupsAddOrUpdateDto req)
        {
            return await _cmGroupsService.UpdateCmGroups(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            bool hasExists = await _cmGroupsUsersService.HasCurrenlyInUse(id);

            int result = 0;

            if (!hasExists)
            {
                result = await _cmGroupsService.Delete(a => a.Id == id);
            }
            else
            {
                throw new Exception("You're deleting a record that has been applied to a ticket,please click 'Ok' to cancel this action");
            }

           
            return result > 0;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _cmGroupsService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmGroupsDto> Get(long id)
        {
            return await _cmGroupsService.QueryInfo<CmGroupsDto>(a => a.Id == id);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmGroupsDto>> GetList([FromQuery] CmGroupsListQueryDto req)
        {
            return await _cmGroupsService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<CmGroupsDetailDto> Detail(long id)
        {
            return await _cmGroupsService.Detail(id);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmGroupsDetailDto>> DetailList([FromQuery] CmGroupsListQueryDto req)
        {
            return await _cmGroupsService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmGroupsDetailDto>> Query([FromQuery] CmGroupsPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmGroupsDetailDto>> QueryPost(CmGroupsPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmGroupsDetailDto>> PageQuery([FromQuery] CmGroupsPageQueryDto req)
        {
            return await _cmGroupsService.PageQueryView(req);
        }

        [HttpPost("Export")]
        public async Task<IActionResult> Export(CmGroupsPageQueryDto req)
        {
            var isAllCheck = req.ischeckType == 0 ? true : false;
            return await ExcelHelper.Export<Task<PageQueryResult<CmGroupsDetailDto>>>(_cmGroupsService, typeof(ICmGroupsService).Name, "PageQueryView", this, isAllCheck, req);
        }

    }
}