﻿<template>
	<div class="list-page-layout">
		<el-container class="emailTemplate-index-container" style="height: 100%">
			<el-header style="height: auto">
				<el-card class="list-search-card" shadow="never" style="width: 100%" :body-style="{ paddingTop: '10px' }">
					<div class="list-index-search">
						<el-form >
							<el-form-item>
								<!-- <div class="modules-index-search"> -->
								<el-input v-model="tableData.param.searchKey" clearable style="max-width: 180px" @keyup.enter="onSearch"> </el-input>
								<el-button size="small" type="primary" class="ml10" @click="onSearch">
									<el-icon>
										<ele-Search />
									</el-icon>
									{{ $t('message.page.buttonSearch') }}
								</el-button>
								<!-- </div> -->
							</el-form-item>
						</el-form>
					</div>
				</el-card>
			</el-header>
			<el-header class="table_header mt5" style="display: flex">
				<div class="left-panel">
					<el-button v-auth="'emailTemplate.Create'" size="small" type="success" class="ml10" @click="onAdd">
						<el-icon>
							<ele-Plus />
						</el-icon>
						{{ $t('message.page.buttonCreate') }}
					</el-button>

					<el-button
						v-auth="'emailTemplate.BitchDelete'"
						type="danger"
						size="small"
						icon="ele-Delete"
						:disabled="tableData.selection.length == 0"
						@click="onDeleteByList"
					>
						{{ $t('message.page.buttonDelete') }}
					</el-button>
				</div>
				<div class="right-panel" v-if="false">
					<el-button size="small" class="ml10" @click="onAdd">
						<el-icon>
							<ele-Refresh />
						</el-icon>
						刷新
					</el-button>
					<el-button size="small" class="ml10" @click="onAdd">
						<el-icon>
							<ele-ArrowDownBold />
						</el-icon>
						导出
					</el-button>
					<el-button size="small" class="ml10" @click="onPrint">
						<el-icon>
							<ele-Printer />
						</el-icon>
						打印
					</el-button>
				</div>
			</el-header>
			<el-main class="nopadding" ref="printMain">
				<div class="scTable" style="height: 100%" ref="scTableMain">
					<div class="scTable-table">
						<el-table
							:data="tableData.data"
							v-loading="tableData.loading"
							height="calc(100%)"
							@selection-change="selectionChange"
							:row-style="{ height: '40px' }"
							:cell-style="{ padding: '0px' }"
							stripe border
						>
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')" :image-size="100"></el-empty>
							</template>
							<el-table-column type="selection" />

							<el-table-column :label="$t('message.emailFields.templateName')" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.name }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.emailFields.templateDescription')" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.description }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.emailFields.templateStatus')" show-overflow-tooltip>
								<template #default="{ row }">
									<el-icon v-if="row.status == 0" style="color: #67c23a">
										<ele-SuccessFilled />
									</el-icon>
									<el-icon v-if="row.status == 1" style="color: red">
										<ele-SuccessFilled />
									</el-icon>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.page.createBy')" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.createdByName }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.page.createAt')" width="200">
								<template #default="{ row }">
									<span>{{ row.createdAt }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.page.modifiedAt')" width="200">
								<template #default="{ row }">
									<span>{{ row.modifiedAt }}</span>
								</template>
							</el-table-column>

							<el-table-column fixed="right" align="left" :label="$t('message.page.Action')" width="150">
								<template #default="{ row }">
									<el-button v-if="false" size="mini" type="text" @click="onDetail(row)">{{ $t('message.page.actionsView') }}</el-button>
									<el-button v-auth="'emailTemplate.Edit'" size="mini" type="text" @click="onEdit(row)">
										{{ $t('message.page.actionsEdit') }}
									</el-button>
									<el-button v-auth="'emailTemplate.Delete'" size="mini" type="text" @click="onDelete(row)">
										{{ $t('message.page.actionsDelete') }}
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div class="scTable-page">
						<el-pagination
							@size-change="onSizechange"
							@current-change="onCurrentChange"
							:pager-count="5"
							:page-sizes="[10, 20, 30]"
							v-model:current-page="tableData.param.pageIndex"
							background
							v-model:page-size="tableData.param.pageSize"
							layout="total, sizes, prev, pager, next, jumper"
							:total="tableData.total"
							small
						>
						</el-pagination>
					</div>
				</div>

				<CreateOrEdit ref="createOrEditRef" @fetchData="onInit"></CreateOrEdit>
				<el-drawer v-model="infoDrawer" :title="$t('message.page.detailTitle')" :size="600" destroy-on-close>
					<Detail ref="detailRef" :info="detailObj"></Detail>
				</el-drawer>
			</el-main>
		</el-container>
	</div>
</template>

<script lang="ts">
//import { useRouter } from 'vue-router';
import { toRefs, reactive, onMounted, ref, defineComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate } from '/@/utils/formatTime';
import print from '/@/utils/print.js';

import emailTemplateApi from '/@/api/emailTemplate/index';
import Detail from './components/detail.vue';
import CreateOrEdit from './components/createOrEditTemp.vue';
import { useI18n } from 'vue-i18n';

export default defineComponent({
	name: 'emailTemplate',
	components: { Detail, CreateOrEdit },
	setup() {
		const { t } = useI18n();
		//const router = useRouter();
		const createOrEditRef = ref();
		const printMain = ref(null);
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'templateId',
					sort: 'desc', // asc or desc
				},
			},
			infoDrawer: false,
			detailObj: {},
		});

		//初始化
		const onInit = () => {
			state.tableData.loading = true;
			emailTemplateApi
				.Query(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
				})
				.catch((rs) => {})
				.finally(() => (state.tableData.loading = false));
		};
		//搜索
		const onSearch = () => {
			onInit();
		};

		// 添加
		const onAdd = () => {
			createOrEditRef.value.openDialog({ action: 'Create' });
			//router.push('/emailTemplate/Create');
		};
		// 修改
		const onEdit = (row: Object) => {
			createOrEditRef.value.openDialog({ action: 'Edit', templateId: row.templateId });
			//router.push('/emailTemplate/edit/' + row.templateId);
		};
		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				emailTemplateApi
					.DeleteByKey(row.templateId)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((rs) => {});
			});
		};
		//批量删除
		const onDeleteByList = () => {
			ElMessageBox.confirm(
				t('message.page.dlgDeleteSelectText1') + ` ${state.tableData.selection.length} ` + t('message.page.dlgDeleteSelectText2'),
				t('message.page.dlgTip'),
				{
					type: 'warning',
					closeOnClickModal:false,
				}
			).then(() => {
				emailTemplateApi.DeleteMany(state.tableData.selection).then((rs) => {
					if (rs.data == 0) {
						ElMessage.error(rs.resultMsg);
						return;
					}
					onInit();
				});
			});
		};
		//详细信息
		const onDetail = (row: any) => {
			state.detailObj = {};

			emailTemplateApi.Detail(row.templateId).then((rs) => {
				state.detailObj = rs.data;
				state.infoDrawer = true;
			});
		};
		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection.map((a) => {
				return a.templateId;
			});
		};
		//打印
		const onPrint = () => {
			print(printMain.value);
		};
		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};
		// 页面加载时
		onMounted(() => {
			onInit();
		});
		return {
			printMain,
			formatStrDate,
			onPrint,
			createOrEditRef,
			selectionChange,
			onInit,
			onAdd,
			onEdit,
			onDetail,
			onDelete,
			onDeleteByList,
			onSizechange,
			onCurrentChange,
			onSearch,
			...toRefs(state),
		};
	},
});
</script>
