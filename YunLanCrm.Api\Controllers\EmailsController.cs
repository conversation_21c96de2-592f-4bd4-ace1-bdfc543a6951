﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.Emails;
using YunLanCrm.Model.Dto.Emails;
using YunLanCrm.Common.Helper;
using YunLanCrm.Dto.CmGroups;
using YunLanCrm.Services;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class EmailsController : ControllerBase
    {
        private readonly ILogger<EmailsInfo> _logger;
        private readonly IEmailsService _emailsService;


        /// <summary>
        /// 
        /// </summary>
        /// <param name="emailsService"></param>
        /// <param name="logger"></param>
        public EmailsController(IEmailsService emailsService, ILogger<EmailsInfo> logger)
        {
            _logger = logger;
            _emailsService = emailsService;

        }

        /// <summary>
        /// 获取Email
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetEmails")]
        public async Task<List<EmailAddrDto>> GetEmails()
        {
            return await _emailsService.GetEmails();
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("SendEmail")]
        public async Task<long> SendEmail(EmailsAddOrUpdateDto req)
        {
            return await _emailsService.SendEmail(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(EmailsAddOrUpdateDto req)
        {
            return await _emailsService.UpdateAsync(req.Adapt<EmailsInfo>());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _emailsService.Delete(a => a.Id == id);

            return result > 0;
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _emailsService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<EmailsDto> Get(long id)
        {
            return await _emailsService.QueryInfo<EmailsDto>(a => a.Id == id);
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<EmailsDto>> GetList([FromQuery] EmailsListQueryDto req)
        {
            return await _emailsService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<EmailsDetailDto> Detail(long id)
        {
            return await _emailsService.Detail(id);
        }

        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<EmailsDetailDto>> DetailList([FromQuery] EmailsListQueryDto req)
        {
            return await _emailsService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<EmailsDetailDto>> Query([FromQuery] EmailsPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<EmailsDetailDto>> QueryPost(EmailsPageQueryDto req)
        {
            return await PageQuery(req);
        }

        [HttpGet("Autocomplete/{searchTxt}")]
        public async Task<List<string>> AutoComplete(string searchTxt)
        {
            return await _emailsService.AutoComplete(searchTxt);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<EmailsDetailDto>> PageQuery([FromQuery] EmailsPageQueryDto req)
        {
            return await _emailsService.PageQueryView(req);
        }



        [HttpPost("Export")]
        public async Task<IActionResult> Export(EmailsPageQueryDto req)
        {
            var isAllCheck = req.ischeckType == 0 ? true : false;
            return await ExcelHelper.Export<Task<PageQueryResult<EmailsDetailDto>>>(_emailsService, typeof(IEmailsService).Name, "PageQueryView", this, isAllCheck, req);
        }

    }
}