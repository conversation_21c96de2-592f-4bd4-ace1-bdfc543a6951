<template>
	<div class="email-content">
		<el-card>
			<template #header>Change My Password</template>
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-position="top" label-width="130px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item :label="$t('message.profileFields.currentPassword')" prop="currentPassword"
							class="form-control">
							<el-input v-model="ruleForm.currentPassword"
								:placeholder="$t('message.profilePlaceholder.currentPassword')"
								:type="isShowCurrentPassword ? 'text' : 'password'" autocomplete="off"
								style="width: 500px">
								<template #suffix>
									<el-icon @click="isShowCurrentPassword = !isShowCurrentPassword">
										<template v-if="isShowCurrentPassword">
											<img src="/img/show.svg" />
										</template>
										<template v-else>
											<img src="/img/hide.svg" />
										</template>
									</el-icon>
								</template>
							</el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item :label="$t('message.profileFields.newPassword')" prop="newPassword"
							class="form-control">
							<el-input v-model="ruleForm.newPassword"
								:placeholder="$t('message.profilePlaceholder.newPassword')"
								:type="isShowNewPassword ? 'text' : 'password'" autocomplete="off" style="width: 500px">
								<template #suffix>
									<el-icon @click="isShowNewPassword = !isShowNewPassword">
										<template v-if="isShowNewPassword">
											<img src="/img/show.svg" />
										</template>
										<template v-else>
											<img src="/img/hide.svg" />
										</template>
									</el-icon>
								</template>
							</el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item :label="$t('message.profileFields.confirmNewPassword')" prop="confirmNewPassword"
							class="form-control">
							<el-input v-model="ruleForm.confirmNewPassword"
								:placeholder="$t('message.profilePlaceholder.confirmNewPassword')"
								:type="isShowConfirmPassword ? 'text' : 'password'" autocomplete="off"
								style="width: 500px">
								<template #suffix>
									<el-icon @click="isShowConfirmPassword = !isShowConfirmPassword">
										<template v-if="isShowConfirmPassword">
											<img src="/img/show.svg" />
										</template>
										<template v-else>
											<img src="/img/hide.svg" />
										</template>
									</el-icon>
								</template>
							</el-input>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item>
							<el-alert
								title="Must be a minimum of 8 characters in length and contain a combination of at least two of the following:"
								type="error" :closable="false">
								<div>
									<p>1. A mix of uppercase and lowercase letters</p>
									<p>2. Special character (~ ! @ # $ % ^ & * ( ) _ +)</p>
									<p>3. Number (numeric value)</p>
								</div>
							</el-alert>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item class="el-form-item_msg">
							<ul class="message-list">
								<li v-for="message in messages" :key="message.msg"
									:class="`message-item ${message.type === 'error' ? 'error' : ''}`">
									<span class="dot"></span> {{ message.msg }}
								</li>
							</ul>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item class="el-form-item_btn">
							<el-button :loading="saveLoading" color="#626aef" type="primary" @click="onSubmit">{{
								$t('message.profileButtons.ChangePassword')
							}}</el-button>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent, watch, getCurrentInstance } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { checkEmail } from '/@/utils/toolsValidate';
import { useI18n } from 'vue-i18n';
import userApi from '/@/api/user';
import { IApiResultMessage } from '/@/types/models';

export default defineComponent({
	name: 'password',
	props: {
		info: Object,
	},
	setup(props, context) {
		const { t } = useI18n();
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			isShowCurrentPassword: false,
			isShowNewPassword: false,
			isShowConfirmPassword: false,
			saveLoading: false,
			messages: [] as IApiResultMessage[], // 初始为空数组
			ruleForm: {
				id: 0,
				currentPassword: '',
				newPassword: '',
				confirmNewPassword: '',
			},
			rules: {
				currentPassword: [{ required: true, message: t('message.validates.current_password_required'), trigger: 'blur' }],
				newPassword: [
					{ required: true, message: t('message.validates.new_password_required'), trigger: 'blur' },
					{
						pattern:
							/^(?=.*[a-z])(?=.*[A-Z])(?=.*(\d|[~!@#$%^&*()_+,]))[A-Za-z\d~!@#$%^&*()_+,]{7,20}$|^(?=.*\d)(?=.*[~!@#$%^&*()_+,])[A-Za-z\d~!@#$%^&*()_+,]{7,20}$/,
						message: t('message.validates.password_complexity'),
						trigger: ['blur'],
					},
				],
				confirmNewPassword: [
					{
						required: true,
						message: t('message.validates.confirm_new_password_required'),
						trigger: 'blur',
					},
					{
						validator: (rule, value, callback) => {
							if (value !== state.ruleForm.newPassword) {
								callback(new Error(t('message.validates.password_mismatch')));
							} else {
								callback();
							}
						},
						trigger: ['blur'],
					},
				],
			},
		});

		watch(
			() => props.info,
			(newVal: any, oldVal) => {
				state.ruleForm.id = newVal.id;
			}
		);
		const addMessage = (msg: string, type: 'success' | 'error' | 'info' = 'error') => {
			state.messages.push({ msg, type });
		};

		const onSubmit = () => {
			state.messages = [];
			proxy.$refs.ruleFormRef.validate((valid: any, invalidFields: any) => {
				if (!valid) {
					Object.keys(invalidFields).forEach((field) => {
						console.log('invalidFields', invalidFields);
						invalidFields[field].forEach((error: any) => {
							let msg = error.message;
							const errorMessage = msg || `${field} is invalid`;
							addMessage(errorMessage, 'error');
						});
					});
					return;
				}

				if (state.ruleForm.newPassword != state.ruleForm.confirmNewPassword) {
					addMessage(t('message.profileValidates.passwordsDoNotMatch'), 'error');
					return;
				}

				var obj = {
					id: state.ruleForm.id,
					OldPassword: state.ruleForm.currentPassword,
					NewPassword: state.ruleForm.newPassword,
				};

				//obj.NewPassword="123456";

				state.saveLoading = true;

				userApi
					.ChangePassword(obj)
					.then((rs) => {
						ElMessage.success('Succeed');
						context.emit('fetchData', obj.id);
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};

		watch(
			() => state.ruleForm,
			(newRuleForm, oldRuleForm) => {
				state.messages = [];
			},
			{ deep: true }
		);

		// 页面加载时
		onMounted(() => {
			if (props.info) {
				state.ruleForm.id = props.info.id;
			}
		});
		return {
			onSubmit,
			...toRefs(state),
		};
	},
});
</script>


<style scoped>
.lpx-content {
	max-width: 1280px !important;
	padding: 1.25em 2em 3em;
	margin: 0 auto;
}

.lpx-avatar-img-lg {
	height: 144px;
	width: 144px;
	border-radius: 144px;
}

.mb-0 {
	margin-bottom: 0 !important;
}

.mb-3 {
	margin-bottom: 1.5rem !important;
}

.mt-2 {
	margin-top: 0.75rem !important;
}

.pb-3 {
	padding-bottom: 1.5rem !important;
}

.card .card-body img {
	max-width: 100%;
}

.text-center {
	text-align: center !important;
}

.border-bottom {
	border-bottom: 1px solid #e8eef3;
}

.card .card-title {
	font-size: 1.25em;
	font-weight: 500;
	color: #062a44;
}

.text-muted {
	--bs-text-opacity: 1;
	color: #325168 !important;
	font-size: 12px;
}

.text-muted {
	opacity: 0.8;
}

.fw-normal {
	font-weight: 400 !important;
}

h5,
.h5 {
	font-size: 13px !important;
}

.menu_btn_wrapper {
	display: flex;
	flex-direction: column;
}

.menu_btn {
	width: 100% !important;
}

.button_container {
	margin-bottom: 10px;
}

.el-icon img {
	height: 1em;
	width: 1em;
	cursor: pointer;
}
</style>