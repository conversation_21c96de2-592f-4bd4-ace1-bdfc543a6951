﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <Authors>softlgl</Authors>
    <Copyright>softlgl</Copyright>
    <Owners>softlgl</Owners>
    <PackageProjectUrl>https://github.com/softlgl/Ocelot.Provider.Nacos</PackageProjectUrl>
    <Title>Ocelot.Provider.Nacos</Title>
    <Description>Repo for Nacos integration with Ocelot</Description>
    <Version>1.2.1</Version>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="nacos-sdk-csharp" Version="1.2.1" />
    <PackageReference Include="Ocelot" Version="17.0.0" />   
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="5.0.0" />
    <PackageReference Include="EasyCaching.InMemory" Version="1.4.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="5.0.2" />
  </ItemGroup>
</Project>
