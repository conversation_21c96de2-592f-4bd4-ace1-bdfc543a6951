﻿using YunLanCrm.AuthHelper;
using YunLanCrm.Common;
using YunLanCrm.Common.AppConfig;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YunLanCrm.Common.Seed;
using YunLanCrm.Model.Models;
using YunLanCrm.Common.Authorizations.Helpers;

namespace YunLanCrm.Extensions
{
    /// <summary>
    /// JWT权限 认证服务
    /// </summary>
    public static class Authentication_JWTSetup
    {
        public static void AddAuthentication_JWTSetup(this IServiceCollection services)
        {
            if (services == null) throw new ArgumentNullException(nameof(services));


            var symmetricKeyAsBase64 = AppSecretConfig.Audience_Secret_String;
            var keyByteArray = Encoding.ASCII.GetBytes(symmetricKeyAsBase64);
            var signingKey = new SymmetricSecurityKey(keyByteArray);
            var Issuer = Appsettings.app(new string[] { "Audience", "Issuer" });
            var Audience = Appsettings.app(new string[] { "Audience", "Audience" });

            var signingCredentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);

            // 令牌验证参数
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = signingKey,
                ValidateIssuer = true,
                ValidIssuer = Issuer,//发行人
                ValidateAudience = true,
                ValidAudience = Audience,//订阅人
                ValidateLifetime = true,
                ClockSkew = TimeSpan.FromSeconds(30),
                RequireExpirationTime = true,
            };

            // 开启Bearer认证
            services.AddAuthentication(o =>
            {
                o.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
                o.DefaultChallengeScheme = nameof(ApiResponseHandler);
                o.DefaultForbidScheme = nameof(ApiResponseHandler);
                
            })
             // 添加JwtBearer服务
             .AddJwtBearer(o =>
             {
                 o.TokenValidationParameters = tokenValidationParameters;
                 o.Events = new JwtBearerEvents
                 {
                     OnMessageReceived = context =>
                     {
                         var token = context.Request.Headers["Authorization"].ObjToString().Replace("Bearer ", "");
                         // 从数据库获取令牌信息
                         var tokenFromDb = GetTokenFromDatabase(services, token);
                         if (tokenFromDb == null)
                         {
                             //把描述添加到头部
                             context.Response.Headers.Add("Token-Error", "Token does not exist in database!");
                             //直接返回验证失败
                             context.Fail("Token does not exist in database!");
                         }
                         else if (token != tokenFromDb)
                         {
                             context.Response.Headers.Add("Token-Error", "Token does not match the one in database!");
                             context.Fail("Token does not match the one in database!");
                         }
                         //还有验证是否 过期，



                         return Task.CompletedTask;
                     },
                     OnChallenge = context =>
                     {
                         context.Response.Headers.Add("Token-Error", context.ErrorDescription);
                         return Task.CompletedTask;
                     },
                     OnAuthenticationFailed = context =>
                     {
                         var jwtHandler = new JwtSecurityTokenHandler();
                         var token = context.Request.Headers["Authorization"].ObjToString().Replace("Bearer ", "");

                         if (token.IsNotEmptyOrNull() && jwtHandler.CanReadToken(token))
                         {
                             var jwtToken = jwtHandler.ReadJwtToken(token);

                             if (jwtToken.Issuer != Issuer)
                             {
                                 context.Response.Headers.Add("Token-Error-Iss", "issuer is wrong!");
                             }

                             if (jwtToken.Audiences.FirstOrDefault() != Audience)
                             {
                                 context.Response.Headers.Add("Token-Error-Aud", "Audience is wrong!");
                             }
                         }


                         // 如果过期，则把<是否过期>添加到，返回头信息中
                         if (context.Exception.GetType() == typeof(SecurityTokenExpiredException))
                         {
                             context.Response.Headers.Add("Token-Expired", "true");
                         }
                         return Task.CompletedTask;
                     }
                 };
             })
             .AddScheme<AuthenticationSchemeOptions, ApiResponseHandler>(nameof(ApiResponseHandler), o => { })
             .AddApiKeyAuthentication(); // 添加API Key认证方案

        }

        /// <summary>
        /// 用于切掉用户已删除但他还登录在系统中的情况，强制Token过期
        /// </summary>
        /// <param name="services"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        private static string GetTokenFromDatabase(this IServiceCollection services, string token)
        {
            if (!string.IsNullOrEmpty(token))
            {
                var myContext = services.BuildServiceProvider().GetService<MyContext>();

                var onlineInfo = myContext.Db.Queryable<OnlineUserInfo>().Where(a => a.Token == token).First();
                
                if (onlineInfo != null)
                {
                    var tokenObj = JwtHelper.SerializeJwt(token);

                    if(tokenObj.ExpireDate.Year >= 2023 && DateTime.Now > tokenObj.ExpireDate)
                    {
                        return null;
                    }

                    onlineInfo.ModifyTime = DateTime.Now;

                    //更新token过期时间
                    myContext.Db.Updateable(onlineInfo).ExecuteCommand();

                    return token;
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return token;
            }
        }
    }
}
