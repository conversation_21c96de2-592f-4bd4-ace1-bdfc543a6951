<template>
	<div class="system-edit-menu-container">
		<el-dialog :title="title" v-model="isShowDialog" width="500px" :close-on-click-modal="false" draggable>
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="130px" label-position="top">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.groupFields.name')" prop="group_Name">
							<el-input v-model="ruleForm.group_Name" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.groupFields.description')" prop="group_Description">
							<el-input v-model="ruleForm.group_Description" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.groupFields.status')" prop="group_Status">
							<el-select v-model="ruleForm.group_Status"
								:placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100">
								<el-option :label="$t('message.userFields.Active')" :value="false"></el-option>
								<el-option :label="$t('message.userFields.Inactive')" :value="true"></el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.groupFields.Default')" prop="default_Group">
							<el-switch v-model="ruleForm.default_Group" class="ml-2"
								:label="$t('message.groupFields.Default')" />

							<!-- <el-text class="mx-1" type="warning">{{ $t('message.groupFields.DefaultText') }}</el-text> -->
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.userFields.escalation')" prop="escalation">
							<el-select v-model="ruleForm.escalation" clearable filterable
								:placeholder="$t('message.page.selectKeyPlaceholder')" class="w100">
								<el-option v-for="item in users" :key="parseInt(item.nodeId)" :label="item.label"
									:value="parseInt(item.nodeId)" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.groupFields.users')" prop="usersIds">
							<el-tree-select :placeholder="$t('message.page.selectKeyPlaceholder')" ref="groupTree"
								v-model="ruleForm.usersIds" :data="users" multiple :render-after-expand="false"
								show-checkbox style="width: 100%" node-key="value" check-strictly check-on-click-node
								filterable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
					<el-button :loading="loading" type="primary" @click="onSubmit" size="small">{{
						$t('message.page.buttonSave') }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<script lang="ts">
import { reactive, toRefs, getCurrentInstance, ref, watch } from 'vue';
import { getElcascaderSingle } from '/@/utils';
import { ElMessageBox, ElMessage, ElTree } from 'element-plus';
import { i18n } from '/@/i18n/index';
import { useI18n } from 'vue-i18n';
import userInfoApi from '/@/api/user/index';
import groupsApi from '/@/api/cmGroups/index';

interface DialogParams {
	action: string;
	id: number;
}

export default {
	name: 'menuCreateOrEdit',
	components: {},
	setup(props, context) {
		const groupTree = ref();
		const { t } = useI18n();
		const { proxy } = getCurrentInstance() as any;
		const apiTreeRef = ref();
		const apiSelectRef = ref();
		const parantRef = ref();

		const state = reactive({
			loading: false,
			title: t('message.groupButtons.createGroup'),
			isShowDialog: false,
			DialogParams: {
				action: '',
				id: -1,
			},
			ruleForm: {
				group_Name: '',
				group_Description: '',
				group_Status: false,
				default_Group: false,
				nodeId: '',
				usersIds: [] as any,
				escalation:0,
			},
			rules: {
				group_Name: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
				group_Description: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
				group_Status: [{ required: true, message: t('message.page.selectKeyPlaceholder'), trigger: 'blur' }],
				default_Group: [{ required: false, message: t('message.page.selectKeyPlaceholder'), trigger: 'blur' }],
				usersIds: [{ required: true, message: t('message.page.selectKeyPlaceholder'), trigger: 'blur' }],
			},
			users: [],
		});
		// 打开弹窗
		const openDialog = (params: DialogParams) => {
			onInitForm();
			state.DialogParams = params;
			if (params.action == 'Create') {
				state.title = t('message.groupFields.titleCreate');
				if (state.ruleForm.escalation==0)
				{	
					state.ruleForm.escalation="";
				}
			} else if (params.action == 'Edit') {
				state.title = t('message.groupFields.titleEdit');

				//获取详细信息
				getData(params.id);
			} else {
				ElMessage.error('Parameter action cannot be empty.');
			}
		};

		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}

				var selectUserTree = groupTree.value.getCheckedNodes();

				const selectUserToIdTree = [] as any[];
				selectUserTree.map(function (obj: any, index: any, array: any) {
					if (obj.nodeType == 'UserNode') {
						selectUserToIdTree.push(obj.nodeId);
					}
				});
				if (state.ruleForm.escalation=="")
				{	
					state.ruleForm.escalation=0;
				}
				var obj = state.ruleForm;
				obj.usersIds = selectUserToIdTree;
				state.loading = true;

				groupsApi
					.Save(obj)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
						getData(state.DialogParams.id);
					})
					.finally(() => {
						state.loading = false;
					});
			});
		};
		//根据id获取完整的信息
		const getData = async (id: any) => {
			await groupsApi.Detail(id).then((rs) => {
				state.ruleForm = Object.assign({}, rs.data);
				if (state.ruleForm.escalation==0)
				{	
					state.ruleForm.escalation="";
				}
			});
		};

		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}

			state.ruleForm = {
				group_Name: '',
				group_Description: '',
				group_Status: false,
				default_Group: false,
				nodeId: '',
				escalation:0,
				usersIds: [] as any,
			};
			getUsers();
		};

		const onVisible = () => {
			apiSelectRef.value.blur(); //使选择器的输入框失去焦点，并隐藏下拉框
			apiTreeRef.value.openDialog({ multiple: false });
		};

		const getUsers = () => {
			userInfoApi.GetStaffMemberList({}).then((rs) => {
				state.users = rs.data;
			});
		};
		return {
			openDialog,
			onVisible,
			apiTreeRef,
			apiSelectRef,
			parantRef,
			closeDialog,
			onCancel,
			onSubmit,
			onInitForm,
			groupTree,
			getUsers,
			...toRefs(state),
		};
	},
};
</script>
