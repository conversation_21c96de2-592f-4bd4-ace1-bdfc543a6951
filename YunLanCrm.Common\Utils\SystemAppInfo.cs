﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YunLanCrm.Common.Utils
{
    public class SystemAppInfo
    {
        private readonly IHttpContextAccessor _accessor;

        public SystemAppInfo(IHttpContextAccessor accessor)
        {
            _accessor = accessor;
        }

        string _appUrl = null; 
        string _appSiteName = null;

        /// <summary>
        /// 获得url路径, http://localhost/aa/index.aspx 的app部分如：http://localhost/aa
        /// </summary>
        public string AppUrl
        {
            get
            {
                if (_appUrl == null)
                {
                    //_appUrl = _accessor.HttpContext.Request.Headers.Origin + AppSiteName;
                    _appUrl = Appsettings.app(new string[] { "webUrl" });
                }

                return _appUrl;
            }
        }

        /// <summary>
        /// 获得 http://localhost/aa/index.aspx 的 虚拟应用名部分如：aa
        /// </summary>
        public string AppSiteName
        {
            get
            {
                if (_appSiteName == null)
                    _appSiteName = _accessor.HttpContext.Request.Path.Equals("/")
                        ? string.Empty
                        : _accessor.HttpContext.Request.Path;

                return _appSiteName;
            }
        }
    }
}
