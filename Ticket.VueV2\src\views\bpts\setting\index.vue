<template>
	<div class="system-user-container layout-list-page layout-padding">
		<el-tabs v-model="activeName" type="border-card" style="height: 100%">
			<Auth :value="'Configuration.Site'">
			<el-tab-pane label="Site" name="basic">
				<Basic ref="basicRef" :basicObj="configData.info" />
			</el-tab-pane>
		  </Auth>
			<el-tab-pane label="Upload" name="upload" v-if="false">
				<Upload ref="uploadRef" :uploadObj="configData.upload" />
			</el-tab-pane>
			<el-tab-pane label="Security" name="security" v-if="false">
				<Token ref="tokenRef" :tokenObj="configData.token" />
			</el-tab-pane>
			<Auth :value="'Configuration.Email'">
			<el-tab-pane label="Email" name="email">
				<Third ref="thirdRef" :smsObj="configData.sms" :emailObj="configData.email" />
			</el-tab-pane>
		</Auth>
		<Auth :value="'Configuration.Toggles'">
			<el-tab-pane label="Toggles" name="toggles">
					<Toggles ref="togglesRef" :toggleObj="configData.toggles" />
			</el-tab-pane>
		</Auth>
		<Auth :value="'Configuration.Holiday'">
			<el-tab-pane label="Holiday" name="holiday">
					<Holiday ref="holidayRef" :holidayObj="configData.holiday" />
			</el-tab-pane>
		</Auth>
		</el-tabs>

	</div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, ref, defineComponent,computed } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import Basic from './components/basic.vue';
import Token from './components/token.vue';
import configApi from '/@/api/config/index';
import Third from './components/third.vue';
import Upload from './components/upload.vue';
import Toggles from './components/toggles.vue';
import Auth from '/@/components/auth/auth.vue';
import { auth, auths, authAll } from '/@/utils/authFunction';
import Holiday from './components/holiday.vue';
export default defineComponent({
	name: 'systemSetting',
	components: { Basic, Token, Third, Upload, Toggles,Auth,Holiday },
	setup() {
		const state = reactive({
			activeName: 'basic',
			configData: {
				info: {},
				token: {},
				sms: {},
				email: {},
				toggles: {},
				holiday: {},
			},
		});
		const getTabName = computed(() => {
			let tabName="basic";
			if (auth('Configuration.Site')){
				tabName="basic";
			}else if (auth('Configuration.Email')){
				tabName="email";
			}else  if (auth('Configuration.Toggles')){
				tabName="toggles";
			}
			return tabName;
		});
		// 页面加载时
		onMounted(() => {
			state.activeName=getTabName.value;
			configApi.GetConfig().then((rs) => {
				state.configData = rs.data;
			});
		});
		return {
			...toRefs(state),
		};
	},
});
</script>

<style scoped>
.config-index-container {
	padding: 10px;
}

:deep(.el-tabs__content) {
	height: 100% !important;
}
</style>
