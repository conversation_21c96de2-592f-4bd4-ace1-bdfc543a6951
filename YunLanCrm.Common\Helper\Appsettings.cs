﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using YunLanCrm.Model.Models;
using YunLanCrm.Model;
using YunLanCrm.Common.Helper;

namespace YunLanCrm.Common
{
    /// <summary>
    /// appsettings.json操作类
    /// </summary>
    public class Appsettings
    {
        public static IConfiguration Configuration { get; set; }
        static string contentPath { get; set; }

        public Appsettings(string contentPath)
        {
            string Path = "appsettings.json";

            //如果你把配置文件 是 根据环境变量来分开了，可以这样写
            //Path = $"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}.json";

            Configuration = new ConfigurationBuilder()
               .SetBasePath(contentPath)
               .Add(new JsonConfigurationSource { Path = Path, Optional = false, ReloadOnChange = true })//这样的话，可以直接读目录里的json文件，而不是 bin 文件夹下的，所以不用修改复制属性
               .Build();
        }

        public Appsettings(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        /// 封装要操作的字符
        /// </summary>
        /// <param name="sections">节点配置</param>
        /// <returns></returns>
        public static string app(params string[] sections)
        {
            try
            {

                if (sections.Any())
                {
                    return Configuration[string.Join(":", sections)];
                }
            }
            catch (Exception) { }

            return "";
        }

        /// <summary>
        /// 递归获取配置信息数组
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="sections"></param>
        /// <returns></returns>
        public static List<T> app<T>(params string[] sections)
        {
            List<T> list = new List<T>();
            // 引用 Microsoft.Extensions.Configuration.Binder 包
            Configuration.Bind(string.Join(":", sections), list);
            return list;
        }


        /// <summary>
        /// 根据路径  configuration["App:Name"];
        /// </summary>
        /// <param name="sectionsPath"></param>
        /// <returns></returns>
        public static string GetValue(string sectionsPath)
        {
            try
            {
                return Configuration[sectionsPath];
            }
            catch (Exception) { }

            return "";
        }

        /// 获取指定配置部分并映射到指定类型
        /// </summary>
        /// <typeparam name="T">要映射的类型</typeparam>
        /// <param name="sectionName">配置部分名称</param>
        /// <returns>映射到的对象</returns>
        public static T GetSection<T>(string sectionName) where T : new()
        {
            var section = new T();
            Configuration.GetSection(sectionName).Bind(section);
            return section;
        }

        public static List<ConfigInfo> ToDbList<T>(T obj, List<ConfigInfo> configDbList) where T : class
        {
            var BackendKey = Appsettings.GetValue("Encryption:BackendKey");
            var BackendIV = Appsettings.GetValue("Encryption:BackendIV");

            List<ConfigInfo> list = new List<ConfigInfo>();

            //var propertys = base.GetType().GetProperties();

            var properties = typeof(T).GetProperties();

            foreach (var item in properties)
            {
                //var subItem = item.GetValue(this, null);
                var subItem = item.GetValue(obj, null);

                if (subItem == null || subItem.GetType().GetProperties() == null)
                {
                    continue;
                }

                var items = subItem.GetType().GetProperties();

                if (items != null)
                {
                    foreach (var data in items)
                    {
                        string value = data.GetValue(subItem, null)?.ToString() ?? "";

                        if (
                            ((item.Name == "Sftp" && data.Name == "Password") || (item.Name == "Email" && data.Name == "Password"))
                            && !string.IsNullOrEmpty(value)
                        )
                        {
                            if (value == Consts.DefaultEmptyPassword)
                            {
                                var dbValue = configDbList.Where(a => a.TypeId == item.Name && a.Name == data.Name).Select(a => a.Value).FirstOrDefault();

                                try
                                {
                                    value = AesEncryptionHelper.Decrypt(dbValue, BackendKey, BackendIV);
                                }
                                catch (Exception)
                                {
                                }
                            }

                            value = AesEncryptionHelper.Encrypt(value, BackendKey, BackendIV);
                        }

                        ConfigInfo configInfo = new ConfigInfo();

                        configInfo.Name = data.Name;

                        configInfo.Value = value;

                        if (configInfo.Value == null)
                        {
                            configInfo.Value = "";
                        }

                        configInfo.TypeId = item.Name;

                        list.Add(configInfo);
                    }
                }

            }

            return list;
        }
    }
}
