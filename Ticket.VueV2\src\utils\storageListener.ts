import { App, nextTick } from 'vue';
import { Router } from 'vue-router';
import { Local, Session } from './storage';
import mittBus from '/@/utils/mitt';

export function setupStorageListener(router: Router) {
	window.addEventListener('storage', async function (event: any) {
		let userInfoKey = Local.setKey('userInfo');
		let needToChangePwdKey = Local.setKey('needToChangePwd');
		let tagsKey = 'tagsViewList';
		if (event.key === userInfoKey || event.key === needToChangePwdKey) {
			//console.log(event.key + '改变触发了addEventListener');
			Session.remove(tagsKey);
			mittBus.emit('onCurrentContextmenuClick', {
				contextMenuClickId: 0,
				path: '/Dashboard',
				meta: {},
				name: '',
			});
			if (router.currentRoute.value.path === '/login') {
				location.reload();
			} else {
				router.push('/login');
			}
		}
	});
}
