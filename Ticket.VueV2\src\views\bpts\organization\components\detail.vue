﻿<template>
    <el-main style="padding:0 20px;">
        <el-descriptions :column="1" border size="small">
           
 <el-descriptions-item label="orgId">{{ info.orgId }}</el-descriptions-item>
 <el-descriptions-item label="orgNumber">{{ info.orgNumber }}</el-descriptions-item>
 <el-descriptions-item label="name">{{ info.name }}</el-descriptions-item>
 <el-descriptions-item label="parentId">{{ info.parentId }}</el-descriptions-item>
 <el-descriptions-item label="postCode">{{ info.postCode }}</el-descriptions-item>
 <el-descriptions-item label="address">{{ info.address }}</el-descriptions-item>
 <el-descriptions-item label="phone">{{ info.phone }}</el-descriptions-item>
 <el-descriptions-item label="email">{{ info.email }}</el-descriptions-item>
 <el-descriptions-item label="fax">{{ info.fax }}</el-descriptions-item>
 <el-descriptions-item label="linkTel">{{ info.linkTel }}</el-descriptions-item>
 <el-descriptions-item label="logo">{{ info.logo }}</el-descriptions-item>
 <el-descriptions-item label="region">{{ info.region }}</el-descriptions-item>
 <el-descriptions-item label="regions">{{ info.regions }}</el-descriptions-item>
 <el-descriptions-item label="sort">{{ info.sort }}</el-descriptions-item>
 <el-descriptions-item label="status">{{ info.status }}</el-descriptions-item>
 <el-descriptions-item label="description">{{ info.description }}</el-descriptions-item>
 <el-descriptions-item label="isDeleted">{{ info.isDeleted }}</el-descriptions-item>
 <el-descriptions-item label="createAt">{{ info.createAt }}</el-descriptions-item>
 <el-descriptions-item label="updateAt">{{ info.updateAt }}</el-descriptions-item>
        </el-descriptions>
    </el-main>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent } from 'vue';
import { formatStrDate } from '/@/utils/formatTime';

export default defineComponent({
    name: 'apiDetail',
    props: {
        info: Object
    },
    setup(props) {
        const state = reactive({
            isShowDialog: false,
            obj: {},
        });
        // 页面加载时
        onMounted(() => {
           window.console.log("props", props.info)
        });
        return {
            ...toRefs(state),
        };
    },
});
</script>


                        
        
        