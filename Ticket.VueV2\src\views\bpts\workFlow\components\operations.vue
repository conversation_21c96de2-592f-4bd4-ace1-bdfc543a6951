<template>
	<div class="workflow-edit-container">
		<el-timeline>
			<el-timeline-item v-for="item in operations" :key="item.nodeId" :timestamp="item.createdAt" placement="top">
				<el-card v-if="item.nodeType === 'Started'">
					<h4>{{ item.nodeName == '开始' ? 'Start' : item.nodeName }}</h4>
					<p>Initiator：{{ item.createdByName }}</p>
				</el-card>
				<el-card v-else-if="item.nodeType === WorkflowActions.APPROVE">
					<div v-if="item.isAutoPass">
						<div>
							<h4>{{ item.nodeName }}</h4>
							<p>Reviewer：{{ item.createdByName }}</p>
							<p>Review Status：<el-tag type="success">Approved</el-tag></p>
							<p>Review Date：{{ item.createdAt }}</p>
							<!-- <p>审核方式：自动通过</p> -->
							<!-- <p>审核备注：{{ item.msg }}</p> -->
						</div>
					</div>
					<!-- 只需要一个人审核 -->
					<div v-else-if="item.operationData.length == 1">
						<div v-if="item.nodeStatus === 'Pending'">
							<h4>{{ item.nodeName }}</h4>
							<p></p>
							<p>Reviewer：{{ item.operationData[0].operatorName }}</p>
							<p>Review Status：<el-tag>Pending Review</el-tag></p>
							<p v-if="item.msg">Remark：</p>
							<span v-html="item.msg"></span>
						</div>
						<div v-else>
							<Approved v-if="item.operationData[0].operationResult === WorkflowActions.APPROVE"
								:nodeData="item" />
							<Rejected v-if="item.operationData[0].operationResult === WorkflowActions.REJECTED"
								:nodeData="item" />
							<AssignTo v-if="item.operationData[0].operationResult === WorkflowActions.ASSIGNTO"
								:nodeData="item" />
							<Returned v-if="item.operationData[0].operationResult === WorkflowActions.RETURN"
								:nodeData="item" />
							<Cancel v-if="item.operationData[0].operationResult === WorkflowActions.CANCEL"
								:nodeData="item" />
						</div>
					</div>

					<!-- 如果是多个人审核的 -->
					<div v-else-if="item.operationData.length > 1">
						<ApprovedTable :nodeData="item" />
					</div>
				</el-card>

				<el-card v-else-if="item.nodeType === WorkflowActions.REVOKE">
					<div>
						<h4>执行操作</h4>
						<p></p>
						<p>执行人员：{{ item.createdByName }}</p>
						<p>执行动作： <el-tag type="danger">撤回</el-tag></p>
						<p>撤回时间：{{ item.createdAt }}</p>
						<p>撤回原因：{{ item.msg }}</p>
					</div>
				</el-card>
				<el-card v-else-if="item.nodeType === WorkflowActions.ASSIGNTO">
					<div>
						<h4>{{ item.nodeName }}</h4>
						<p></p>
						<p>执行人员：{{ item.createdByName }}</p>
						<p>执行动作： <el-tag type="info">转办</el-tag></p>
						<p>转办时间：{{ item.createdAt }}</p>
						<p>转办原因：{{ item.msg }}</p>
					</div>
				</el-card>

				<el-card v-else-if="item.nodeType === 'Cancelled'">
					<p style="color: red">流程已取消</p>
					<p>取消人{{ item.createdByName }}</p>
					<p>取消原因：{{ item.msg }}</p>
				</el-card>
				<el-card v-else-if="item.nodeType === 'FinishActivity'">
					<p>Finished</p>
				</el-card>
				<el-card v-else-if="item.nodeType === 'Resubmit'">
					<div>
						<h4>{{ item.nodeName }}</h4>
						<p></p>
						<p>状态：<el-tag>未提交</el-tag></p>
						<p>备注：{{ item.msg }}</p>
					</div>
				</el-card>
				<el-card v-else-if="item.nodeType === 'Scheduled'">
					<div>
						<h4>{{ item.nodeName }}</h4>
						<p>Reviewer：{{ item.createdByName }}</p>
						<p>Review Status：<el-tag type="success">Scheduled</el-tag></p>
						<p>Review Date：{{ item.createdAt }}</p>
						<p v-if="item.msg">Remark：</p>
						<span v-html="item.msg"></span>
					</div>
				</el-card>
				<el-card v-else>
					<div>
						<h4>{{ item.nodeName }}</h4>
						<p></p>
						<p>操作人员：{{ item.createdByName }}</p>
						<p>操作备注：{{ item.msg }}</p>
					</div>
				</el-card>
			</el-timeline-item>
		</el-timeline>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent, onMounted } from 'vue';
import workflowCrmApi from '/@/api/workflowCrm/index';
import { WorkflowActions, ElsaActivityType } from '/@/types/workflow';
import { formatStrDate } from '/@/utils/formatTime';

import Approved from '/@/views/bpts/workFlow/components/Approved.vue';
import Rejected from '/@/views/bpts/workFlow/components/Rejected.vue';
import Returned from '/@/views/bpts/workFlow/components/Returned.vue';
import AssignTo from '/@/views/bpts/workFlow/components/AssignTo.vue';
import Cancel from '/@/views/bpts/workFlow/components/Cancel.vue';
import ApprovedTable from '/@/views/bpts/workFlow/components/ApprovedTable.vue';

export default defineComponent({
	name: 'workflowOperations',
	components: { Approved, Rejected, AssignTo, Returned, ApprovedTable, Cancel },
	props: {
		crmWorkflowId: String,
		workflowInstanceId: String,
	},
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			operations: [],
		});

		onMounted(() => {
			const params = {
				crmWorkflowId: props.crmWorkflowId,
				workflowInstanceId: props.workflowInstanceId,
			};
			workflowCrmApi.GetWorkflowOperations(params).then((rs) => {
				state.operations = rs.data;
			});
		});

		return {
			WorkflowActions,
			formatStrDate,
			...toRefs(state),
		};
	},
});
</script>
