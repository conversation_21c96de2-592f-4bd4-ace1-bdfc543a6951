<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>


  <ItemGroup>
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="6.0.0" />
    <PackageReference Include="Autofac.Extras.DynamicProxy" Version="5.0.0" />
    <PackageReference Include="Confluent.Kafka" Version="1.7.0" />
   
    <PackageReference Include="Microsoft.Extensions.Logging" Version="5.0.0" />
   
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Polly" Version="7.2.1" />
    <PackageReference Include="protobuf-net" Version="3.0.101" />
    <PackageReference Include="RabbitMQ.Client" Version="6.2.1" />
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\YunLanCrm.Common\YunLanCrm.Common.csproj" />
  </ItemGroup>

</Project>
