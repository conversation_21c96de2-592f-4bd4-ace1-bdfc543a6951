﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>默认首页</title>
    <script src="http://code.jquery.com/jquery-1.8.3.min.js"></script>
    <script>
        let postdata = {
            "bID": 10,
            "bsubmitter": "222",
            "btitle": "33333",
            "bcategory": "4444",
            "bcontent": "5555",
            "btraffic": 0,
            "bcommentNum": 0,
            "bUpdateTime": "2018-11-08T02:36:26.557Z",
            "bCreateTime": "2018-11-08T02:36:26.557Z",
            "bRemark": "string"
        };
        $.ajax({
            url: "http://localhost:9291/api/Values",
            type: "POST",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify(postdata),
            dataType: 'json',
            success: function (data) {
                alert(JSON.stringify(data));
            }

        });
       
    </script>
</head>
<body>
    <button id="btn">点击我请求数据</button>
    <div id="requestMsg"></div>
    <form id="sub1" method="post" action="http://localhost:9291/api/Values">
        <input name="name" type="text" />
        <input type="submit" value="提交吧" />
    </form>
</body>
</html>