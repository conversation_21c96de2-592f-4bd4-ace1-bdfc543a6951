{"$id": "1", "activities": [{"$id": "2", "activityId": "ee8adbb7-0b63-4ec9-9de1-90732616ea70", "type": "SignalReceived", "name": "TestSignalStart", "displayName": "触发流程", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "3", "name": "Signal", "expressions": {"$id": "4", "Literal": "TestSignalStart"}}], "propertyStorageProviders": {}}, {"$id": "5", "activityId": "efc7d67c-a9cb-459f-a071-f6f195259fc2", "type": "InitWorkflow", "name": "发起人", "displayName": "发起人", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "6", "name": "Content", "expressions": {"$id": "7", "Literal": "InitWorkflow"}}], "propertyStorageProviders": {}}, {"$id": "8", "activityId": "f81f939e-e202-458d-90c1-baa480b4d956", "type": "Fork", "name": "选择分支", "displayName": "选择分支", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "9", "name": "Branches", "expressions": {"$id": "10", "Json": "[\"审批节点1\",\"审批节点2\"]"}}], "propertyStorageProviders": {}}, {"$id": "11", "activityId": "6a62b396-8f0d-4f8e-be76-13757ef00b9b", "type": "Approved", "name": "审批节点1", "displayName": "审核人", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "12", "name": "Content", "expressions": {"$id": "13", "Literal": "Approved"}}], "propertyStorageProviders": {}}, {"$id": "14", "activityId": "b6f9ec43-7e0c-43f1-b505-87d2e89799f0", "type": "Approved", "name": "审批节点2", "displayName": "审核人", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "15", "name": "Content", "expressions": {"$id": "16", "Literal": "Approved"}}], "propertyStorageProviders": {}}], "connections": [{"$id": "17", "sourceActivityId": "ee8adbb7-0b63-4ec9-9de1-90732616ea70", "targetActivityId": "efc7d67c-a9cb-459f-a071-f6f195259fc2", "outcome": "Done"}, {"$id": "18", "sourceActivityId": "efc7d67c-a9cb-459f-a071-f6f195259fc2", "targetActivityId": "f81f939e-e202-458d-90c1-baa480b4d956", "outcome": "Done"}, {"$id": "19", "sourceActivityId": "f81f939e-e202-458d-90c1-baa480b4d956", "targetActivityId": "6a62b396-8f0d-4f8e-be76-13757ef00b9b", "outcome": "Done"}, {"$id": "20", "sourceActivityId": "f81f939e-e202-458d-90c1-baa480b4d956", "targetActivityId": "b6f9ec43-7e0c-43f1-b505-87d2e89799f0", "outcome": "Done"}, {"$id": "21", "sourceActivityId": "f81f939e-e202-458d-90c1-baa480b4d956", "targetActivityId": "6a62b396-8f0d-4f8e-be76-13757ef00b9b", "outcome": "审批节点1"}, {"$id": "22", "sourceActivityId": "f81f939e-e202-458d-90c1-baa480b4d956", "targetActivityId": "b6f9ec43-7e0c-43f1-b505-87d2e89799f0", "outcome": "审批节点2"}], "variables": {"$id": "23", "data": {}}, "contextOptions": {"$id": "24", "contextType": "System.Object, mscorlib", "contextFidelity": "<PERSON><PERSON><PERSON>"}, "customAttributes": {"$id": "25", "data": {}}, "channel": "Channel7575e871-eafb-4e77-97f1-27d745b7b2ad"}