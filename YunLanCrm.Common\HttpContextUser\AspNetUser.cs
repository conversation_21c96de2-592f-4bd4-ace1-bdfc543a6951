﻿using System.Collections.Generic;
using System.Drawing.Text;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace YunLanCrm.Common.HttpContextUser
{
    public class AspNetUser : IUser
    {
        private readonly IHttpContextAccessor _accessor;
        private readonly ILogger<AspNetUser> _logger;

        public AspNetUser(IHttpContextAccessor accessor, ILogger<AspNetUser> logger)
        {
            _accessor = accessor;
            _logger = logger;
        }

        public string Name => GetName();

        private string GetName()
        {
            if (IsAuthenticated() && _accessor.HttpContext.User.Identity.Name.IsNotEmptyOrNull())
            {
                return _accessor.HttpContext.User.Identity.Name;
            }
            else
            {
                if (!string.IsNullOrEmpty(GetToken()))
                {
                    var getNameType = Permissions.IsUseIds4 ? "name" : "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name";
                    return GetUserInfoFromToken(getNameType).FirstOrDefault().ObjToString();
                }
            }

            return "";
        }
        private string _email;
        public string Email
        {
            get
            {
                return GetUserInfoFromToken(ClaimTypes.Email).FirstOrDefault().ObjToString();
            }
            set
            {
                _email = value;
            }
        }
        //public int UserId => GetClaimValueByType("jti").FirstOrDefault().ObjToInt();

        private int _userId;
        public int UserId
        {
            get
            {
                return GetClaimValueByType("jti").FirstOrDefault().ObjToInt() == 0 ? _userId : GetClaimValueByType("jti").FirstOrDefault().ObjToInt();
            }
            set
            {
                _userId = value;
            }
        }

        private string _userType;

        public string UserType
        {
            get
            {
                // 如果私有字段 _userType 为空，则尝试从 claims 中获取
                if (string.IsNullOrEmpty(_userType))
                {
                    _userType = GetClaimValueByType("UserType").FirstOrDefault()?.ObjToString();
                }
                return _userType;
            }
            set
            {
                _userType = value;
            }
        }


        private string _groupIds;

        public string GroupIds
        {
            get
            {
                // 如果私有字段 _groupIds 为空，则尝试从 claims 中获取
                if (string.IsNullOrEmpty(_groupIds))
                {
                    _groupIds = GetClaimValueByType("GroupIds").FirstOrDefault()?.ObjToString();
                }
                return _groupIds;
            }
            set
            {
                _groupIds = value;
            }
        }


        private List<int> _roleIds;

        public List<int> RoleIds
        {
            get
            {
                // 如果私有字段 _roleIds 为空，则尝试从 GetRoleIds 中获取
                if (_roleIds == null)
                {
                    _roleIds = GetRoleIds();
                }
                return _roleIds;
            }
            set
            {
                _roleIds = value;
            }
        }


        private List<int> GetRoleIds()
        {
            var jwtHandler = new JwtSecurityTokenHandler();
            var token = "";

            token = GetToken();

            // token校验
            if (token.IsNotEmptyOrNull() && jwtHandler.CanReadToken(token))
            {
                JwtSecurityToken jwtToken = jwtHandler.ReadJwtToken(token);

                return (from item in jwtToken.Claims
                        where item.Type == ClaimTypes.Role
                        select SqlFunc.ToInt32(item.Value)).ToList();
            }

            return new List<int>() { };
        }

        public string ClientIP => GetClientIP();

        private string GetClientIP()
        {
            return GetClientIP(_accessor.HttpContext);
        }

        public string Device => GetDevice();

        private string GetDevice()
        {
            return _accessor.HttpContext.Request.Headers["User-Agent"].ObjToString();

        }

        public bool IsAuthenticated()
        {
            return _accessor.HttpContext != null && _accessor.HttpContext.User.Identity != null && _accessor.HttpContext.User.Identity.IsAuthenticated;
        }


        public string GetToken()
        {
            return _accessor.HttpContext?.Request?.Headers["Authorization"].ObjToString().Replace("Bearer ", "");
        }

        public List<string> GetUserInfoFromToken(string ClaimType)
        {
            var jwtHandler = new JwtSecurityTokenHandler();
            var token = "";

            token = GetToken();
            // token校验
            if (token.IsNotEmptyOrNull() && jwtHandler.CanReadToken(token))
            {
                JwtSecurityToken jwtToken = jwtHandler.ReadJwtToken(token);

                return (from item in jwtToken.Claims
                        where item.Type == ClaimType
                        select item.Value).ToList();
            }

            return new List<string>() { };
        }

        public IEnumerable<Claim> GetClaimsIdentity()
        {
            if (_accessor.HttpContext != null)
            {
                var claims = _accessor.HttpContext.User.Claims.ToList();
                var headers = _accessor.HttpContext.Request.Headers;
                foreach (var header in headers)
                {
                    claims.Add(new Claim(header.Key, header.Value));
                }

                return claims;
            }
            else
            {
                return null;
            }
        }

        public List<string> GetClaimValueByType(string ClaimType)
        {
            var claims = GetClaimsIdentity();

            if (claims != null)
            {
                var getClaimList = (from item in claims
                                    where item.Type == ClaimType
                                    select item.Value).ToList();

                return getClaimList;
            }
            else
            {
                return new List<string>();
            }


        }

        public static string GetClientIP(HttpContext context)
        {
            var ip = context.Request.Headers["X-Forwarded-For"].ObjToString();
            if (string.IsNullOrEmpty(ip))
            {
                ip = context.Connection.RemoteIpAddress.ObjToString();
            }
            return ip;
        }
    }
}
