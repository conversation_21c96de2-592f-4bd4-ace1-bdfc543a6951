﻿
using System;
using System.Collections.ObjectModel;
using System.Data;
using System.Text;
using Serilog;
using Serilog.Events;
using Serilog.Filters;
using Serilog.Sinks.MSSqlServer;

namespace YunLanCrm.Common.LogHelper
{
    public static class Initializer
    {
        private static string _connectionString = Appsettings.GetValue("Serilog:ConnectionString");
        private static string _tableName = Appsettings.GetValue("Serilog:TableName");

        public static LoggerConfiguration CreateLoggerConfiguration()
        {
            return new LoggerConfiguration()
                .Enrich.FromLogContext()
                .WriteTo.MSSqlServer(
                    _connectionString,
                    new MSSqlServerSinkOptions
                    {
                        TableName = _tableName,
                        AutoCreateSqlTable = true
                    },
                    sinkOptionsSection: null,
                    appConfiguration: null,
                    restrictedToMinimumLevel: LevelAlias.Minimum,
                    formatProvider: null,
                    columnOptions: BuildColumnOptions(),
                    columnOptionsSection: null,
                    logEventFormatter: null);

        }

        private static ColumnOptions BuildColumnOptions()
        {
            var columnOptions = new ColumnOptions
            {
                TimeStamp =
                {
                    ColumnName = "TimeStampUTC",
                    ConvertToUtc = true,
                },

                AdditionalColumns = new Collection<SqlColumn>
                {

                }
            };

            columnOptions.Store.Remove(StandardColumn.Properties);

            return columnOptions;
        }

        [Obsolete]
        public static void SetLoggerConfiguration(LoggerConfiguration config)
        {
            
            var sinkOpts = new MSSqlServerSinkOptions();
            sinkOpts.TableName = "LogEvent";
            sinkOpts.AutoCreateSqlTable = true;

            var columnOpts = new ColumnOptions();
            columnOpts.Store.Remove(StandardColumn.Properties);
            columnOpts.Store.Add(StandardColumn.LogEvent);
            columnOpts.LogEvent.DataLength = 2048;
            columnOpts.TimeStamp.NonClusteredIndex = true;

            columnOpts.AdditionalDataColumns = new Collection<DataColumn>
                {
                    new DataColumn {DataType = typeof (string), ColumnName = "LogType"},
                    new DataColumn {DataType = typeof (string), ColumnName = "DataKey"},
                    new DataColumn {DataType = typeof (string), ColumnName = "DataMessage"},
                };

            string date = DateTime.Now.ToString("yyyy-MM-dd");//按时间创建文件夹
            string outputTemplate = "{NewLine}【{Level:u3}】{Timestamp:yyyy-MM-dd HH:mm:ss.fff}" +
            "{NewLine}#Msg#{Message:lj}" +
            "{NewLine}#Pro #{Properties:j}" +
            "{NewLine}#Exc#{Exception}" +
            new string('-', 50);//输出模板

            ///1.输出所有restrictedToMinimumLevel：LogEventLevel类型
            config
                .MinimumLevel.Debug() // 所有Sink的最小记录级别
                 //.MinimumLevel.Override("Microsoft", LogEventLevel.Fatal) "
                .Filter.ByExcluding(Matching.FromSource("Quartz"))
                .Enrich.FromLogContext()
                .WriteTo.Console(outputTemplate: outputTemplate)
                .WriteTo.File($"log/{date}/application.log",
                       outputTemplate: outputTemplate,
                        restrictedToMinimumLevel: LogEventLevel.Information,
                        rollingInterval: RollingInterval.Day,//日志按日保存，这样会在文件名称后自动加上日期后缀
                                                             //rollOnFileSizeLimit: true,          // 限制单个文件的最大长度
                                                             //retainedFileCountLimit: 10,         // 最大保存文件数,等于null时永远保留文件。
                                                             //fileSizeLimitBytes: 10 * 1024,      // 最大单个文件大小
                        encoding: Encoding.UTF8            // 文件字符编码
                    )
                 .WriteTo.MSSqlServer
                (
                    connectionString: Appsettings.GetValue("Serilog:ConnectionString"),
                    sinkOptions: sinkOpts,
                    columnOptions: columnOpts
                )
            #region 2.按LogEventLevel.输出独立发布/单文件

        ///2.1仅输出 LogEventLevel.Debug 类型
        .WriteTo.Logger(lg => lg.Filter.ByIncludingOnly(evt => evt.Level == LogEventLevel.Debug)//筛选过滤
            .WriteTo.File($"log/{date}/{LogEventLevel.Debug}.log",
                outputTemplate: outputTemplate,
                rollingInterval: RollingInterval.Day,//日志按日保存，这样会在文件名称后自动加上日期后缀
                encoding: Encoding.UTF8            // 文件字符编码
             )
                )

        ///2.2仅输出 LogEventLevel.Error 类型
        .WriteTo.Logger(lg => lg.Filter.ByIncludingOnly(evt => evt.Level == LogEventLevel.Error)//筛选过滤
            .WriteTo.File($"log/{date}/{LogEventLevel.Error}.log",
                outputTemplate: outputTemplate,
                rollingInterval: RollingInterval.Day,//日志按日保存，这样会在文件名称后自动加上日期后缀
                encoding: Encoding.UTF8            // 文件字符编码
             )
                );

            #endregion 按LogEventLevel 独立发布/单文件
        }


    }

}

