﻿<template>
	<el-container class="logs-index-container tablelist">
		<el-main class="nopadding test" ref="printMain">
			<el-tabs v-model="activeName" type="border-card" class="test">
				<!-- <el-tab-pane label="系统日志" name="System" class="test" >
					<System ref="systemRef" />
				</el-tab-pane>  -->
				<el-tab-pane label="User Logins" name="Login" class="test">
					<Login ref="loginRef" />
				</el-tab-pane>
				<!-- <el-tab-pane label="请求日志" name="Request" >
					<Request ref="requestRef" />
				</el-tab-pane>
				<el-tab-pane label="Sql日志" name="Exceptions" >
					<Sql ref="sqlRef" />
				</el-tab-pane> -->
			</el-tabs>
		</el-main>
	</el-container>
</template>

<script lang="ts">
//import { useRouter } from 'vue-router';
import { toRefs, reactive, onMounted, ref, defineComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate } from '/@/utils/formatTime';
import print from '/@/utils/print.js';

import operateLogApi from '/@/api/operateLog/index';
import System from './components/system.vue';
import Request from './components/request.vue';
import Sql from './components/sql.vue';
import Login from './components/login.vue';

export default defineComponent({
	name: 'systemLogs',
	components: { System, Request, Sql, Login },
	//components: {Login },
	setup() {
		//const router = useRouter();
		const systemRef = ref();
		const requestRef = ref();
		const sqlRef = ref();
		const loginRef = ref();

		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'id',
					sort: 'desc', // asc or desc
				},
			},
			infoDrawer: false,
			detailObj: {},
			dateType: '今天',
			logDate: [],
			activeName: 'Login',
		});

		//初始化
		const onInit = () => {
			state.tableData.loading = true;
			// operateLogApi
			// 	.Query(state.tableData.param)
			// 	.then((rs) => {
			// 		state.tableData.data = rs.data;
			// 		state.tableData.total = rs.totalCount;
			// 	})
			// 	.catch((rs) => {})
			// 	.finally(() => (state.tableData.loading = false));
		};
		//搜索
		const onSearch = () => {
			onInit();
		};

		// 添加
		const onAdd = () => {
			createOrEditRef.value.openDialog({ action: 'Create' });
			//router.push('/operateLog/Create');
		};
		// 修改
		const onEdit = (row: Object) => {
			createOrEditRef.value.openDialog({ action: 'Edit', id: row.id });
			//router.push('/operateLog/edit/' + row.id);
		};
		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(`此操作将永久当前纪录，是否继续?`, '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				operateLogApi
					.DeleteByKey(row.id)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((rs) => {});
			});
		};
		//批量删除
		const onDeleteByList = () => {
			ElMessageBox.confirm(`确定删除选中的 ${state.tableData.selection.length} 项吗？`, '提示', {
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				operateLogApi.Delete({ keys: state.tableData.selection.join(',') }).then((rs) => {
					if (rs.data == 0) {
						ElMessage.error(rs.resultMsg);
						return;
					}
					onInit();
				});
			});
		};
		//详细信息
		const onDetail = (row: any) => {
			state.detailObj = {};

			operateLogApi.Detail(row.id).then((rs) => {
				state.detailObj = rs.data;
				state.infoDrawer = true;
			});
		};
		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection;
		};
		//打印
		const onPrint = () => {
			print(printMain.value);
		};
		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};
		// 页面加载时
		onMounted(() => {
			onInit();
		});
		return {
			systemRef,
			requestRef,
			sqlRef,
			loginRef,
			formatStrDate,
			onPrint,
			selectionChange,
			onInit,
			onAdd,
			onEdit,
			onDetail,
			onDelete,
			onDeleteByList,
			onSizechange,
			onCurrentChange,
			onSearch,
			...toRefs(state),
		};
	},
});
</script>

<style></style>
