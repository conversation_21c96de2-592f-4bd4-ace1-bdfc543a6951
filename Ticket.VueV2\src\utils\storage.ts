import Cookies from 'js-cookie';

/**
 * window.localStorage 浏览器永久缓存
 * @method set 设置永久缓存
 * @method get 获取永久缓存
 * @method remove 移除永久缓存
 * @method clear 移除全部永久缓存
 */
export const Local = {
	// 查看 v2.4.3版本更新日志
	setKey(key: string) {
		// @ts-ignore
		return `${__NEXT_NAME__}:${key}`;
	},
	// 设置永久缓存
	set<T>(key: string, val: T) {
		window.localStorage.setItem(Local.setKey(key), JSON.stringify(val));
	},
	// 获取永久缓存
	get(key: string) {
		let json = <string>window.localStorage.getItem(Local.setKey(key));
		return JSON.parse(json);
	},
	// 移除永久缓存
	remove(key: string) {
		window.localStorage.removeItem(Local.setKey(key));
	},
	// 移除全部永久缓存
	clear() {
		window.localStorage.clear();
	},
	//只移除一些关键的信息
	clearLogin() {
		const keysToKeep = ['bpts-ticket:token', 'bpts-ticket:tokenExp', 'bpts-ticket:refreshToken', 'bpts-ticket:userInfo'];

		if (window.localStorage) {
			// 遍历localStorage中的所有项
			for (let i = 0; i < localStorage.length; i++) {
				const key = localStorage.key(i)?.trim(); // 使用trim()方法去除空白字符，并且使用可选链（?.）确保不会因为null而出错

				// 如果当前的key不在保留列表中，则移除
				if (key && !keysToKeep.includes(key)) {
					localStorage.removeItem(key);
					// 因为移除项后，localStorage的长度会改变，所以需要调整索引
					i--;
				}
			}
		} else {
			console.log('localStorage is not available');
		}
	},
	//只移除Token类的信息
	clearToken() {
		const keysToRemove = ['bpts-ticket:token', 'bpts-ticket:tokenExp', 'bpts-ticket:refreshToken', 'bpts-ticket:userInfo'];

		if (window.localStorage) {
			for (let i = 0; i < localStorage.length; i++) {
				const key = localStorage.key(i)?.trim();

				if (key && keysToRemove.includes(key)) {
					localStorage.removeItem(key);
					i--;
				}
			}
		} else {
			console.log('localStorage is not available');
		}
	},
	//只移除样式缓存的信息
	clearStyle() {
		const keysToRemove = ['bpts-ticket:themeConfig', 'bpts-ticket:themeConfigStyle'];
		if (window.localStorage) {
			for (let i = 0; i < localStorage.length; i++) {
				const key = localStorage.key(i)?.trim();
				if (key && keysToRemove.includes(key)) {
					localStorage.removeItem(key);
					i--;
				}
			}
		} else {
			console.log('localStorage is not available');
		}
	},
};

/**
 * window.sessionStorage 浏览器临时缓存
 * @method set 设置临时缓存
 * @method get 获取临时缓存
 * @method remove 移除临时缓存
 * @method clear 移除全部临时缓存
 */
export const Session = {
	// 设置临时缓存
	set<T>(key: string, val: T) {
		// if (key === 'token') return Cookies.set(key, val);
		window.sessionStorage.setItem(Local.setKey(key), JSON.stringify(val));
	},
	// 获取临时缓存
	get(key: string) {
		// if (key === 'token') return Cookies.get(key);
		let json = <string>window.sessionStorage.getItem(Local.setKey(key));
		return JSON.parse(json);
	},
	// 移除临时缓存
	remove(key: string) {
		// if (key === 'token') return Cookies.remove(key);
		window.sessionStorage.removeItem(Local.setKey(key));
	},
	// 移除全部临时缓存
	clear() {
		// Cookies.remove('token');
		// Local.clearToken();
		window.sessionStorage.clear();
	},
};
