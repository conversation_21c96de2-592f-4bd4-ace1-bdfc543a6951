using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Newtonsoft.Json;
using YunLanCrm.Extensions.Exceptions;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.Net;

namespace YunLanCrm.Extensions.Middlewares
{
    public class RequestValidationMiddleware
    {
        private readonly RequestDelegate _next;

        private static readonly Regex[] InvalidPatterns =
        {
            // Check for <script> tags
            // Attackers may input: <script>alert('XSS')</script>
            // Prevent Cross-Site Scripting (XSS) attacks
            new Regex(@"<script.*?>.*?</script>", RegexOptions.IgnoreCase),

            // Check for javascript: protocol
            // Attackers may input: <a href="javascript:alert('XSS')">Click</a>
            // Prevent executing malicious code via javascript: protocol
            new Regex(@"javascript:", RegexOptions.IgnoreCase),

            // Check for event attributes (e.g., onclick=)
            // Attackers may input: <div onclick="alert('XSS')">Click</div>
            // Prevent executing malicious code via event attributes
            new Regex(@"\bon\w+\s*=\s*(['""]|(?!\s*\w+\s*=))", RegexOptions.IgnoreCase),

            // Attackers may input: document.cookie
            new Regex(@"document\.", RegexOptions.IgnoreCase),

            // Check for SQL injection (loose check for common SQL injection patterns)
            new Regex(@"(union\s+select|insert\s+into|delete\s+from|update\s+\w+\s+set|drop\s+table|drop\s+database|alter\s+table|create\s+table|exec\s+|\bxp_cmdshell\b|/\*[^*]*\*/)", RegexOptions.IgnoreCase),

            // Check for path traversal (e.g., ../../etc/passwd)
            new Regex(@"\.\.(/|\\)", RegexOptions.IgnoreCase),

            // Check for malicious file uploads (e.g., .exe, .dll)
            new Regex(@"\.(exe|dll|bat|sh|php|asp|aspx|jsp|py|pl|rb)", RegexOptions.IgnoreCase),

            // Check for encoded attacks (e.g., %3Cscript%3E)
            new Regex(@"%[0-9a-fA-F]{2}", RegexOptions.IgnoreCase),

            // Check for malicious functions (e.g., eval(), exec(), etc.)
            new Regex(@"(eval\s*\(|exec\s*\(|system\s*\(|passthru\s*\(|shell_exec\s*\(|popen\s*\(|proc_open\s*\()", RegexOptions.IgnoreCase)
        };

        public RequestValidationMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (context.Request.Method == "GET" 
                || context.Request.Path == "/Api/CmOpenItemDetailAttachment/UpLoadV2"
                || context.Request.Path == "/Api/Files/UpLoadV2"
                || context.Request.Path == "/Api/Files/Upload" 
                || context.Request.Path == "/Api/emails/SendEmail"
                || context.Request.Path == "/api/token/SSOCallback"
                )
            {
                await _next(context);
                return;
            }

            // 保存原始请求体
            var originalBody = context.Request.Body;
            var requestContent = string.Empty;

            // 读取请求内容
            using (var reader = new StreamReader(context.Request.Body))
            {
                requestContent = await reader.ReadToEndAsync();
            }

            var requestContentTemp = string.Empty;

            // 使用 System.Net.WebUtility.UrlDecode() 进行解码
            if (!string.IsNullOrEmpty(requestContent))
            {
                requestContentTemp = WebUtility.UrlDecode(requestContent);
            }

            // 验证内容
            if (!string.IsNullOrEmpty(requestContent))
            {
                // 检查是否包含危险内容
                if (ContainsDangerousContent(requestContentTemp))
                {
                    context.Response.StatusCode = 400;
                    context.Response.ContentType = "application/json";
                    await context.Response.WriteAsync("The request contains illegal content");
                    return;
                }

                // 尝试解析 JSON
                try
                {
                    var jsonObject = JsonConvert.DeserializeObject(requestContent);
                    ValidateObject(jsonObject);
                }
                catch (JsonReaderException)
                {
                    context.Response.StatusCode = 400;
                    context.Response.ContentType = "application/json";
                    await context.Response.WriteAsync("The request contains illegal content");
                    return;
                }
            }

            // 重新创建一个包含原始内容的流
            var requestStream = new MemoryStream();
            var requestWriter = new StreamWriter(requestStream);
            await requestWriter.WriteAsync(requestContent);
            await requestWriter.FlushAsync();
            requestStream.Position = 0;

            // 替换请求流
            context.Request.Body = requestStream;

            try
            {
                await _next(context);
            }
            finally
            {
                context.Request.Body = originalBody;
                requestStream.Dispose();
            }
        }

        private bool ContainsDangerousContent(string content)
        {
            return InvalidPatterns.Any(pattern => pattern.IsMatch(content));

            //var result = false;

            //foreach (var pattern in InvalidPatterns)
            //{
            //    var match = pattern.Match(content);
            //    if (match.Success)
            //    {
            //        return true;
            //    }
            //}

            //return result;
        }

        private void ValidateObject(object obj)
        {
            if (obj == null) return;

            if (obj is string str)
            {
                if (ContainsDangerousContent(str))
                {
                    throw new Exception("The request contains illegal content");
                }
                return;
            }

            if (obj is IDictionary<string, object> dict)
            {
                foreach (var value in dict.Values)
                {
                    ValidateObject(value);
                }
                return;
            }

            if (obj is IEnumerable<object> enumerable)
            {
                foreach (var item in enumerable)
                {
                    ValidateObject(item);
                }
                return;
            }

            var properties = obj.GetType().GetProperties();
            foreach (var prop in properties)
            {
                var value = prop.GetValue(obj);
                ValidateObject(value);
            }
        }
    }
}