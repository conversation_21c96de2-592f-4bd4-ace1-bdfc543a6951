﻿using System;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using YunLanCrm.Common;
using YunLanCrm.Common.Helper;
using YunLanCrm.Common.HttpContextUser;
using YunLanCrm.Common.LogHelper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace YunLanCrm.Extensions.Middlewares
{
    /// <summary>
    /// 中间件
    /// 记录用户方访问数据
    /// </summary>
    public class RecordAccessLogsMiddleware
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly RequestDelegate _next;
        private readonly IUser _user;
        private readonly ILogger<RecordAccessLogsMiddleware> _logger;
        private readonly IWebHostEnvironment _environment;
        private Stopwatch _stopwatch;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="next"></param>
        public RecordAccessLogsMiddleware(RequestDelegate next, IUser user, ILogger<RecordAccessLogsMiddleware> logger, IWebHostEnvironment environment)
        {
            _next = next;
            _user = user;
            _logger = logger;
            _environment = environment;
            _stopwatch = new Stopwatch();
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (Appsettings.app("Middleware", "RecordAccessLogs", "Enabled").ObjToBool())
            {
                var api = context.Request.Path.ObjToString().TrimEnd('/').ToLower();

                var ignoreApis = Appsettings.app("Middleware", "RecordAccessLogs", "IgnoreApis");

                // 过滤，只有接口
                if (api.Contains("api") && !ignoreApis.Contains(api) && context.Request.Method != "OPTIONS")
                {
                    _stopwatch.Restart();

                    var userAccessModel = new UserAccessModel();

                    HttpRequest request = context.Request;

                    userAccessModel.API = api;
                    userAccessModel.User = _user.Name;
                    userAccessModel.IP = IpLogMiddleware.GetClientIP(context);
                    userAccessModel.BeginTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    userAccessModel.RequestMethod = request.Method;
                    userAccessModel.Agent = request.Headers["User-Agent"].ObjToString();
                    userAccessModel.Authorization = Convert.ToString(request.Headers["Authorization"]);
                    userAccessModel.AccessUrl = Convert.ToString(request.Headers["AccessUrl"]);

                    // 获取请求body内容
                    if (request.Method.ToLower().Equals("post") || request.Method.ToLower().Equals("put"))
                    {
                        // 启用倒带功能，就可以让 Request.Body 可以再次读取
                        request.EnableBuffering();

                        Stream stream = request.Body;
                        byte[] buffer = new byte[request.ContentLength.Value];
                        stream.Read(buffer, 0, buffer.Length);
                        userAccessModel.RequestData = Encoding.UTF8.GetString(buffer);

                        request.Body.Position = 0;
                    }
                    else if (request.Method.ToLower().Equals("get") || request.Method.ToLower().Equals("delete"))
                    {
                        userAccessModel.RequestData = HttpUtility.UrlDecode(request.QueryString.ObjToString(), Encoding.UTF8);
                    }

                    // 获取Response.Body内容
                    var originalBodyStream = context.Response.Body;

                    using (var responseBody = new MemoryStream())
                    {
                        context.Response.Body = responseBody;

                        await _next(context);

                        var responseBodyData = await GetResponse(context.Response);

                        userAccessModel.ResponseData = responseBodyData;

                        await responseBody.CopyToAsync(originalBodyStream);
                    }

                    // 响应完成记录时间和存入日志
                    context.Response.OnCompleted(() =>
                    {
                        _stopwatch.Stop();

                        userAccessModel.OPTime = _stopwatch.ElapsedMilliseconds + "ms";

                        // 自定义log输出
                        var requestInfo = JsonConvert.SerializeObject(userAccessModel);

                        //Parallel.For(0, 1, e =>
                        //{
                        //    LogLock.OutSql2Log("RecordAccessLogs", new string[] { requestInfo + "," }, false);
                        //});

                        //var logFileName = FileHelper.GetAvailableFileNameWithPrefixOrderSize(_environment.ContentRootPath, "RecordAccessLogs.log");

                        //_logger.LogInformation(logFileName, new string[] { requestInfo + "," }, false);

                        //_logger.LogToFile(LogLevel.Information, requestInfo, "RecordAccessLogs.log");

                        var content = string.Empty;

                        content += $"【用户名】=> {userAccessModel.User}\r\n";
                        content += $"【用户IP】=> {userAccessModel.IP}\r\n";
                        content += $"【请求开始时间】=> {userAccessModel.BeginTime}\r\n";
                        content += $"【请求耗费时间】=> {userAccessModel.OPTime}\r\n";
                        content += $"【请求路径】=> {userAccessModel.API}\r\n";
                        content += $"【请求方式】=> {userAccessModel.RequestMethod}\r\n";
                        content += $"【请求Authorization】=> {userAccessModel.Authorization}\r\n";
                        content += $"【访问的Url】=> {userAccessModel.AccessUrl}\r\n";
                        content += $"【请求的数据】=> \r\n{userAccessModel.RequestData}\r\n";

                        if (!string.IsNullOrEmpty(content))
                        {
                            content += $"【响应数据】=> \r\n{userAccessModel.ResponseData}\r\n\r\n";
                            content += $"【原有记录的信息】=> \r\n{requestInfo}\r\n\r\n";

                            if (_stopwatch.ElapsedMilliseconds > 3000)
                            {
                                _logger.LogToFile(LogLevel.Information, content, nameof(RecordAccessLogsMiddleware) + "/LongTimeAccess/RecordAccessLogs.log");
                            }
                            else if (userAccessModel.API.IndexOf("refreshtoken", StringComparison.OrdinalIgnoreCase) > -1)
                            {
                                //_logger.LogToFile(LogLevel.Information, content, nameof(RecordAccessLogsMiddleware) + "/RefreshToken/RecordAccessLogs.log");
                            }
                            else
                            {
                                _logger.LogToFile(LogLevel.Information, content, nameof(RecordAccessLogsMiddleware) + "/UserAccess/RecordAccessLogs.log");
                            }
                        }

                        //SerilogServer.WriteAccessLog(requestInfo);

                        return Task.CompletedTask;
                    });
                }
                else
                {
                    await _next(context);
                }
            }
            else
            {
                await _next(context);
            }
        }

        /// <summary>
        /// 获取响应内容
        /// </summary>
        /// <param name="response"></param>
        /// <returns></returns>
        public async Task<string> GetResponse(HttpResponse response)
        {
            response.Body.Seek(0, SeekOrigin.Begin);
            var text = await new StreamReader(response.Body).ReadToEndAsync();
            response.Body.Seek(0, SeekOrigin.Begin);
            return text;
        }
    }

    public class UserAccessModel
    {
        public string User { get; set; }
        public string IP { get; set; }
        public string API { get; set; }
        public string BeginTime { get; set; }
        public string OPTime { get; set; }
        public string RequestMethod { get; set; }
        public string RequestData { get; set; }
        public string ResponseData { get; set; }
        public string Agent { get; set; }
        public string Authorization { get; set; }
        public string AccessUrl { get; set; }
    }
}

