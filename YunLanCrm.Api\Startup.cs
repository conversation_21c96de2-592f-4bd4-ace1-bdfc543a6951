﻿using Autofac;
using System.Text;
using System.Reflection;
using Microsoft.Extensions.Azure;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using System.IdentityModel.Tokens.Jwt;

using Elsa;
using Elsa.Activities.UserTask.Activities;
using Elsa.Persistence.EntityFramework.Core.Extensions;
using Elsa.Persistence.EntityFramework.SqlServer;
using Microsoft.AspNetCore.ResponseCompression;
using YunLanCrm.Common;
using YunLanCrm.Common.LogHelper;
using YunLanCrm.Common.Seed;
using YunLanCrm.Extensions;
using YunLanCrm.Filter;
using YunLanCrm.Hubs;
using YunLanCrm.IServices;
using YunLanCrm.Tasks;
using YunLanCrm.Api.Filter;
using YunLanCrm.Common.Helper;
using YunLanCrm.Extensions.Middlewares;
using YunLanCrm.ElsaWorkflows.Activities;
using YunLanCrm.Api.ElsaWorkflows.Activities;
using YunLanCrm.Api.ElsaWorkflows.TicketIndexing;
using System.IO.Compression;

namespace YunLanCrm
{
    public class Startup
    {
        private IServiceCollection _services;

        public Startup(IConfiguration configuration, IWebHostEnvironment env)
        {
            Configuration = configuration;
            Env = env;
        }

        /// <summary>
        /// IConfiguration 加载配置值
        /// </summary>
        public IConfiguration Configuration { get; }

        /// <summary>
        /// 提供有关正在运行应用程序的Web托管环境的信息
        /// </summary>
        public IWebHostEnvironment Env { get; }

        //此方法由运行时调用。使用此方法将服务添加到容器
        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            // 以下code可能与文章中不一样,对代码做了封装,具体查看右侧 Extensions 文件夹.
            //通常每个应用程序只创建一次 Singleton 服务，并且在整个应用程序生命周期中使用该单个实例
            services.AddSingleton(new Appsettings(Configuration));
            services.AddSingleton(new LogLock(Env.ContentRootPath));
            services.AddSingleton(new UploadHelper(Env.ContentRootPath));//文件帮助类注入
            services.AddUiFilesZipSetup(Env);

            //在范围内的每个请求中创建一个新的 Scoped 服务实例
            //services.AddScoped

            //这里是自定义的配置类 Appsettings 
            Permissions.IsUseIds4 = Appsettings.app(new string[] { "Startup", "IdentityServer4", "Enabled" }).ObjToBool();
            RoutePrefix.Name = Appsettings.app(new string[] { "AppSettings", "SvcName" }).ObjToString();

            // 确保从认证中心返回的ClaimType不被更改，不使用Map映射
            JwtSecurityTokenHandler.DefaultInboundClaimTypeMap.Clear();

            services.AddMemoryCacheSetup();

            services.AddRedisCacheSetup();

            services.AddSqlsugarSetup();

            services.AddDbSetup();

            //services.AddDbConfig();//添加配置信息

            services.AddAutoMapperSetup();

            services.AddCorsSetup();

            services.AddMiniProfilerSetup();

            // services.AddSwaggerSetup();

            services.AddJobSetup();

            services.AddHttpContextSetup();

            //services.AddAppConfigSetup(Env);

            //services.AddAppTableConfigSetup(Env);//表格打印配置

            //services.AddHttpApi();

            //services.AddRedisInitMqSetup();

            //services.AddRabbitMQSetup();

            //services.AddKafkaSetup(Configuration);

            //services.AddEventBusSetup();

            //services.AddNacosSetup(Configuration);

            // 授权+认证 (jwt or ids4)
            services.AddAuthorizationSetup();

            if (Permissions.IsUseIds4)
            {
                //services.AddAuthentication_Ids4Setup();
            }
            else
            {
                services.AddAuthentication_JWTSetup();
            }

            // 添加API Key认证支持
            services.AddApiKeyAuthenticationSetup();

            services.AddIpPolicyRateLimitSetup(Configuration);

            //services.AddSignalR().AddNewtonsoftJsonProtocol();

            services.AddScoped<UseServiceDIAttribute>();

            services.Configure<KestrelServerOptions>(x => x.AllowSynchronousIO = true)
                    .Configure<IISServerOptions>(x => x.AllowSynchronousIO = true);

            //services.AddDistributedMemoryCache();

            //services.AddSession();

            services.AddHttpPollySetup();

            services.AddControllers(o =>
            {
                // 全局异常过滤
                o.Filters.Add(typeof(GlobalExceptionsFilter));

                // 全局路由权限公约
                o.Conventions.Insert(0, new GlobalRouteAuthorizeConvention());

                // 全局路由前缀，统一修改路由
                o.Conventions.Insert(0, new GlobalRoutePrefixFilter(new RouteAttribute(RoutePrefix.Name)));
            })

            // 这种写法也可以
            //.AddJsonOptions(options =>
            //{
            //    options.JsonSerializerOptions.PropertyNamingPolicy = null;
            //})

            //MVC全局配置Json序列化处理
            .AddNewtonsoftJson(options =>
            {
                //忽略循环引用
                options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;

                //不使用驼峰样式的key
                //options.SerializerSettings.ContractResolver = new DefaultContractResolver();

                //使用驼峰样式
                options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();

                //设置时间格式
                options.SerializerSettings.DateFormatString = "MM/dd/yyyy hh:mm:ss tt";

                //忽略Model中为null的属性
                //options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;

                //设置本地时间而非UTC时间
                options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Local;

                //添加Enum转string
                options.SerializerSettings.Converters.Add(new StringEnumConverter());
            })

            .AddInjectWithUnifyResult<RESTfulResultProvider>()
            ;

            //使用 Azure
            services.AddAzureClients(builder =>
            {
                builder.AddBlobServiceClient(Configuration["AzureStorageConnectionString"]);
            });

            #region 注册Elsa
            // services.AddElsaSetup(Configuration);
            var elsaSection = Configuration.GetSection("Elsa");
            var connectionString = Appsettings.app(new string[] { "Serilog", "ConnectionString" });

            services
                    .AddElsa(elsa => elsa
                    .UseEntityFrameworkPersistence(ef => ef.UseSqlServer(connectionString))
                    .AddConsoleActivities()
                    .AddHttpActivities(elsaSection.GetSection("Server").Bind)
                    .AddEmailActivities(elsaSection.GetSection("Smtp").Bind)
                    .AddQuartzTemporalActivities()
                    .AddWorkflowsFrom<Startup>()
                    .AddActivity<UserTask>()

                    .AddActivity<TicketIndexingNew>()
                    .AddActivity<TicketIndexingOpen>()
                    .AddActivity<TicketIndexingReplied>()
                    .AddActivity<TicketIndexingSolved>()

                    .AddActivity<TicketIndexingHold>()
                    .AddActivity<TicketIndexingCancelled>()


                    .AddActivity<StartActivity>()
                    .AddActivity<FinishActivity>()
                    .AddActivity<Approved>()
                    .AddActivity<UserTaskCrm>()
                    .AddActivity<ForkCrm>()
                    .AddActivity<JoinCrm>()
                    .AddActivity<IFElseActivity>()
                  );

            //services.Decorate<IBookmarkProvider, MyBookmarkProvider>();

            // Elsa API endpoints.
            services.AddElsaApiEndpoints();

            // For Dashboard.
            services.AddRazorPages();
            #endregion

            services.AddResponseCompression(options =>
            {
                options.EnableForHttps = true;
                options.Providers.Add<BrotliCompressionProvider>();
                options.Providers.Add<GzipCompressionProvider>();
            });

            services.Configure<BrotliCompressionProviderOptions>(options =>
            {
                options.Level = CompressionLevel.Fastest;
            });

            services.Configure<GzipCompressionProviderOptions>(options =>
            {
                options.Level = CompressionLevel.SmallestSize;
            });

            //这行代码影响 Elsa 注册 去掉时 会影响IUser 注入
            //指定控制器实例让容器来创建
            //services.Replace(ServiceDescriptor.Transient<IControllerActivator, ServiceBasedControllerActivator>());

            _services = services;

            //支持编码大全 例如:支持 System.Text.Encoding.GetEncoding("GB2312")  System.Text.Encoding.GetEncoding("GB18030") 
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
        }

        // 注意在Program.CreateHostBuilder，添加Autofac服务工厂，映射实体
        public void ConfigureContainer(ContainerBuilder builder)
        {
            builder.RegisterModule(new AutofacModuleRegister());
            builder.RegisterModule<AutofacPropertityModuleReg>();
        }

        //此方法由运行时调用。使用此方法配置HTTP请求管道
        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, MyContext myContext, ITasksQzServices tasksQzServices, ISchedulerCenter schedulerCenter, IHostApplicationLifetime lifetime)
        {

            // 开启异常中间件
            //app.UseExceptionHandlerMiddle();

            // Add HTTP activities middleware.
            //app.UseHttpActivities();

            // Ip限流,尽量放管道外层
            app.UseIpLimitMiddle();

            // 记录请求与返回数据 
            app.UseRequestResponseLogMiddle();

            // 用户访问记录(必须放到外层，不然如果遇到异常，会报错，因为不能返回流)
            app.UseRecordAccessLogsMiddle();

            // signalr 
            //app.UseSignalRSendMiddle();

            // 记录ip请求
            //app.UseIpLogMiddle();

            // 查看注入的所有服务
            //app.UseAllServicesMiddle(_services);

            if (env.IsDevelopment())
            {
                // 在开发环境中，使用异常页面，这样可以暴露错误堆栈信息，所以不要放在生产环境。
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/Error");

                // 在非开发环境中，使用HTTP严格安全传输(or HSTS) 对于保护web安全是非常重要的。
                // 强制实施 HTTPS 在 ASP.NET Core，配合 app.UseHttpsRedirection
                //app.UseHsts();
            }
            
            // 请求验证中间件
            app.UseRequestValidation();

            //app.UseSession();

            // app.UseSwaggerAuthorized();

            app.UseMiddleware<SwaggerIndexMiddleware>();

            // 封装Swagger展示
            app.UseSwaggerMiddle(() => GetType().GetTypeInfo().Assembly.GetManifestResourceStream("YunLanCrm.Api.index.html"));

            // ↓↓↓↓↓↓ 注意下边这些中间件的顺序，很重要 ↓↓↓↓↓↓

            // CORS跨域
            app.UseCors(Appsettings.app(new string[] { "Startup", "Cors", "PolicyName" }));

            // 跳转https
            //app.UseHttpsRedirection();

            // 使用静态文件
            DefaultFilesOptions defaultFilesOptions = new DefaultFilesOptions();
            defaultFilesOptions.DefaultFileNames.Clear();
            defaultFilesOptions.DefaultFileNames.Add("index.html");
            app.UseDefaultFiles(defaultFilesOptions);

            app.UseStaticFiles();

            // 使用cookie
            app.UseCookiePolicy();

            // 返回错误码
            app.UseStatusCodePages();

            // Routing
            app.UseRouting();

            // RefererMiddleware 需要在 UseRouting 之后才能获取到 endpoint
            app.UseMiddleware<RefererMiddleware>();

            // 这种自定义授权中间件，可以尝试，但不推荐
            // app.UseJwtTokenAuth();

            // 测试用户，用来通过鉴权
            if (Configuration.GetValue<bool>("AppSettings:UseLoadTest"))
            {
                app.UseMiddleware<ByPassAuthMiddleware>();
            }

            // 先开启认证
            app.UseAuthentication();

            // 然后是授权中间件
            app.UseAuthorization();
            
            //开启性能分析
            //app.UseMiniProfilerMiddleware();

            app.UseInject("api");

            app.UseMiddleware<FeatureAccessMiddleware>();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute(name: "default", pattern: "{controller=Home}/{action=Index}/{id?}");

                //endpoints.MapHub<ChatHub>("/api2/chatHub");

                //endpoints.MapFallbackToPage("/_Host");
            });
           
            // 生成种子数据
            //app.UseSeedDataMiddle(myContext, Env.WebRootPath);

            // 开启QuartzNetJob调度服务
            app.UseQuartzJobMiddleware(tasksQzServices, schedulerCenter);            

            app.UseResponseCompression();

            // 服务注册
            //app.UseConsulMiddle(Configuration, lifetime);

            // 事件总线，订阅服务
            //app.ConfigureEventBus();            
        }
    }
}
