﻿export default {
	taskQueueSearch: {
        //查询区域
		searchKeyPlaceholder: '请输入关键字',
	},
	taskQueueButtons: {
    //非按钮
	},
	taskQueueFields:{
    //table 列名
         id:' ',
         name:' ',
         jobGroup:' ',
         cron:' ',
         assemblyName:' ',
         className:' ',
         remark:' ',
         runTimes:' ',
         beginTime:' ',
         endTime:' ',
         triggerType:' ',
         intervalSecond:' ',
         cycleRunTimes:' ',
         isStart:' ',
         jobParams:' ',
         isDeleted:' ',
         createTime:' ',
		
	},
    taskCommon:{        
        executeClass:'执行类',
        TimingRules:'定时规则',
        Running:'运行中',
        deactivated:'停用',
        AddingScheduledTasks:'添加计划任务',
        Taskschedulinglog:'任务调度日志',
    }
};




                        
        
        