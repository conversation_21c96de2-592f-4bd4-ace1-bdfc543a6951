<template>
	<div style="border: 1px solid #ccc; width: 100%;">
		<Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="toolbarConfig"
			:mode="mode" />
		<!-- <div style="position: relative"> --> <!-- 此div影响到点击放大--> 
			<Editor v-model="valueHtml" :defaultConfig="editorConfig" :mode="mode" @onCreated="handleCreated"
				@onChange="handleChange" :style="{ height }" />
			<div v-if="isDisabled" class="overlay"></div>
		<!-- </div> -->
	</div>
</template>

<script lang="ts" scoped>
import '@wangeditor/editor/dist/css/style.css'; // 引入 css
import {getCurrentInstance} from 'vue';
import { onBeforeUnmount, ref, shallowRef, onMounted, watch, computed } from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { i18nChangeLanguage } from '@wangeditor/editor';
import { Local, Session } from '/@/utils/storage';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
export default {
	components: { Editor, Toolbar },
	props: {
		// 是否禁用
		disable: {
			type: Boolean,
			default: () => false,
		},
		placeholder: {
			type: String,
			default: () => 'Please input',
		},
		modelValue: String,
		// 双向绑定，用于获取 editor.getText()
		getText: String,
		// 高度
		height: {
			type: String,
			default: () => '310px',
		},
	},
	setup(props, { emit }) {
		i18nChangeLanguage('en');
		const { proxy } = getCurrentInstance() as any;
		const BASE_URL = computed(() => {
			const appSettings = proxy?.$appSettings;
			return appSettings?.BASE_URL || import.meta.env.VITE_API_URL;
		});
		// 编辑器实例，必须用 shallowRef
		const editorRef = shallowRef();
		const stores = useUserInfo();
		const { userInfos } = storeToRefs(stores);
		// 内容 HTML
		const valueHtml = ref(props.modelValue);

		const toolbarConfig = {
			excludeKeys: ['fontFamily', 'insertTable'],
			
		};

		const editorConfig = {
			placeholder: 'Please input',
			MENU_CONF: {},
			//XSS_ENABLE: true, // 开启 XSS 过滤
			// XSS_WHITE_LIST: {
			// 	img: ['src', 'alt', 'class', 'data-*', 'draggable', 'style'] // 允许特定属性:ml-citation{ref="4" data="citationList"}
			// }
		};

		const editorStyle = computed(() => ({
			height: '150px',
			// 设置编辑器的高度
			backgroundColor: '#eee',
			// 根据禁用状态设置背景颜色
		}));

		//上传图片
		editorConfig.MENU_CONF['uploadImage'] = {
			//server: import.meta.env.VITE_API_URL + '/common/UploadFile',
			server: `${BASE_URL.value}Api/Files/UploadFile`,
			// form-data fieldName ，默认值 'wangeditor-uploaded-image'
			fieldName: 'file',
			// 单个文件的最大体积限制，默认为 2M
			maxFileSize: 5 * 1024 * 1024, // 5M
			// 最多可上传几个文件，默认为 100
			maxNumberOfFiles: 10,
			// 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
			allowedFileTypes: ['image/*'],
			// 将 meta 拼接到 url 参数中，默认 false
			metaWithUrl: false,
			// 自定义增加 http  header
			headers: {
				//Authorization: 'Bearer ' + getToken(),
				Authorization: 'Bearer ' +Local.get('token'),
				userid:userInfos.value.userId
			},
			// 跨域是否传递 cookie ，默认为 false
			withCredentials: true,
			// 超时时间，默认为 10 秒
			timeout: 20 * 1000, // 20 秒
			// 自定义插入图片
			customInsert(res, insertFn) {
				//let result=JSON.parse(res.data);
				//console.log("result:",result.Data);
				// ; -(
				// 	// 从 res 中找到 url alt href ，然后插图图片
				// 	insertFn(result.Data.url)
				// )
				  // 手动拼接带属性的 <img> 标签

				const result = JSON.parse(res.data);
				const imgUrl = result.Data.url;
				const imgHtml = `
					<img 
					src="${imgUrl}" 
					
					/>
				`;

				// 直接插入完整 HTML（需通过 ref 获取 editor 实例）
				editorRef.value.dangerouslyInsertHtml(imgHtml); // Vue3 示例，React 同理
			}
		};
		// 组件销毁时，也及时销毁编辑器
		onBeforeUnmount(() => {
			const editor = editorRef.value;
			if (editor == null) return;
			editor.destroy();
		});

		const handleCreated = (editor) => {
			editorRef.value = editor; // 记录 editor 实例，重要！
			if (props.disable) {
				editorRef.value.disable();
			}
		};

		const handleChange = (editor) => {
			emit('update:modelValue', editor.getHtml());
			emit('update:getText', editor.getText());
			emit('getisFocused', editor.isFocused());
		};
		// 监听是否禁用改变
		watch(
			() => props.disable,
			(bool) => {
				const editor = editorRef.value;
				if (editor == null) return;
				bool ? editor.disable() : editor.enable();
			},
			{
				deep: true,
			}
		);
		watch(
			() => props.modelValue,
			(value) => {
				const editor = editorRef.value;
				if (value == undefined) {
					editor.clear();
					return;
				}
				valueHtml.value = value;
			}
		);

		return {
			editorRef,
			valueHtml,
			mode: 'default', // 或 'simple'
			toolbarConfig,
			editorConfig,
			handleCreated,
			handleChange,
			editorStyle,
			isDisabled: computed(() => props.disable),
		};
	},
};
</script>

<style scoped lang="scss">
:deep(.el-collapse-item__content) {
	padding-top: 10px;
	padding-bottom: 0;
}

:deep(.w-e-bar-divider) {
	height: 32px;
}

:deep(.w-e-bar-item) {
	//height:32px;   //设置后会引起弹出未能选中
	padding: 2px 0 0 0;

	button {
		height: 20px;
		padding: 0px 5px;
	}
}

:deep(.w-e-modal) {
	padding: 0px 10px;

	.babel-container {
		margin: 0px;
		padding: 0px;

		span {
			margin: 0px;
		}

		input {
			padding: 5px;
			margin: 0px;
		}
	}
}

.overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(239, 240, 253, 0.6);
	z-index: 10;
	cursor: not-allowed;
}
</style>
