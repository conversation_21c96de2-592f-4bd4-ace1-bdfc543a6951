import { RectNode, RectNodeModel, h } from '@logicflow/core';
import { v4 as uuidv4 } from 'uuid';
class UserTaskNode extends RectNode {
	getIconShape() {
		const { model } = this.props;
		const { x, y, width, height } = model;
		return h(
			'g',
			{
				transform: `translate(${x - width / 2 + 5} ${y - height / 2 + 5})`,
			},
			[
				h('circle', {
					cx: 10,
					cy: 6,
					r: 5,
					fill: '#000000',
				}),
				h('path', {
					d: 'M15 15 Q10 12 5 15 L5 20 Q10 18 15 20 Z',
					fill: '#000000',
				}),
			]
		);
	}
	getShape() {
		const { model } = this.props;
		const { x, y, width, height } = model;
		return h('g', {}, [
			h('rect', {
				x: x - width / 2,
				y: y - height / 2,
				width,
				height,
				fill: '#ffffff',
				stroke: '#000000',
			}),
			this.getIconShape(), // 调用 getIconShape 方法并将其返回的结果添加到渲染列表中
		]);
	}
}
class UserTaskModel extends RectNodeModel {
	constructor(data, graphModel) {
		super(data, graphModel);
	}
	// 自定义节点形状属性
	initNodeData(data) {
		if (!data.properties) {
			data.properties = {
				name: this.findNodeByName(),
			};
		}
		super.initNodeData(data);
		this.width = 150;
		this.height = 60;
	}
	findNodeByName() {
		const nodes = this.graphModel.nodes;
		const names = nodes.map((node) => node.properties.name).sort(); // 将所有name从小到大排序
		const lastNodeName = names.pop(); // 取出最后一个name
		const lastNodeNumber = lastNodeName ? parseInt(lastNodeName.replace(/^\D+/g, '')) : 0; // 提取数字部分并转换为数字
		const nextNodeNumber = lastNodeNumber + 1; // 计算下一个数字
		const nextNodeName = `node${nextNodeNumber}`; // 拼接下一个name
		return nextNodeName;
	}
	setTriggerName() {
		const nodes = this.graphModel.nodes;
		const names = nodes.map((node) => node.properties.execute.name).sort(); // 将所有name从小到大排序
		const lastNodeName = names.pop(); // 取出最后一个name
		const lastNodeNumber = lastNodeName ? parseInt(lastNodeName.replace(/^\D+/g, '')) : 0; // 提取数字部分并转换为数字
		const nextNodeNumber = lastNodeNumber + 1; // 计算下一个数字
		const nextNodeName = `SignalReceived${nextNodeNumber}`; // 拼接下一个name
		return nextNodeName;
	}
}

export default {
	type: 'UserTask',
	view: UserTaskNode,
	model: UserTaskModel,
};
