﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

using LinqKit;
using Mapster;
using YunLanCrm.Model;
using YunLanCrm.Common;
using YunLanCrm.IServices;
using YunLanCrm.Model.Api;
using YunLanCrm.Model.Models;
using YunLanCrm.Dto.CmTicketAttachment;

namespace YunLanCrm.WebApi.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("Api/[controller]")]
    [Authorize(Permissions.Name)]
    public class CmTicketAttachmentController : ControllerBase
    {
        private readonly ILogger<CmTicketAttachmentInfo> _logger;
        private readonly ICmTicketAttachmentService _cmTicketAttachmentService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmTicketAttachmentService"></param>
        /// <param name="logger"></param>
        public CmTicketAttachmentController(ICmTicketAttachmentService cmTicketAttachmentService, ILogger<CmTicketAttachmentInfo> logger)
        {
            _logger = logger;
            _cmTicketAttachmentService = cmTicketAttachmentService;
            
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="req">请求信息</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<long> Add(CmTicketAttachmentAddOrUpdateDto req)
        {
            return await _cmTicketAttachmentService.AddCmTicketAttachment(req);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="req">信息</param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<bool> Update(CmTicketAttachmentAddOrUpdateDto req)
        {
            return await _cmTicketAttachmentService.UpdateCmTicketAttachment(req);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost("Delete/{id}")]
        public async Task<bool> Delete(long id)
        {
            int result = await _cmTicketAttachmentService.Delete(a => a.Id == id);

            return result > 0;
        }
        
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        public async Task<bool> Delete(object[] items)
        {
            int result = await _cmTicketAttachmentService.Delete(a => items.Contains(a.Id));

            return result > 0;
        }


        /// <summary>
        /// 获取一个信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Get/{id}")]
        public async Task<CmTicketAttachmentDto> Get(long id)
        {
            return await _cmTicketAttachmentService.QueryInfo<CmTicketAttachmentDto>(a => a.Id == id);
        }
        
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Get")]
        public async Task<List<CmTicketAttachmentDto>> GetList([FromQuery] CmTicketAttachmentListQueryDto req)
        {
            return await _cmTicketAttachmentService.GetList(req);
        }

        /// <summary>
        /// 获取一个详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Detail/{id}")]
        public async Task<CmTicketAttachmentDetailDto> Detail(long id)
        {
            return await _cmTicketAttachmentService.Detail(id);
        }
        
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Detail")]
        public async Task<List<CmTicketAttachmentDetailDto>> DetailList([FromQuery] CmTicketAttachmentListQueryDto req)
        {
            return await _cmTicketAttachmentService.DetailList(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("Query")]
        public async Task<PageQueryResult<CmTicketAttachmentDetailDto>> Query([FromQuery] CmTicketAttachmentPageQueryDto req)
        {
            return await PageQuery(req);
        }

        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("Query")]
        public async Task<PageQueryResult<CmTicketAttachmentDetailDto>> QueryPost(CmTicketAttachmentPageQueryDto req)
        {
            return await PageQuery(req);
        }


        /// <summary>
        /// 获取列表，分页
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<PageQueryResult<CmTicketAttachmentDetailDto>> PageQuery([FromQuery] CmTicketAttachmentPageQueryDto req)
        {
            return await _cmTicketAttachmentService.PageQueryView(req);
        }

    }
}