using MimeKit.Cryptography;
using System;
using System.Collections.Generic;
using System.IO.Compression;
using System.IO;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Security.Cryptography.Xml;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Xml;

namespace YunLanCrm.Common.SSO
{
    namespace Saml
    {
        /// <summary>
        /// this class adds support of SHA256 signing to .NET 4.0 and earlier
        /// (you can use it in .NET 4.5 too, if you don't want a "System.Deployment" dependency)
        /// </summary>
        public sealed class RSAPKCS1SHA256SignatureDescription : SignatureDescription
        {
            public RSAPKCS1SHA256SignatureDescription()
            {
                KeyAlgorithm = typeof(RSACryptoServiceProvider).FullName;
                DigestAlgorithm = typeof(SHA256Managed).FullName;   // Note - SHA256CryptoServiceProvider is not registered with CryptoConfig
                FormatterAlgorithm = typeof(RSAPKCS1SignatureFormatter).FullName;
                DeformatterAlgorithm = typeof(RSAPKCS1SignatureDeformatter).FullName;
            }

            public override AsymmetricSignatureDeformatter CreateDeformatter(AsymmetricAlgorithm key)
            {
                if (key == null)
                    throw new ArgumentNullException("key");

                RSAPKCS1SignatureDeformatter deformatter = new RSAPKCS1SignatureDeformatter(key);
                deformatter.SetHashAlgorithm("SHA256");
                return deformatter;
            }

            public override AsymmetricSignatureFormatter CreateFormatter(AsymmetricAlgorithm key)
            {
                if (key == null)
                    throw new ArgumentNullException("key");

                RSAPKCS1SignatureFormatter formatter = new RSAPKCS1SignatureFormatter(key);
                formatter.SetHashAlgorithm("SHA256");
                return formatter;
            }

            private static bool _initialized = false;
            public static void Init()
            {
                if (!_initialized)
                    CryptoConfig.AddAlgorithm(typeof(RSAPKCS1SHA256SignatureDescription), "http://www.w3.org/2001/04/xmldsig-more#rsa-sha256");
                _initialized = true;
            }
        }

        public partial class Response
        {
            private static byte[] StringToByteArray(string st)
            {
                if (string.IsNullOrWhiteSpace(st))
                {
                    throw new ArgumentException("Certificate string cannot be null or empty");
                }

                // Handle PEM format certificates
                if (st.Contains("-----BEGIN CERTIFICATE-----"))
                {
                    // Remove PEM headers, footers, and whitespace
                    st = st.Replace("-----BEGIN CERTIFICATE-----", "")
                           .Replace("-----END CERTIFICATE-----", "")
                           .Replace("\r", "")
                           .Replace("\n", "")
                           .Replace(" ", "");
                }

                try
                {
                    // Try to parse as Base64 first (most common format)
                    return Convert.FromBase64String(st);
                }
                catch (FormatException)
                {
                    // Fallback to original byte conversion if not Base64
                    byte[] bytes = new byte[st.Length];
                    for (int i = 0; i < st.Length; i++)
                    {
                        bytes[i] = (byte)st[i];
                    }
                    return bytes;
                }
            }

            protected XmlDocument _xmlDoc;
            protected readonly X509Certificate2 _certificate;
            protected XmlNamespaceManager _xmlNameSpaceManager; //we need this one to run our XPath queries on the SAML XML

            public string Xml { get { return _xmlDoc.OuterXml; } }

            public Response(string certificateStr, string responseString)
                : this(StringToByteArray(certificateStr), responseString) { }

            public Response(byte[] certificateBytes, string responseString)
                : this(certificateBytes)
            {
                LoadXmlFromBase64(responseString);
            }

            public Response(string certificateStr) : this(StringToByteArray(certificateStr)) { }

            public Response(byte[] certificateBytes)
            {
                RSAPKCS1SHA256SignatureDescription.Init(); //init the SHA256 crypto provider (for needed for .NET 4.0 and lower)

                if (certificateBytes == null || certificateBytes.Length == 0)
                {
                    throw new ArgumentException("Certificate bytes cannot be null or empty");
                }

                try
                {
                    _certificate = new X509Certificate2(certificateBytes);

                    // Verify the certificate has a valid public key
                    if (_certificate.PublicKey?.Key == null)
                    {
                        throw new ArgumentException("Certificate does not contain a valid public key");
                    }
                }
                catch (Exception ex)
                {
                    throw new ArgumentException($"Invalid certificate data: {ex.Message}", ex);
                }
            }

            public void LoadXml(string xml)
            {
                _xmlDoc = new XmlDocument();
                _xmlDoc.PreserveWhitespace = true;
                _xmlDoc.XmlResolver = null;
                _xmlDoc.LoadXml(xml);

                _xmlNameSpaceManager = GetNamespaceManager(); //lets construct a "manager" for XPath queries
            }

            public void LoadXmlFromBase64(string response)
            {


                UTF8Encoding enc = new UTF8Encoding();
                LoadXml(enc.GetString(Convert.FromBase64String(response)));
            }

            public bool IsValid()
            {
                try
                {
                    if (_certificate == null)
                    {
                        throw new InvalidOperationException("Certificate is not initialized");
                    }

                    if (_xmlDoc == null)
                    {
                        throw new InvalidOperationException("XML document is not loaded");
                    }

                    XmlNodeList nodeList = _xmlDoc.SelectNodes("//ds:Signature", _xmlNameSpaceManager);

                    if (nodeList.Count == 0) return false;

                    SignedXml signedXml = new SignedXml(_xmlDoc);
                    signedXml.LoadXml((XmlElement)nodeList[0]);

                    // Validate signature reference first
                    if (!ValidateSignatureReference(signedXml))
                    {
                        return false;
                    }

                    // Check if certificate has a valid public key before signature verification
                    if (_certificate.PublicKey?.Key == null)
                    {
                        throw new InvalidOperationException("Certificate does not contain a valid public key");
                    }

                    // Verify the signature
                    bool signatureValid = signedXml.CheckSignature(_certificate, true);

                    // Check expiration
                    bool notExpired = !IsExpired();

                    return signatureValid && notExpired;
                }
                catch (Exception ex)
                {
                    // Log the exception details for debugging
                    System.Diagnostics.Debug.WriteLine($"SAML validation error: {ex.Message}");
                    return false;
                }
            }

            //an XML signature can "cover" not the whole document, but only a part of it
            //.NET's built in "CheckSignature" does not cover this case, it will validate to true.
            //We should check the signature reference, so it "references" the id of the root document element! If not - it's a hack
            private bool ValidateSignatureReference(SignedXml signedXml)
            {
                if (signedXml.SignedInfo.References.Count != 1) //no ref at all
                    return false;

                var reference = (Reference)signedXml.SignedInfo.References[0];
                var id = reference.Uri.Substring(1);

                var idElement = signedXml.GetIdElement(_xmlDoc, id);

                if (idElement == _xmlDoc.DocumentElement)
                    return true;
                else //sometimes its not the "root" doc-element that is being signed, but the "assertion" element
                {
                    var assertionNode = _xmlDoc.SelectSingleNode("/samlp:Response/saml:Assertion", _xmlNameSpaceManager) as XmlElement;
                    if (assertionNode != idElement)
                        return false;
                }

                return true;
            }

            private bool IsExpired()
            {
                DateTime expirationDate = DateTime.MaxValue;
                XmlNode node = _xmlDoc.SelectSingleNode("/samlp:Response/saml:Assertion[1]/saml:Subject/saml:SubjectConfirmation/saml:SubjectConfirmationData", _xmlNameSpaceManager);
                if (node != null && node.Attributes["NotOnOrAfter"] != null)
                {
                    DateTime.TryParse(node.Attributes["NotOnOrAfter"].Value, out expirationDate);
                }
                return DateTime.UtcNow > expirationDate.ToUniversalTime();
            }

            public string GetNameID()
            {
                XmlNode node = _xmlDoc.SelectSingleNode("/samlp:Response/saml:Assertion[1]/saml:Subject/saml:NameID", _xmlNameSpaceManager);
                return node.InnerText;
            }

            public virtual string GetUpn()
            {
                return GetCustomAttribute("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/upn");
            }

            public virtual string GetEmail()
            {
                return GetCustomAttribute("User.email")
                    ?? GetCustomAttribute("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress") //some providers (for example Azure AD) put last name into an attribute named "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
                    ?? GetCustomAttribute("mail"); //some providers put last name into an attribute named "mail"
            }

            public virtual string GetFirstName()
            {
                return GetCustomAttribute("first_name")
                    ?? GetCustomAttribute("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname") //some providers (for example Azure AD) put last name into an attribute named "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname"
                    ?? GetCustomAttribute("User.FirstName")
                    ?? GetCustomAttribute("givenName"); //some providers put last name into an attribute named "givenName"
            }

            public virtual string GetLastName()
            {
                return GetCustomAttribute("last_name")
                    ?? GetCustomAttribute("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname") //some providers (for example Azure AD) put last name into an attribute named "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname"
                    ?? GetCustomAttribute("User.LastName")
                    ?? GetCustomAttribute("sn"); //some providers put last name into an attribute named "sn"
            }

            public virtual string GetDepartment()
            {
                return GetCustomAttribute("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/department");
            }

            public virtual string GetPhone()
            {
                return GetCustomAttribute("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/homephone")
                    ?? GetCustomAttribute("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/telephonenumber");
            }

            public virtual string GetCompany()
            {
                return GetCustomAttribute("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/companyname")
                    ?? GetCustomAttribute("User.CompanyName");
            }

            public string GetCustomAttribute(string attr)
            {
                XmlNode node = _xmlDoc.SelectSingleNode("/samlp:Response/saml:Assertion[1]/saml:AttributeStatement/saml:Attribute[@Name='" + attr + "']/saml:AttributeValue", _xmlNameSpaceManager);
                return node == null ? null : node.InnerText;
            }

            //returns namespace manager, we need one b/c MS says so... Otherwise XPath doesnt work in an XML doc with namespaces
            //see https://stackoverflow.com/questions/7178111/why-is-xmlnamespacemanager-necessary
            private XmlNamespaceManager GetNamespaceManager()
            {
                XmlNamespaceManager manager = new XmlNamespaceManager(_xmlDoc.NameTable);
                manager.AddNamespace("ds", SignedXml.XmlDsigNamespaceUrl);
                manager.AddNamespace("saml", "urn:oasis:names:tc:SAML:2.0:assertion");
                manager.AddNamespace("samlp", "urn:oasis:names:tc:SAML:2.0:protocol");

                return manager;
            }
        }

        public class AuthRequest
        {
            public string _id;
            private string _issue_instant;

            private string _issuer;
            private string _assertionConsumerServiceUrl;

            public enum AuthRequestFormat
            {
                Base64 = 1
            }

            public AuthRequest(string issuer, string assertionConsumerServiceUrl)
            {
                RSAPKCS1SHA256SignatureDescription.Init(); //init the SHA256 crypto provider (for needed for .NET 4.0 and lower)

                _id = "_" + Guid.NewGuid().ToString();
                _issue_instant = DateTime.Now.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ", System.Globalization.CultureInfo.InvariantCulture);

                _issuer = issuer;
                _assertionConsumerServiceUrl = assertionConsumerServiceUrl;
            }

            public string GetRequest(AuthRequestFormat format)
            {
                using (StringWriter sw = new StringWriter())
                {
                    XmlWriterSettings xws = new XmlWriterSettings();
                    xws.OmitXmlDeclaration = true;

                    using (XmlWriter xw = XmlWriter.Create(sw, xws))
                    {
                        xw.WriteStartElement("samlp", "AuthnRequest", "urn:oasis:names:tc:SAML:2.0:protocol");
                        xw.WriteAttributeString("ID", _id);
                        xw.WriteAttributeString("Version", "2.0");
                        xw.WriteAttributeString("IssueInstant", _issue_instant);
                        xw.WriteAttributeString("ProtocolBinding", "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST");
                        xw.WriteAttributeString("AssertionConsumerServiceURL", _assertionConsumerServiceUrl);

                        xw.WriteStartElement("saml", "Issuer", "urn:oasis:names:tc:SAML:2.0:assertion");
                        xw.WriteString(_issuer);
                        xw.WriteEndElement();

                        xw.WriteStartElement("samlp", "NameIDPolicy", "urn:oasis:names:tc:SAML:2.0:protocol");
                        xw.WriteAttributeString("Format", "urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified");
                        xw.WriteAttributeString("AllowCreate", "true");
                        xw.WriteEndElement();

                        /*xw.WriteStartElement("samlp", "RequestedAuthnContext", "urn:oasis:names:tc:SAML:2.0:protocol");
                        xw.WriteAttributeString("Comparison", "exact");
                        xw.WriteStartElement("saml", "AuthnContextClassRef", "urn:oasis:names:tc:SAML:2.0:assertion");
                        xw.WriteString("urn:oasis:names:tc:SAML:2.0:ac:classes:PasswordProtectedTransport");
                        xw.WriteEndElement();
                        xw.WriteEndElement();*/

                        xw.WriteEndElement();
                    }

                    if (format == AuthRequestFormat.Base64)
                    {
                        //byte[] toEncodeAsBytes = System.Text.ASCIIEncoding.ASCII.GetBytes(sw.ToString());
                        //return System.Convert.ToBase64String(toEncodeAsBytes);

                        //https://stackoverflow.com/questions/25120025/acs75005-the-request-is-not-a-valid-saml2-protocol-message-is-showing-always%3C/a%3E
                        var memoryStream = new MemoryStream();
                        var writer = new StreamWriter(new DeflateStream(memoryStream, CompressionMode.Compress, true), new UTF8Encoding(false));
                        writer.Write(sw.ToString());
                        writer.Close();
                        string result = Convert.ToBase64String(memoryStream.GetBuffer(), 0, (int)memoryStream.Length, Base64FormattingOptions.None);
                        return result;
                    }

                    return null;
                }
            }

            //returns the URL you should redirect your users to (i.e. your SAML-provider login URL with the Base64-ed request in the querystring
            public string GetRedirectUrl(string samlEndpoint, string relayState = null)
            {
                var queryStringSeparator = samlEndpoint.Contains("?") ? "&" : "?";

                var url = samlEndpoint + queryStringSeparator + "SAMLRequest=" + HttpUtility.UrlEncode(GetRequest(AuthRequestFormat.Base64));

                if (!string.IsNullOrEmpty(relayState))
                {
                    url += "&RelayState=" + HttpUtility.UrlEncode(relayState);
                }

                return url;
            }
        }

        public class LogoutRequest
        {
            public string _id;
            private string _issue_instant;
            private string _destination;
            private string _issuer;
            private string _notOnOrAfter;
            private string _nameID;

            public enum AuthRequestFormat
            {
                Base64 = 1
            }

            public LogoutRequest(string issuer, string nameid, string destination)
            {
                RSAPKCS1SHA256SignatureDescription.Init();

                _id = "_" + Guid.NewGuid().ToString();
                _issue_instant = DateTime.Now.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ", System.Globalization.CultureInfo.InvariantCulture);
                _notOnOrAfter = DateTime.Now.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ", System.Globalization.CultureInfo.InvariantCulture);

                _destination = destination;
                _issuer = issuer;
                _nameID = nameid;
            }

            public string GetRequest(AuthRequestFormat format)
            {
                using (StringWriter sw = new StringWriter())
                {
                    XmlWriterSettings xws = new XmlWriterSettings();
                    xws.OmitXmlDeclaration = true;

                    using (XmlWriter xw = XmlWriter.Create(sw, xws))
                    {
                        xw.WriteStartElement("samlp", "LogoutRequest", "urn:oasis:names:tc:SAML:2.0:protocol");
                        xw.WriteAttributeString("ID", _id);
                        xw.WriteAttributeString("Version", "2.0");
                        xw.WriteAttributeString("IssueInstant", _issue_instant);
                        xw.WriteAttributeString("Destination", _destination);
                        xw.WriteAttributeString("NotOnOrAfter", _notOnOrAfter);
                        xw.WriteAttributeString("Reason", "urn:oasis:names:tc:SAML:2.0:logout:user");

                        xw.WriteStartElement("saml", "Issuer", "urn:oasis:names:tc:SAML:2.0:assertion");
                        xw.WriteString(_issuer);
                        xw.WriteEndElement();

                        xw.WriteStartElement("saml", "NameID", "urn:oasis:names:tc:SAML:2.0:assertion");
                        xw.WriteString(_nameID);
                        xw.WriteEndElement();

                        xw.WriteEndElement();
                    }

                    if (format == AuthRequestFormat.Base64)
                    {
                        var memoryStream = new MemoryStream();
                        var writer = new StreamWriter(new DeflateStream(memoryStream, CompressionMode.Compress, true), new UTF8Encoding(false));
                        writer.Write(sw.ToString());
                        writer.Close();
                        string result = Convert.ToBase64String(memoryStream.GetBuffer(), 0, (int)memoryStream.Length, Base64FormattingOptions.None);
                        return result;
                    }

                    return null;
                }
            }

            public string GetRedirectUrl(string samlEndpoint, string relayState = null)
            {
                var queryStringSeparator = samlEndpoint.Contains("?") ? "&" : "?";

                var url = samlEndpoint + queryStringSeparator + "SAMLRequest=" + HttpUtility.UrlEncode(GetRequest(AuthRequestFormat.Base64));

                if (!string.IsNullOrEmpty(relayState))
                {
                    url += "&RelayState=" + HttpUtility.UrlEncode(relayState);
                }

                return url;
            }
        }

        public partial class LogoutResponse
        {
            private static byte[] StringToByteArray(string st)
            {
                if (string.IsNullOrWhiteSpace(st))
                {
                    throw new ArgumentException("Certificate string cannot be null or empty");
                }

                // Handle PEM format certificates
                if (st.Contains("-----BEGIN CERTIFICATE-----"))
                {
                    // Remove PEM headers, footers, and whitespace
                    st = st.Replace("-----BEGIN CERTIFICATE-----", "")
                           .Replace("-----END CERTIFICATE-----", "")
                           .Replace("\r", "")
                           .Replace("\n", "")
                           .Replace(" ", "");
                }

                try
                {
                    // Try to parse as Base64 first (most common format)
                    return Convert.FromBase64String(st);
                }
                catch (FormatException)
                {
                    // Fallback to original byte conversion if not Base64
                    byte[] bytes = new byte[st.Length];
                    for (int i = 0; i < st.Length; i++)
                    {
                        bytes[i] = (byte)st[i];
                    }
                    return bytes;
                }
            }

            protected XmlDocument _xmlDoc;
            protected readonly X509Certificate2 _certificate;
            protected XmlNamespaceManager _xmlNameSpaceManager; //we need this one to run our XPath queries on the SAML XML

            public string Xml { get { return _xmlDoc.OuterXml; } }

            public LogoutResponse(string certificateStr, string responseString)
                : this(StringToByteArray(certificateStr), responseString) { }

            public LogoutResponse(byte[] certificateBytes, string responseString)
                : this(certificateBytes)
            {
                LoadXmlFromBase64(responseString);
            }

            public LogoutResponse(string certificateStr) : this(StringToByteArray(certificateStr)) { }

            public LogoutResponse(byte[] certificateBytes)
            {
                RSAPKCS1SHA256SignatureDescription.Init(); //init the SHA256 crypto provider (for needed for .NET 4.0 and lower)

                if (certificateBytes == null || certificateBytes.Length == 0)
                {
                    throw new ArgumentException("Certificate bytes cannot be null or empty");
                }

                try
                {
                    _certificate = new X509Certificate2(certificateBytes);

                    // Verify the certificate has a valid public key
                    if (_certificate.PublicKey?.Key == null)
                    {
                        throw new ArgumentException("Certificate does not contain a valid public key");
                    }
                }
                catch (Exception ex)
                {
                    throw new ArgumentException($"Invalid certificate data: {ex.Message}", ex);
                }
            }

            public void LoadXml(string xml)
            {
                _xmlDoc = new XmlDocument();
                _xmlDoc.PreserveWhitespace = true;
                _xmlDoc.XmlResolver = null;
                _xmlDoc.LoadXml(xml);

                _xmlNameSpaceManager = GetNamespaceManager(); //lets construct a "manager" for XPath queries
            }

            public void LoadXmlFromBase64(string LogoutResponse)
            {
                //UTF8Encoding enc = new UTF8Encoding();
                //LoadXml(enc.GetString(Convert.FromBase64String(LogoutResponse)));

                var decompressedStream = Decompress(Convert.FromBase64String(LogoutResponse));
                StreamReader reader = new StreamReader(decompressedStream);
                string decodedSaml = reader.ReadToEnd();

                LoadXml(decodedSaml);
            }

            public bool IsValid()
            {
                try
                {
                    if (_certificate == null)
                    {
                        throw new InvalidOperationException("Certificate is not initialized");
                    }

                    if (_xmlDoc == null)
                    {
                        throw new InvalidOperationException("XML document is not loaded");
                    }

                    XmlNodeList nodeList = _xmlDoc.SelectNodes("//ds:Signature", _xmlNameSpaceManager);

                    if (nodeList.Count == 0) return false;

                    SignedXml signedXml = new SignedXml(_xmlDoc);
                    signedXml.LoadXml((XmlElement)nodeList[0]);

                    // Validate signature reference first
                    if (!ValidateSignatureReference(signedXml))
                    {
                        return false;
                    }

                    // Check if certificate has a valid public key before signature verification
                    if (_certificate.PublicKey?.Key == null)
                    {
                        throw new InvalidOperationException("Certificate does not contain a valid public key");
                    }

                    // Verify the signature
                    bool signatureValid = signedXml.CheckSignature(_certificate, true);

                    // Check expiration
                    bool notExpired = !IsExpired();

                    return signatureValid && notExpired;
                }
                catch (Exception ex)
                {
                    // Log the exception details for debugging
                    System.Diagnostics.Debug.WriteLine($"SAML validation error: {ex.Message}");
                    return false;
                }
            }

            //an XML signature can "cover" not the whole document, but only a part of it
            //.NET's built in "CheckSignature" does not cover this case, it will validate to true.
            //We should check the signature reference, so it "references" the id of the root document element! If not - it's a hack
            private bool ValidateSignatureReference(SignedXml signedXml)
            {
                if (signedXml.SignedInfo.References.Count != 1) //no ref at all
                    return false;

                var reference = (Reference)signedXml.SignedInfo.References[0];
                var id = reference.Uri.Substring(1);

                var idElement = signedXml.GetIdElement(_xmlDoc, id);

                if (idElement == _xmlDoc.DocumentElement)
                    return true;
                else //sometimes its not the "root" doc-element that is being signed, but the "assertion" element
                {
                    var assertionNode = _xmlDoc.SelectSingleNode("/samlp:LogoutResponse/saml:Assertion", _xmlNameSpaceManager) as XmlElement;
                    if (assertionNode != idElement)
                        return false;
                }

                return true;
            }

            private bool IsExpired()
            {
                DateTime expirationDate = DateTime.MaxValue;
                XmlNode node = _xmlDoc.SelectSingleNode("/samlp:LogoutResponse/saml:Assertion[1]/saml:Subject/saml:SubjectConfirmation/saml:SubjectConfirmationData", _xmlNameSpaceManager);
                if (node != null && node.Attributes["NotOnOrAfter"] != null)
                {
                    DateTime.TryParse(node.Attributes["NotOnOrAfter"].Value, out expirationDate);
                }
                return DateTime.UtcNow > expirationDate.ToUniversalTime();
            }

            public string GetStatus()
            {
                XmlNode node = _xmlDoc.SelectSingleNode("/samlp:LogoutResponse/samlp:Status/samlp:StatusCode", _xmlNameSpaceManager);
                return node.Attributes.GetNamedItem("Value").InnerText;
            }

            public Stream Decompress(byte[] input)
            {
                var output = new MemoryStream();

                using (var compressStream = new MemoryStream(input))
                using (var decompressor = new DeflateStream(compressStream, CompressionMode.Decompress))
                    decompressor.CopyTo(output);

                output.Position = 0;
                return output;
            }

            public string GetCustomAttribute(string attr)
            {
                XmlNode node = _xmlDoc.SelectSingleNode("/samlp:LogoutResponse/saml:Assertion[1]/saml:AttributeStatement/saml:Attribute[@Name='" + attr + "']/saml:AttributeValue", _xmlNameSpaceManager);
                return node == null ? null : node.InnerText;
            }

            public string GetNameID()
            {
                XmlNode node = _xmlDoc.SelectSingleNode("/samlp:LogoutRequest/saml:NameID", _xmlNameSpaceManager);
                return node.InnerText;
            }

            //returns namespace manager, we need one b/c MS says so... Otherwise XPath doesnt work in an XML doc with namespaces
            //see https://stackoverflow.com/questions/7178111/why-is-xmlnamespacemanager-necessary
            private XmlNamespaceManager GetNamespaceManager()
            {
                XmlNamespaceManager manager = new XmlNamespaceManager(_xmlDoc.NameTable);
                manager.AddNamespace("ds", SignedXml.XmlDsigNamespaceUrl);
                manager.AddNamespace("saml", "urn:oasis:names:tc:SAML:2.0:assertion");
                manager.AddNamespace("samlp", "urn:oasis:names:tc:SAML:2.0:protocol");

                return manager;
            }
        }
    }
}
