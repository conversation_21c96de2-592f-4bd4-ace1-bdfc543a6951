﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class workflowCrmApi extends BaseApi {
	QueryWorkflowInitiate(params: any) {
		return request({
			url: this.baseurl + 'QueryWorkflowInitiate',
			method: 'get',
			params,
		});
	}

	CheckWorkflowLimits(params: any) {
		return request({
			url: this.baseurl + 'CheckWorkflowLimits',
			method: 'get',
			params,
		});
	}

	WorkflowDoneQuery(data: any) {
		return request({
			url: this.baseurl + 'WorkflowDoneQuery',
			method: 'post',
			data,
		});
	}

	GetActivityTypes(params: any) {
		return request({
			url: this.baseurl + 'GetActivityTypes',
			method: 'get',
			params,
		});
	}

	SaveWorkflowTemplate(data: any) {
		return request({
			url: this.baseurl + 'SaveWorkflowTemplate',
			method: 'post',
			data,
		});
	}

	GetWorkflowOperations(params: any) {
		return request({
			url: this.baseurl + 'GetWorkflowOperations',
			method: 'get',
			params,
		});
	}

	GetWorkflowPermissions(params: any) {
		return request({
			url: this.baseurl + 'GetWorkflowPermissions',
			method: 'get',
			params,
		});
	}

	GetLastActivity(params: any) {
		return request({
			url: this.baseurl + 'GetLastActivity',
			method: 'get',
			params,
		});
	}

	GetCurrentApproval(params: any) {
		return request({
			url: this.baseurl + 'GetCurrentApproval',
			method: 'get',
			params,
		});
	}

	GetManualActivitys(params: any) {
		return request({
			url: this.baseurl + 'GetManualActivitys',
			method: 'get',
			params,
		});
	}

	GetReturnActivitys(params: any) {
		return request({
			url: this.baseurl + 'GetReturnActivitys',
			method: 'get',
			params,
		});
	}

	Workflowexecution(data: any) {
		return request({
			url: this.baseurl + 'Workflowexecution',
			method: 'post',
			data,
		});
	}

	GetEntityProperties(params: any) {
		return request({
			url: this.baseurl + 'GetEntityProperties',
			method: 'get',
			params,
		});
	}
}

export default new workflowCrmApi('/api/workflowCrm/', 'crmWorkflowId');
