﻿using Elsa.Attributes;
using Elsa;
using Elsa.Services.Models;
using Elsa.ActivityResults;
using YunLanCrm.Dto.WorkflowExecutionLogRecords;
using YunLanCrm.Model;
using Elsa.Persistence;
using YunLanCrm.Common.HttpContextUser;
using YunLanCrm.IRepositories;
using YunLanCrm.Model.Models;
using Elsa.Models;
using YunLanCrm.Model.Dto.WorkflowCrm;
using Newtonsoft.Json;
using YunLanCrm.Common.Extensions;
using Elsa.Activities.UserTask.Activities;
using Elsa.Serialization;
using Elsa.Expressions;
using YunLanCrm.Dto.WorkflowCrm;
using YunLanCrm.ElsaWorkflows.Activities;
using YunLanCrm.IServices;
using YunLanCrm.Model.Dto.Emails;
using System.Reflection;
using Mapster;
using YunLanCrm.Dto.CmTickets;
using YunLanCrm.Services;
using Org.BouncyCastle.Ocsp;

namespace YunLanCrm.Api.ElsaWorkflows.Activities
{
    [Action(Category = "基础节点", DisplayName = "Approved", Description = "审批节点（UserTask）", Outcomes = new[] { OutcomeNames.Done })]
    public class Approved : UserTask
    {
        protected readonly IUser _user;
        protected readonly IWorkflowCrmRepository _workflowCrmRepository;
        protected readonly IWorkflowDefinitionStore _workflowDefinitionStore;
        protected readonly IEmailsService _emailsService;
        private readonly IWorkflowCrmService _workflowCrmService;
        private IWorkflowCrmRepository workflowCrmRepository;
        private IWorkflowDefinitionStore workflowDefinitionStore;

        [ActivityInput(Hint = "Config", SupportedSyntaxes = new[] { SyntaxNames.Literal, SyntaxNames.JavaScript, SyntaxNames.Liquid })]
        public string Config { get; set; } = default;

        public List<string> UserActions { get; set; }

        public Approved(IContentSerializer serializer,
            IUser user,
            IEmailsService emailsService,
            IWorkflowCrmService workflowCrmService,
            IWorkflowCrmRepository workflowCrmRepository,
            IWorkflowDefinitionStore workflowDefinitionStore) : base(serializer)
        {
            _workflowDefinitionStore = workflowDefinitionStore;
            _workflowCrmRepository = workflowCrmRepository;
            _user = user;
            _emailsService = emailsService;
            _workflowCrmService = workflowCrmService;

            //定义流程的操作
            UserActions = new List<string>() {
                Consts.WorkflowAction_Started,
                Consts.WorkflowAction_Approve,
                Consts.WorkflowAction_Rejected,
                Consts.WorkflowAction_AssignTo,
                Consts.WorkflowAction_Revoked,
                Consts.WorkflowAction_Returned,
                Consts.WorkflowAction_Cancelled,
                Consts.WorkflowAction_Escalate,
                Consts.WorkflowAction_Resubmit,
            };
        }

        public Approved(IContentSerializer serializer) : base(serializer)
        {

        }

        /// <summary>
        /// 第一次执行
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            var newTaskId = Guid.NewGuid();

            //执行的信息
            var input = new WorkflowExecutionDto();

            if (context.Input is WorkflowExecutionDto)
            {
                input = context.GetInput<WorkflowExecutionDto>();
            }

            //任务来源 这个属性很重要，撤回、退回到哪个节点是根据这个来决定的 
            var fromTaskId = input.TaskId.HasValue ? input.TaskId.Value : Guid.Empty;

            //表单数据
            var formObj = context.GetVariable("FormData");

            var ticketInfo = formObj.Adapt<CmTicketsAddOrUpdateDto>();

            var currentUserId = ticketInfo.UserTrusteeId > 0 ? ticketInfo.UserTrusteeId : _user.UserId;//是否是代理用户
            
            //当前节点的配置
            var nodeConfig = JsonConvert.DeserializeObject<DesignNodeProperty>(Config);

            //当前流程的实例
            var workflowInstance = context.WorkflowInstance;

            //当前节点的名称
            var nodeName = GetActivityBlueprintName(context.ActivityBlueprint);

            //添加流转记录
            var isActionApprove = SetIsActionApprove(context);
            var transferTypeName = GetTransferTypeName(context);
            var result = GetOperationType(context, nodeConfig);
            var operationType = result.Item1;
            var operationMsg = result.Item2;

            WorkflowTransferInfo log = new WorkflowTransferInfo()
            {
                TransferId = Guid.NewGuid(),
                WorkflowInstanceId = context.WorkflowInstance.Id,
                TransferDate = DateTime.Now,
                //TransferType = GetBaseTypeName(context.ActivityBlueprint.Type),
                TransferType =input.Action== Consts.WorkflowTaskStatus_OnHold ? Consts.WorkflowTaskStatus_OnHold : GetBaseTypeName(context.ActivityBlueprint.Type),
                TransferTypeName = GetActivityBlueprintName(context.ActivityBlueprint),
                ActivityId = context.ActivityBlueprint.Id,
                CreatedById = currentUserId,
                Display = true,
                IsAutoPass = (nodeConfig.autoApproval == 1),
                FromActivityId = input.ActivityId,
                Msg = operationMsg,
            };

            await _workflowCrmRepository.Db.Insertable(log).ExecuteCommandAsync();

            //取业务表
            var busObj = await _workflowCrmRepository.Db.Queryable<WorkflowBusinessInfo>().Where(a => a.ElsaInstancesId == workflowInstance.Id).FirstAsync();

            //更新当前审批节点
            await _workflowCrmRepository.Db.Updateable<WorkflowBusinessInfo>()
                .SetColumns(a => a.NextNode == nodeName)
                .Where(a => a.ElsaInstancesId == workflowInstance.Id)
                .ExecuteCommandAsync();

            var createdByUserId = await GetTaskCreator(context, currentUserId);
            // //取当前节点对应的任务
            var taskObj = await _workflowCrmRepository.Db.Queryable<WorkflowTaskInfo>()
                                    .Where(a => a.TaskId == input.TaskId).FirstAsync();
            WorkflowTaskInfo fromTaskObj = null;

            //为审批人创建一条任务
            WorkflowTaskInfo task = new WorkflowTaskInfo
            {
                TaskId = newTaskId,
                TaskMode = nodeConfig.approvalMethod,
                TaskType = Consts.TaskType_Approve,
                ActivityId = context.ActivityId,
                FromTaskId = fromTaskId,
                WorkflowInstanceId = workflowInstance.Id,
                TaskStatus = Consts.WorkflowTaskStatus_Pending,
                Priority = WorkflowPriority.Normal.ToEnumInt(),
                TransferId = log != null ? log.TransferId : Guid.Empty,
                CreatedById = createdByUserId,
                CreatedDate = DateTime.Now.AddSeconds(1),
                Msg = string.Format("于 {0} 生成了一个审批任务；", DateTime.Now.ToString())
            };

            await _workflowCrmRepository.Db.Insertable(task).ExecuteCommandAsync();

            //需要审批
            if (nodeConfig.autoApproval == 0)
            {
                var list = new List<WorkflowTaskUserInfo>();

                //当前审批节点的审批人
                List<int> owners = new List<int>();

                if (input.ExecutionType == Consts.WorkflowAction_Revoked)
                {
                    fromTaskObj = await _workflowCrmRepository.Db.Queryable<WorkflowTaskInfo>()
                                        .Where(a => a.TaskId == taskObj.FromTaskId).FirstAsync();

                    if (fromTaskObj == null || !fromTaskObj.FromTaskId.HasValue)
                    {
                        throw new Exception("没有获取到任务来源");
                    }

                    //更新真正的任务来源
                    await _workflowCrmRepository.Db.Updateable<WorkflowTaskInfo>()
                     .SetColumns(a => a.FromTaskId, fromTaskObj.FromTaskId)
                     .SetColumns(a => a.CreatedById, fromTaskObj.CreatedById)
                     .Where(a => a.TaskId == task.TaskId)
                     .ExecuteCommandAsync();

                    var users = await _workflowCrmRepository.Db.Queryable<WorkflowTaskUserInfo>()
                                           .Where(a => a.TaskId == fromTaskObj.TaskId && (a.OperationResult != Consts.WorkflowAction_AssignTo || string.IsNullOrWhiteSpace(a.OperationResult)))
                                           .Select(a => int.Parse(a.UserId.ToString()))
                                           .ToListAsync();

                    owners.AddRange(users);
                }
                else if (input.ExecutionType == Consts.WorkflowAction_Returned)
                {
                    var workflowDefinition = await _workflowDefinitionStore.FindByDefinitionIdAsync(workflowInstance.DefinitionId, VersionOptions.LatestOrPublished);
                    var fromActivity = workflowDefinition.Activities.Where(a => a.ActivityId == input.ActivityId).FirstOrDefault();
                    var freomNodeConfig = GetNodeConfig(fromActivity);

                    //如果是退回的操作触发的，要从任务表里面把节点的人取出来 
                    if (freomNodeConfig.ReturnType == 1)
                    {
                        //根据任务的来源获取它对应的人，FromTaskId 表示这个任务是从哪个审批过来的
                        fromTaskObj = await _workflowCrmRepository.Db.Queryable<WorkflowTaskInfo>()
                                           .Where(a => a.TaskId == taskObj.FromTaskId).FirstAsync();
                    }

                    if (freomNodeConfig.ReturnType == 2)
                    {
                        fromTaskObj = await _workflowCrmRepository.Db.Queryable<WorkflowTaskInfo>()
                                                .Where(a => a.WorkflowInstanceId == workflowInstance.Id)
                                                .OrderBy(a => a.CreatedDate).FirstAsync();
                    }

                    if (freomNodeConfig.ReturnType == 3)
                    {
                        fromTaskObj = await _workflowCrmRepository.Db.Queryable<WorkflowTaskInfo>()
                                                .Where(a => a.WorkflowInstanceId == workflowInstance.Id
                                                && a.ActivityId == input.RevokeOrReturnActivityId
                                                && a.TaskStatus == Consts.WorkflowTaskStatus_Completed)
                                                .OrderByDescending(a => a.CreatedDate).FirstAsync();
                    }

                    if (fromTaskObj == null || !fromTaskObj.FromTaskId.HasValue)
                    {
                        throw new Exception("没有获取到任务来源");
                    }

                    //更新真正的任务来源
                    await _workflowCrmRepository.Db.Updateable<WorkflowTaskInfo>()
                     .SetColumns(a => a.FromTaskId, fromTaskObj.FromTaskId)
                     .SetColumns(a => a.CreatedById, fromTaskObj.CreatedById)
                     .Where(a => a.TaskId == task.TaskId)
                     .ExecuteCommandAsync();

                    var users = await _workflowCrmRepository.Db.Queryable<WorkflowTaskUserInfo>()
                                           .Where(a => a.TaskId == fromTaskObj.TaskId && (a.OperationResult != Consts.WorkflowAction_AssignTo || string.IsNullOrWhiteSpace(a.OperationResult)))
                                           .Select(a => int.Parse(a.UserId.ToString()))
                                           .ToListAsync();

                    owners.AddRange(users);
                }
                else if (input.Approvers != null && input.Approvers.Count > 0)
                {
                    //审批的时候 选择的人
                    owners = input.Approvers;
                }
                else
                {
                    //节点配置的审批人
                    owners = await _workflowCrmService.GetApprovers(workflowInstance.Id, context.ActivityId, currentUserId, formObj);
                }

                if (owners != null && owners.Count > 0)
                {
                    //任务可能由多个人来执行,每个人都预先添加一条处理记录，状态为未处理
                    foreach (var owner in owners)
                    {
                        var uid = owner.ObjToLong();
                        list.Add(new WorkflowTaskUserInfo()
                        {
                            Id = Guid.NewGuid(),
                            WorkflowInstanceId = context.WorkflowInstance.Id,
                            TaskId = task.TaskId,
                            Status = Consts.WorkflowTaskStatus_Pending,
                            UserId = uid,
                            FromTaskId = fromTaskObj != null ? fromTaskObj.TaskId : Guid.NewGuid(),
                            CreatedById = createdByUserId,
                            CreatedAt = DateTime.Now
                        });
                    }
                }

                //取临时数据
                if (nodeConfig.approvalMethod == Consts.SignatureType_WaitAll)
                {
                    var approvals = context.GetVariable<List<WorkflowExecutionDto>>("Approvals") ?? new List<WorkflowExecutionDto>();
                    if (approvals.Count > 0)
                    {
                        foreach (var item in approvals)
                        {
                            //任务可能由多个人来执行,每个人都预先添加一条处理记录，状态为未处理
                            foreach (var uid in item.Approvers)
                            {
                                if (owners.Contains(uid))
                                {
                                    //一个人只能选择一次
                                    continue;
                                }
                                owners.Add(uid);
                                list.Add(new WorkflowTaskUserInfo()
                                {
                                    Id = Guid.NewGuid(),
                                    WorkflowInstanceId = context.WorkflowInstance.Id,
                                    TaskId = task.TaskId,
                                    Status = Consts.WorkflowTaskStatus_Pending,
                                    UserId = uid,
                                    FromTaskId = fromTaskObj != null ? fromTaskObj.TaskId : Guid.NewGuid(),
                                    CreatedById = item.UserId.ObjToInt()
                                });
                            }
                        }
                    }
                }

                //如果没有审批人就自动通过
                if (list.Count == 0)
                {
                    await _workflowCrmRepository.Db.Updateable<WorkflowTransferInfo>()
                        .SetColumns(a => a.IsAutoPass, true)
                        .SetColumns(a => a.Msg, "没有设置审批人")
                        .Where(a => a.TransferId == log.TransferId)
                        .ExecuteCommandAsync();

                    await _workflowCrmRepository.Db.Updateable<WorkflowTaskInfo>()
                        .SetColumns(a => a.TaskStatus, Consts.WorkflowTaskStatus_Completed)
                        .SetColumns(a => a.Msg, "没有设置审批人")
                        .Where(a => a.TaskId == task.TaskId)
                        .ExecuteCommandAsync();

                    return Done();
                }

                //把审批代办人添加到数据库 
                await _workflowCrmRepository.Db.Insertable(list).ExecuteCommandAsync();

                #region 给任务的待办人发送邮件通知

                //这里当初为什么这样写？忘记了
                //if (input.ExecutionType == Consts.Activity_Type_Approved || input.ExecutionType == Consts.WorkflowAction_Started)

                //首先要获取当前节点配置 是使用开始节点配置、系统默认 还是自定义
                //这里是任务代办，所以获取节点代办的配置就可以了 noticeType: 'Todo'
                var req = new SendEmailWorkflowDto()
                {
                    DefinitionId = workflowInstance.DefinitionId,//定义
                    WorkflowInstanceId = workflowInstance.Id,//实例
                    NodeConfig = nodeConfig,//当前节点的配置信息
                    NoticeType = nameof(nodeConfig.approveNoticeData.Todo),//发送邮件的类型
                    Entity = formObj,//表单数据
                    ActivityId = context.ActivityId,//当前节点的id
                    DateType = busObj.DateType,//关联的业务类型
                    DateValue = busObj.DataId,//关联的业务Id
                    CurrentNodeUsers = owners,//当前节点的审批人
                    ToUserIds = owners,//邮件默认的收件人
                    CcUserIds = null,//邮件默认的抄送人
                    UserTrusteeId = ticketInfo.UserTrusteeId,
                    UserTrusteeIds= ticketInfo.UserTrusteeIds,
                    UserTrusteeIsSend = true,
                    //Ticket_Project=ticketInfo.Ticket_Project,
                };

                await _workflowCrmService.SendWorkflowEmail(req);

                #endregion

                //因为会签的审批是在审批完成之前除了最后一个，其他人的审批操作都是临时保存在这里，所以最后一个审批完成后要清空
                context.SetVariable("Approvals", null);
            }

            //不需要审批，自动通过
            if (nodeConfig.autoApproval == 1)
            {
                await _workflowCrmRepository.Db.Updateable<WorkflowTransferInfo>()
                        .SetColumns(a => a.IsAutoPass, true)
                        .SetColumns(a => a.Msg, "审批节点设置为【自动通过】")
                        .Where(a => a.TransferId == log.TransferId)
                        .ExecuteCommandAsync();

                await _workflowCrmRepository.Db.Updateable<WorkflowTaskInfo>()
                    .SetColumns(a => a.TaskStatus, Consts.WorkflowTaskStatus_Completed)
                    .SetColumns(a => a.Msg, "审批节点设置为【自动通过】")
                    .Where(a => a.TaskId == task.TaskId)
                    .ExecuteCommandAsync();

                return Done();
            }

            return await base.OnExecuteAsync(context);
        }

        /// <summary>
        /// 获取任务的创建人
        /// </summary>
        /// <param name="context"></param>
        ///  <param name="userId"></param>
        /// <returns></returns>
        private async Task<int> GetTaskCreator(ActivityExecutionContext context,int userId)
        {
            var input = new WorkflowExecutionDto();
            if (context.Input is WorkflowExecutionDto)
            {
                input = context.GetInput<WorkflowExecutionDto>();
            }
            //如果是退回操作的话 应该找到当前审批节点的历史 创建人
            if (input.ExecutionType == Consts.WorkflowAction_Returned || input.ExecutionType == Consts.WorkflowAction_Revoked)
            {
                var list = await _workflowCrmRepository.Db.Queryable<WorkflowTaskInfo>()
                                                .Where(a => a.WorkflowInstanceId == context.WorkflowInstance.Id
                                                && a.ActivityId == context.ActivityId
                                                && a.TaskType == Consts.TaskType_Approve
                                                )
                                                .OrderByDescending(a => a.CreatedDate)
                                                .ToListAsync();
                if (list != null && list.Count > 0)
                {
                    return list[0].CreatedById.ObjToInt();
                }
            }



            //如果没有的话就默认是当前用户
            //return _user.UserId;
            return userId;
        }

        /// <summary>
        /// 活动恢复后执行的逻辑
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        protected override async ValueTask<IActivityExecutionResult> OnResumeAsync(ActivityExecutionContext context)
        {
            WorkflowExecutionInput inputDone = new WorkflowExecutionInput();
            //当前节点的配置
            var nodeConfig = JsonConvert.DeserializeObject<DesignNodeProperty>(Config);
            //当前流程的实例
            var workflowInstance = context.WorkflowInstance;

            //取业务表
            var busObj = await _workflowCrmRepository.Db.Queryable<WorkflowBusinessInfo>().Where(a => a.ElsaInstancesId == workflowInstance.Id).FirstAsync();

            //当前节点的名称
            var nodeName = GetActivityBlueprintName(context.ActivityBlueprint);
            if (context.Input is WorkflowExecutionDto)
            {

                var input = context.GetInput<WorkflowExecutionDto>();
                var result = GetOperationType(context, nodeConfig);
                var operationType = result.Item1;
                var operationMsg = result.Item2;
                var operationDate = DateTime.Now;
                var userTrusteeId = input.UserTrusteeId;//是否是代理用户

               // var obj = _workflowCrmRepository.Db.Queryable<WorkflowBusinessInfo>().Where(a => a.ElsaInstancesId == workflowInstance.Id).First();
                var taskObj = _workflowCrmRepository.Db.Queryable<WorkflowTaskInfo>().Where(a => a.TaskId == input.TaskId).First();

                if (input.Data != null)
                {
                    //保存表单信息
                    context.SetVariable("FormData", input.Data);
                }

                //任务所有人的操作结果
                var taskUsers = _workflowCrmRepository.Db.Queryable<WorkflowTaskUserInfo>().Where(a => a.TaskId == input.TaskId.Value).ToList();

                if (input.ExecutionType == Consts.WorkflowAction_Approve)
                {
                    //更新审批人对应的任务操作记录
                    var currentUserTask = taskUsers.Where(a => a.UserId == input.UserId).FirstOrDefault();

                    if (currentUserTask != null)
                    {
                        currentUserTask.Status = Consts.WorkflowTaskStatus_Completed;
                        currentUserTask.OperationType = input.ExecutionType;
                        currentUserTask.OperationDate = operationDate;
                        currentUserTask.OperationResult = input.ExecutionType;
                        currentUserTask.OperationMsg = input.Msg;
                        await _workflowCrmRepository.Db.Updateable(currentUserTask).ExecuteCommandAsync();
                    }

                    #region 更新任务状态
                    var taskStatus = taskObj.TaskStatus;
                    //任务任意一个人审批或退回就可以
                    if (taskObj.TaskMode == Consts.TaskMode_WaitAny)
                    {
                        taskStatus = Consts.WorkflowTaskStatus_Completed;
                        //把任务标志成已完成
                        await _workflowCrmRepository.Db.Updateable<WorkflowTaskInfo>()
                                               .SetColumns(a => a.TaskStatus, taskStatus)
                                               .SetColumns(a => a.UpdatedById, input.UserId)
                                               .SetColumns(a => a.UpdatedDate, operationDate)
                                               .Where(a => a.TaskId == currentUserTask.TaskId)
                                               .ExecuteCommandAsync();

                        //其他人的操作记录也要标记成已完成
                        await _workflowCrmRepository.Db.Updateable<WorkflowTaskUserInfo>()
                            .SetColumns(a => a.Status == Consts.WorkflowTaskStatus_Completed)
                            .Where(a => a.TaskId == currentUserTask.TaskId)
                            .ExecuteCommandAsync();
                    }
                    //任务需要多个人处理
                    if (taskObj.TaskMode == Consts.TaskMode_WaitAll)
                    {
                        if (AllTaskUserFinished(input.ElsaInstancesId, input.TaskId.Value))
                        {
                            taskStatus = Consts.WorkflowTaskStatus_Completed;
                            //把任务标志成已完成
                            await _workflowCrmRepository.Db.Updateable<WorkflowTaskInfo>()
                                                   .SetColumns(a => a.TaskStatus, taskStatus)
                                                   .SetColumns(a => a.UpdatedById, input.UserId)
                                                   .SetColumns(a => a.UpdatedDate, currentUserTask.OperationDate)
                                                   .Where(a => a.TaskId == currentUserTask.TaskId)
                                                   .ExecuteCommandAsync();
                        }
                        else
                        {
                            //如果需要等待，就把审批人的审批结果保存到临时变量
                            var approvals = context.GetVariable<List<WorkflowExecutionDto>>("Approvals") ?? new List<WorkflowExecutionDto>();
                            // 更新List变量
                            approvals.Add(input);
                            //保存变量
                            context.SetVariable("Approvals", approvals);

                            return Suspend();//没有处理完要暂停在这个节点
                        }
                    }

                    #endregion

                    #region 同意后发邮件

                    //上一个任务的所有审批人
                    var sendTaskUser = await _workflowCrmService.GetFromTaskUsers(taskObj.TaskId);
                    if (sendTaskUser.Count == 0) sendTaskUser.Add(busObj.CreatedById);

                    // var toList = _workflowCrmRepository.Db.Queryable<UserInfo>()
                    //     .Where(a => sendTaskUser.Contains(a.Id) && !string.IsNullOrWhiteSpace(a.Email))
                    //     .Select(a => a.Email).ToList();

                    // //同时抄送一份给流程的发起人
                    // var ccList = _workflowCrmRepository.Db.Queryable<UserInfo>()
                    //.Where(a => a.Id == obj.CreatedById && !string.IsNullOrWhiteSpace(a.Email))
                    //.Select(a => a.Email).Distinct().ToList();

                    // var req = new SendEmailWorkflowDto()
                    // {
                    //     DefinitionId = workflowInstance.DefinitionId,
                    //     NodeConfig = nodeConfig,
                    //     NoticeType = nameof(nodeConfig.approveNoticeData.Approve),
                    //     To = toList,
                    //     Cc = ccList,
                    //     Entity = input.Data,
                    //     ActivityId = context.ActivityId,
                    //     DateType = busObj.DateType,
                    //     DateValue = busObj.DataId
                    // };
                    // await _workflowCrmService.SendWorkflowEmail(req);

                    var req = new SendEmailWorkflowDto()
                    {
                        DefinitionId = workflowInstance.DefinitionId,//定义
                        WorkflowInstanceId = workflowInstance.Id,//实例
                        NodeConfig = nodeConfig,//当前节点的配置信息
                        NoticeType = nameof(nodeConfig.approveNoticeData.Approve),//发送邮件的类型
                        Entity = input.Data,//表单数据
                        ActivityId = context.ActivityId,//当前节点的id
                        DateType = busObj.DateType,//关联的业务类型
                        DateValue = busObj.DataId,//关联的业务Id
                        CurrentNodeUsers = taskUsers.Select(a=>int.Parse(a.UserId.ToString())).ToList(),//当前节点的审批人
                        ToUserIds = sendTaskUser,//邮件默认的收件人
                        CcUserIds = new List<int>() { busObj.CreatedById },//邮件默认的抄送人
                        UserTrusteeId=userTrusteeId,
                    };
                    await _workflowCrmService.SendWorkflowEmail(req);

                    #endregion

                    return Done(input);
                }
                else if (input.ExecutionType == Consts.WorkflowAction_Revoked) //撤回
                {
                    //撤回判断 判断当前节点是否多个人完成，如果是需要所有人都撤回才可以
                    //TODO

                    //撤回到哪个节点
                    var activity = await _workflowCrmService.GetFromApprovalByTask(input.TaskId.Value);
                    var fromTransfer = await _workflowCrmRepository.Db.Queryable<WorkflowTransferInfo>()
                                                                   .InnerJoin<WorkflowTaskInfo>((a, b) => a.TransferId == b.TransferId)
                                                                   .Where((a, b) => b.TaskId == input.TaskId)
                                                                   .FirstAsync();

                    //撤回动作通常是针对当前节点和当前节点之后的所有节点，这意味着流程会回到上一个审批节点
                    WorkflowTransferInfo log = new WorkflowTransferInfo()
                    {
                        TransferId = Guid.NewGuid(),
                        WorkflowInstanceId = context.WorkflowInstance.Id,
                        TransferDate = DateTime.Now,
                        TransferType = input.ExecutionType,
                        TransferTypeName = "执行操作",
                        ActivityId = activity.ActivityId,//在哪个节点撤回
                        CreatedById = _user.UserId,
                        Display = true,
                        FromActivityId = fromTransfer.FromActivityId, //应该获取撤回后的真实审批节点
                        Msg = operationMsg
                    };
                    await _workflowCrmRepository.Db.Insertable(log).ExecuteCommandAsync();

                    var revokeOrReturnActivity = context.WorkflowExecutionContext.GetActivityBlueprintById(activity.ActivityId);
                    if (revokeOrReturnActivity.Type == Consts.Activity_Type_Start)
                    {
                        //添加一条流转记录
                        WorkflowTransferInfo log1 = new WorkflowTransferInfo()
                        {
                            TransferId = Guid.NewGuid(),
                            WorkflowInstanceId = context.WorkflowInstance.Id,
                            TransferDate = DateTime.Now,
                            TransferType = Consts.WorkflowAction_Resubmit,
                            TransferTypeName = "重新提交",
                            ActivityId = context.ActivityBlueprint.Id,
                            CreatedById = _user.UserId,
                            Display = true,
                            IsAutoPass = (nodeConfig.autoApproval == 1),
                            FromActivityId = context.ActivityBlueprint.Id,
                            Msg = "等待发起人重新提交",
                        };
                        await _workflowCrmRepository.Db.Insertable(log1).ExecuteCommandAsync();
                    }

                    #region 取消当前节点的任务 和 任务操作记录
                    await _workflowCrmRepository.Db.Updateable<WorkflowTaskInfo>()
                                                .SetColumns(a => a.TaskStatus == Consts.WorkflowTaskStatus_Cancelled)
                                                .SetColumns(a => a.Msg == input.Msg)
                                                .SetColumns(a => a.UpdatedById, input.UserId)//取消人
                                                .SetColumns(a => a.UpdatedDate, operationDate)
                                                .Where(a => a.TaskId == input.TaskId.Value)
                                                .ExecuteCommandAsync();

                    await _workflowCrmRepository.Db.Updateable<WorkflowTaskUserInfo>()
                                                .SetColumns(a => a.Status == Consts.WorkflowTaskStatus_Cancelled)
                                                .SetColumns(a => a.OperationResult == Consts.WorkflowTaskStatus_Cancelled)
                                                .Where(a => a.TaskId == input.TaskId.Value)
                                                .ExecuteCommandAsync();

                    //流转记录任务对应的流转记录不用显示
                    await _workflowCrmRepository.Db.Updateable<WorkflowTransferInfo>()
                                                .SetColumns(a => a.Display == true)
                                                .Where(a => a.TransferId == taskObj.TransferId)
                                                .ExecuteCommandAsync();
                    #endregion

                    #region 发给任务的所有人发送邮件告知任务已经被撤回 (这里不用给上一个节点发邮件，因为撤回会把上一个节点再走一遍，走的时候会发代办邮件)
                    ////var taskStatus = new string[] { Consts.WorkflowTaskStatus_InProgress, Consts.WorkflowTaskStatus_Pending };
                    //var toList = _workflowCrmRepository.Db.Queryable<WorkflowTaskUserInfo>()
                    //             .InnerJoin<UserInfo>((a, b) => a.UserId == b.Id)
                    //             .Where((a, b) => a.WorkflowInstanceId == workflowInstance.Id && a.TaskId == taskObj.TaskId && !string.IsNullOrWhiteSpace(b.Email))
                    //             .Select((a, b) => b.Email).Distinct().ToList();

                    //var email = new SendEmailWorkflowDto()
                    //{
                    //    DefinitionId = workflowInstance.DefinitionId,
                    //    NodeConfig = await _workflowCrmService.GetFromApprovalConfig(input.TaskId.Value),
                    //    NoticeType = nameof(nodeConfig.approveNoticeData.Revoked),
                    //    Entity = input.Data,
                    //    To = toList,
                    //    ActivityId = context.ActivityId,
                    //    DateType = busObj.DateType,
                    //    DateValue = busObj.DataId
                    //};
                    //await _workflowCrmService.SendWorkflowEmail(email);


                    var req = new SendEmailWorkflowDto()
                    {
                        DefinitionId = workflowInstance.DefinitionId,//定义
                        WorkflowInstanceId = workflowInstance.Id,//实例
                        NodeConfig = nodeConfig,//当前节点的配置信息
                        NoticeType = nameof(nodeConfig.approveNoticeData.Revoked),//发送邮件的类型
                        Entity = input.Data,//表单数据
                        ActivityId = context.ActivityId,//当前节点的id
                        DateType = busObj.DateType,//关联的业务类型
                        DateValue = busObj.DataId,//关联的业务Id
                        CurrentNodeUsers = taskUsers.Select(a => int.Parse(a.UserId.ToString())).ToList(),//当前节点的审批人
                        ToUserIds = taskUsers.Select(a => int.Parse(a.UserId.ToString())).ToList(),//邮件默认的收件人
                        CcUserIds = new List<int>() { busObj.CreatedById },//邮件默认的抄送人
                    };
                    await _workflowCrmService.SendWorkflowEmail(req);


                    #endregion

                    //执行撤回
                    input.NewTransferId = log.TransferId;
                    input.RevokeOrReturnActivityId = activity.ActivityId;
                    context.WorkflowExecutionContext.ScheduleActivity(input.RevokeOrReturnActivityId, input);
                    return Suspend();
                }
                else if (input.ExecutionType == Consts.WorkflowAction_Returned)//退回
                {
                    //退回判断 判断当前节点是否多个人完成，如果是需要所有人都退回才可以
                    //TODO

                    //退回到哪个节点
                    var revokeOrReturnActivity = context.WorkflowExecutionContext.GetActivityBlueprintById(input.RevokeOrReturnActivityId);
                    if (revokeOrReturnActivity.Type == Consts.Activity_Type_Start)
                    {
                        //添加一条流转记录
                        WorkflowTransferInfo log = new WorkflowTransferInfo()
                        {
                            TransferId = Guid.NewGuid(),
                            WorkflowInstanceId = context.WorkflowInstance.Id,
                            TransferDate = DateTime.Now,
                            TransferType = Consts.WorkflowAction_Resubmit,
                            TransferTypeName = "重新提交",
                            ActivityId = context.ActivityBlueprint.Id,
                            CreatedById = _user.UserId,
                            Display = true,
                            IsAutoPass = (nodeConfig.autoApproval == 1),
                            FromActivityId = context.ActivityBlueprint.Id,
                            Msg = "等待发起人重新提交",
                        };
                        await _workflowCrmRepository.Db.Insertable(log).ExecuteCommandAsync();
                    }

                    #region 取消当前节点的任务 和 任务操作记录
                    await _workflowCrmRepository.Db.Updateable<WorkflowTaskInfo>()
                                                .SetColumns(a => a.TaskStatus == Consts.WorkflowTaskStatus_Completed)
                                                .SetColumns(a => a.Msg == input.Msg)
                                                .SetColumns(a => a.UpdatedById, input.UserId)
                                                .SetColumns(a => a.UpdatedDate, operationDate)
                                                .Where(a => a.TaskId == input.TaskId.Value)
                                                .ExecuteCommandAsync();

                    //操作记录全部取消（可能存在多个人的操作记录）
                    await _workflowCrmRepository.Db.Updateable<WorkflowTaskUserInfo>()
                                                .SetColumns(a => a.Status == Consts.WorkflowTaskStatus_Cancelled)
                                                .SetColumns(a => a.OperationResult, Consts.WorkflowAction_Returned)
                                                .Where(a => a.TaskId == input.TaskId.Value && a.Status == Consts.WorkflowTaskStatus_Pending)
                                                .ExecuteCommandAsync();

                    //当前用户的那条操作记录要标志成退回
                    await _workflowCrmRepository.Db.Updateable<WorkflowTaskUserInfo>()
                                                .SetColumns(a => a.Status == Consts.WorkflowTaskStatus_Completed)
                                                .SetColumns(a => a.OperationMsg == input.Msg)
                                                .SetColumns(a => a.OperationResult, Consts.WorkflowAction_Returned)
                                                .SetColumns(a => a.OperationDate, operationDate)
                                                .Where(a => a.TaskId == input.TaskId.Value && a.UserId == input.UserId)
                                                .ExecuteCommandAsync();
                    #endregion

                    #region 退回邮件通知
                    //根据退回节点的任务查找对应的审批人
                    var prvTask = await _workflowCrmRepository.Db.Queryable<WorkflowTaskInfo>()
                                        .Where(a => a.TaskId == taskObj.FromTaskId)
                                        .OrderByDescending(a => a.CreatedDate)
                                        .FirstAsync();

                    var users = new List<long>();

                    if (prvTask != null)
                    {
                        users = await _workflowCrmRepository.Db.Queryable<WorkflowTaskUserInfo>()
                                                                  .Where(a => a.TaskId == prvTask.TaskId)
                                                                  .Select(a => a.UserId)
                                                                  .ToListAsync();
                    }
                    else
                    {
                        users.Add(busObj.CreatedById);
                    }
                    ////任务发起人的邮箱
                    //var toList = _workflowCrmRepository.Db.Queryable<UserInfo>()
                    //    .Where(a => users.Contains(a.Id) && !string.IsNullOrWhiteSpace(a.Email))
                    //    .Select(a => a.Email).Distinct().ToList();

                    //var req = new SendEmailWorkflowDto()
                    //{
                    //    DefinitionId = workflowInstance.DefinitionId,
                    //    NodeConfig = nodeConfig,
                    //    NoticeType = nameof(nodeConfig.approveNoticeData.Return),
                    //    To = toList,
                    //    Entity = input.Data,
                    //    ActivityId = context.ActivityId,
                    //    DateType = busObj.DateType,
                    //    DateValue = busObj.DataId
                    //};
                    //await _workflowCrmService.SendWorkflowEmail(req);


                    var req = new SendEmailWorkflowDto()
                    {
                        DefinitionId = workflowInstance.DefinitionId,//定义
                        WorkflowInstanceId = workflowInstance.Id,//实例
                        NodeConfig = nodeConfig,//当前节点的配置信息
                        NoticeType = nameof(nodeConfig.approveNoticeData.Return),//发送邮件的类型
                        Entity = input.Data,//表单数据
                        ActivityId = context.ActivityId,//当前节点的id
                        DateType = busObj.DateType,//关联的业务类型
                        DateValue = busObj.DataId,//关联的业务Id
                        CurrentNodeUsers = taskUsers.Select(a => int.Parse(a.UserId.ToString())).ToList(),//当前节点的审批人
                        ToUserIds = users.Select(a=>int.Parse(a.ToString())).ToList(),//邮件默认的收件人
                        CcUserIds = new List<int>() { busObj.CreatedById },//邮件默认的抄送人
                    };
                    await _workflowCrmService.SendWorkflowEmail(req);


                    #endregion

                    //因为会签的审批是在审批完成之前除了最后一个，其他人的审批操作都是临时保存在这里，所以最后一个审批完成后要清空
                    context.SetVariable("Approvals", null);
                    //跳转到对应的节点
                    context.WorkflowExecutionContext.ScheduleActivity(input.RevokeOrReturnActivityId, input);
                    //把当前工作流设置为暂停
                    return Suspend();
                }
                else if (input.ExecutionType == Consts.WorkflowAction_AssignTo)
                {

                    var transferId = _workflowCrmRepository.Db.Queryable<WorkflowTaskInfo>().Where(a => a.TaskId == input.TaskId.Value).First().TransferId;
                    var transferObj = _workflowCrmRepository.Db.Queryable<WorkflowTransferInfo>().Where(a => a.TransferId == transferId).First();
                    var taskUserList = _workflowCrmRepository.Db.Queryable<WorkflowTaskUserInfo>().Where(a => a.TaskId == input.TaskId.Value).ToList();
                    if (taskUserList.Count > 1)
                    {
                        var taskUserObj = taskUserList.Where(a => a.UserId == input.UserId).FirstOrDefault();
                        //转交任务
                        var list = new List<WorkflowTaskUserInfo>();
                        foreach (var owner in input.NextApprovalValue)
                        {
                            var uid = owner.ObjToLong();
                            list.Add(new WorkflowTaskUserInfo()
                            {
                                Id = Guid.NewGuid(),
                                WorkflowInstanceId = input.ElsaInstancesId,
                                TaskId = taskObj.TaskId,
                                Status = Consts.WorkflowTaskStatus_Pending,
                                UserId = uid,
                                CreatedAt = DateTime.Now,
                                CreatedById = taskObj.CreatedById.ObjToInt()
                            });
                        }
                        await _workflowCrmRepository.Db.Insertable(list).ExecuteCommandAsync();

                        //更新我的操作记录
                        if (taskUserObj != null)
                        {
                            taskUserObj.Status = Consts.WorkflowTaskStatus_Completed;
                            taskUserObj.OperationType = input.ExecutionType;
                            taskUserObj.OperationDate = DateTime.Now;
                            taskUserObj.OperationResult = input.ExecutionType;
                            taskUserObj.OperationMsg = input.Msg;
                            await _workflowCrmRepository.Db.Updateable(taskUserObj).ExecuteCommandAsync();
                        }
                    }
                    else
                    {
                        if (transferObj.TransferType == Consts.WorkflowAction_Approve)
                        {
                            _workflowCrmRepository.Db.Updateable<WorkflowTransferInfo>()
                                .SetColumns(a => a.Display == false)
                                .Where(a => a.TransferId == transferObj.TransferId)
                                .ExecuteCommand();
                        }

                        await _workflowCrmRepository.Db.Updateable<WorkflowTaskInfo>()
                                                    .SetColumns(a => a.TaskStatus == Consts.WorkflowTaskStatus_Cancelled)
                                                    .SetColumns(a => a.Msg == input.Msg)
                                                    .SetColumns(a => a.UpdatedById, input.UserId)//取消人
                                                    .SetColumns(a => a.UpdatedDate, DateTime.Now)
                                                    .Where(a => a.TaskId == input.TaskId.Value)
                                                    .ExecuteCommandAsync();

                        await _workflowCrmRepository.Db.Updateable<WorkflowTaskUserInfo>()
                                                    .SetColumns(a => a.Operator == input.UserId)
                                                    .SetColumns(a => a.OperationType == Consts.WorkflowAction_AssignTo)
                                                    .SetColumns(a => a.OperationResult == Consts.WorkflowAction_AssignTo)
                                                    .SetColumns(a => a.OperationDate == DateTime.Now)
                                                    .SetColumns(a => a.Status == Consts.WorkflowTaskStatus_Completed)
                                                    .SetColumns(a => a.OperationMsg == input.Msg)
                                                    .Where(a => a.TaskId == input.TaskId.Value)
                                                    .ExecuteCommandAsync();

                        //添加转办的流转记录
                        WorkflowTransferInfo log = new WorkflowTransferInfo()
                        {
                            TransferId = Guid.NewGuid(),
                            WorkflowInstanceId = context.WorkflowInstance.Id,
                            TransferDate = DateTime.Now,
                            TransferType = input.ExecutionType,
                            TransferTypeName = transferObj.TransferTypeName,
                            ActivityId = context.ActivityBlueprint.Id,
                            CreatedById = _user.UserId,
                            Display = true,
                            FromActivityId = transferObj.FromActivityId,
                            Msg = operationMsg
                        };
                        await _workflowCrmRepository.Db.Insertable(log).ExecuteCommandAsync();

                        //添加审批流转记录
                        log = new WorkflowTransferInfo()
                        {
                            TransferId = Guid.NewGuid(),
                            WorkflowInstanceId = context.WorkflowInstance.Id,
                            TransferDate = DateTime.Now,
                            TransferType = Consts.Activity_Type_Approved,
                            TransferTypeName = transferObj.TransferTypeName,
                            ActivityId = context.ActivityBlueprint.Id,
                            CreatedById = _user.UserId,
                            Display = true,
                            FromActivityId = transferObj.FromActivityId,
                            Msg = operationMsg
                        };
                        await _workflowCrmRepository.Db.Insertable(log).ExecuteCommandAsync();

                        //复制任务重新添加一个
                        taskObj.TaskId = Guid.NewGuid();
                        taskObj.TransferId = log.TransferId;
                        //taskObj.CreatedById = input.UserId;
                        taskObj.CreatedDate = DateTime.Now;
                        taskObj.Msg = string.Format("于 {0} 生成了一个审批任务；", DateTime.Now.ToString());
                        await _workflowCrmRepository.Db.Insertable(taskObj).ExecuteCommandAsync();

                        //转交任务
                        var list = new List<WorkflowTaskUserInfo>();
                        foreach (var owner in input.NextApprovalValue)
                        {
                            var uid = owner.ObjToLong();
                            list.Add(new WorkflowTaskUserInfo()
                            {
                                Id = Guid.NewGuid(),
                                WorkflowInstanceId = input.ElsaInstancesId,
                                TaskId = taskObj.TaskId,
                                Status = Consts.WorkflowTaskStatus_Pending,
                                UserId = uid,
                                CreatedAt = DateTime.Now,
                                CreatedById = taskObj.CreatedById.ObjToInt()
                            });
                        }
                        await _workflowCrmRepository.Db.Insertable(list).ExecuteCommandAsync();
                    }

                    #region 转办要告知待办人发邮件
                    if (input.NextApprovalValue != null)
                    {
                        //var toList = _workflowCrmRepository.Db.Queryable<UserInfo>()
                        //    .Where(a => input.NextApprovalValue.Contains(a.Id) && !string.IsNullOrWhiteSpace(a.Email))
                        //    .Select(a => a.Email).Distinct().ToList();

                        //var req = new SendEmailWorkflowDto()
                        //{
                        //    DefinitionId = workflowInstance.DefinitionId,
                        //    NodeConfig = nodeConfig,
                        //    NoticeType = nameof(nodeConfig.approveNoticeData.AssignTo),
                        //    To = toList,
                        //    Entity = input.Data,
                        //    ActivityId = context.ActivityId,
                        //    DateType = busObj.DateType,
                        //    DateValue = busObj.DataId
                        //};
                        //await _workflowCrmService.SendWorkflowEmail(req);

                        var req = new SendEmailWorkflowDto()
                        {
                            DefinitionId = workflowInstance.DefinitionId,//定义
                            WorkflowInstanceId = workflowInstance.Id,//实例
                            NodeConfig = nodeConfig,//当前节点的配置信息
                            NoticeType = nameof(nodeConfig.approveNoticeData.AssignTo),//发送邮件的类型
                            Entity = input.Data,//表单数据
                            ActivityId = context.ActivityId,//当前节点的id
                            DateType = busObj.DateType,//关联的业务类型
                            DateValue = busObj.DataId,//关联的业务Id
                            CurrentNodeUsers = taskUsers.Select(a => int.Parse(a.UserId.ToString())).ToList(),//当前节点的审批人
                            ToUserIds = input.NextApprovalValue.Select(a => int.Parse(a.ToString())).ToList(),//邮件默认的收件人
                            CcUserIds = new List<int>() { busObj.CreatedById },//邮件默认的抄送人
                        };
                        await _workflowCrmService.SendWorkflowEmail(req);

                    }
                    #endregion

                    #region 告知任务的发起人当前任务已转办 、并抄送给流程的发起人
                    if (true)
                    {
                        var toList = _workflowCrmRepository.Db.Queryable<UserInfo>()
                            .Where(a => a.Id == taskObj.CreatedById && !string.IsNullOrWhiteSpace(a.Email))
                            .Select(a => a.Email).ToList();

                        //同时抄送一份给流程的发起人
                        var ccList = _workflowCrmRepository.Db.Queryable<UserInfo>()
                                        .Where(a => a.Id == busObj.CreatedById && !string.IsNullOrWhiteSpace(a.Email))
                                        .Select(a => a.Email).Distinct().ToList();

                        var req = new SendEmailWorkflowDto()
                        {
                            DefinitionId = workflowInstance.DefinitionId,
                            NodeConfig = nodeConfig,
                            NoticeType = "AssignTo_WorkflowAdd",
                            To = toList,
                            Cc = ccList,
                            Entity = input.Data,
                            ActivityId = context.ActivityId,
                            DateType = busObj.DateType,
                            DateValue = busObj.DataId
                        };
                        await _emailsService.SendEmailByName(req);

                    }
                    #endregion

                    return Suspend();
                }
                else
                {

                }
            }

            return Done();
        }

        /// <summary>
        /// 判断操作是否可以执行，返回true 则触发 OnResumeAsync 时间
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        protected override bool OnCanExecute(ActivityExecutionContext context)
        {
            if (context.Input is StartWorkflowDto)
            {
                var input = context.GetInput<StartWorkflowDto>();
                return input != null;
            }

            if (context.Input is WorkflowExecutionDto)
            {
                var input = context.GetInput<WorkflowExecutionDto>();
                var isContain = UserActions.Contains(input.ExecutionType);

                if (input.TaskId.HasValue)
                {
                    //任务信息
                    var taskObj = _workflowCrmRepository.Db.Queryable<WorkflowTaskInfo>().Where(a => a.TaskId == input.TaskId.Value).First();
                    //任务必须是未完成的
                    return taskObj.TaskStatus == Consts.WorkflowTaskStatus_Pending && isContain;
                }

                //退回判断 判断当前节点是否多个人完成，如果是需要所有人都退回才可以
                //TODO

                return isContain;
            }

            return base.OnCanExecute(context);
        }

        /// <summary>
        /// 获取流转名称
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        private string GetTransferTypeName(ActivityExecutionContext context)
        {
            return context.ActivityBlueprint.Type;
        }

        /// <summary>
        /// 获取当前活动对应的名称
        /// </summary>
        /// <param name="activityBlueprint"></param>
        /// <returns></returns>
        private string GetActivityBlueprintName(IActivityBlueprint activityBlueprint)
        {
            if (activityBlueprint.Type == nameof(StartActivity))
                return "开始";
            if (string.IsNullOrWhiteSpace(activityBlueprint.DisplayName))
                return activityBlueprint.Name;

            return activityBlueprint.DisplayName;
        }

        /// <summary>
        /// 获取当前活动对应的名称
        /// </summary>
        /// <param name="activity"></param>
        /// <returns></returns>
        private string GetActivityName(ActivityDefinition activity)
        {
            if (activity.Type == nameof(StartActivity))
                return "开始";
            if (string.IsNullOrWhiteSpace(activity.DisplayName))
                return activity.Name;

            return activity.DisplayName;
        }

        /// <summary>
        /// 判断是否是审批操作
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        private bool SetIsActionApprove(ActivityExecutionContext context)
        {
            if (context.Input is WorkflowExecutionDto)
            {
                var input = context.GetInput<WorkflowExecutionDto>();
                return input.ExecutionType == Consts.WorkflowAction_Approve;
            }
            return false;
        }

        /// <summary>
        /// 获取当前的操作类型
        /// </summary>
        /// <param name="context"></param>
        /// <param name="nodeConfig"></param>
        /// <returns></returns>
        private Tuple<string, string> GetOperationType(ActivityExecutionContext context, DesignNodeProperty nodeConfig)
        {
            var operationType = string.Empty;
            var operationMsg = string.Empty;
            if (context.Input is StartWorkflowDto)
            {
                operationType = Consts.WorkflowAction_Started;
            }
            else if (context.Input is WorkflowExecutionDto)
            {
                var input = context.GetInput<WorkflowExecutionDto>();
                operationType = input.ExecutionType;
                operationMsg = input.Msg;
            }
            else if (nodeConfig != null)
            {
                if (nodeConfig.autoApproval == 0)
                    operationType = context.ActivityBlueprint.Type;
                if (nodeConfig.autoApproval == 1)
                    operationType = Consts.WorkflowAction_AutoPass;
            }

            return new(operationType, operationMsg);
        }

        /// <summary>
        /// 判断任务人是否都已经处理了
        /// </summary>
        /// <param name="workflowInstanceId"></param>
        /// <param name="taskId"></param>
        /// <returns></returns>
        private bool AllTaskUserFinished(string workflowInstanceId, Guid taskId)
        {
            var result = _workflowCrmRepository.Db.Queryable<WorkflowTaskUserInfo>().Where(a => a.TaskId == taskId && a.Status == Consts.WorkflowTaskStatus_Pending).Any();

            return !result;
        }


        private DesignNodeProperty GetNodeConfig(ActivityDefinition activity)
        {
            var config = activity.Properties.Where(a => a.Name == "Config").FirstOrDefault();
            if (config == null) return null;

            var expression = config.GetExpression(SyntaxNames.Literal);
            if (expression == null) return null;

            var designProperty = JsonConvert.DeserializeObject<DesignNodeProperty>(expression);
            if (designProperty == null)
                return null;
            else
                return designProperty;
        }

        public string GetBaseTypeName(string className)
        {
            // 加载包含您想要获取的类型的程序集
            Assembly assembly = Assembly.GetExecutingAssembly();

            // 获取程序集中的所有类型
            Type[] types = assembly.GetTypes();

            // 使用 LINQ 查询来查找您想要的类型
            Type targetType = types.FirstOrDefault(type => type.Name == className);

            // 检查 targetType 是否为 Approved 类型的子类
            if (targetType != null && targetType.IsSubclassOf(typeof(Approved)))
            {
                // targetType 的基类是 Approved 类型
                return nameof(Approved);
            }
            else
            {
                // targetType 的基类不是 Approved 类型
                return className;
            }
        }


        #region 发送邮件
        private void SendEmailTodo()
        {

        }

        #endregion

    }
}
