using System;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.DependencyInjection;
using YunLanCrm.Extensions.Authorizations.ApiKey;
using YunLanCrm.IServices;
using YunLanCrm.Services;

namespace YunLanCrm.Extensions
{
    /// <summary>
    /// API Key认证配置
    /// </summary>
    public static class ApiKeyAuthenticationSetup
    {
        /// <summary>
        /// 添加API Key认证服务
        /// </summary>
        /// <param name="services"></param>
        public static void AddApiKeyAuthenticationSetup(this IServiceCollection services)
        {
            if (services == null) throw new ArgumentNullException(nameof(services));

            // 注意：IApiKeyService和ApiKeyService会通过Autofac自动注册，这里不需要手动注册
            // 注意：不能调用AddAuthentication()，因为JWT设置中已经调用了，这里只添加方案
        }

        /// <summary>
        /// 添加API Key认证方案到现有的认证配置中
        /// 必须在JWT认证设置之后调用
        /// </summary>
        /// <param name="builder"></param>
        /// <returns></returns>
        public static AuthenticationBuilder AddApiKeyAuthentication(this AuthenticationBuilder builder)
        {
            return builder.AddScheme<ApiKeyAuthenticationSchemeOptions, ApiKeyAuthenticationHandler>(
                ApiKeyAuthenticationSchemeOptions.DefaultScheme,
                options => { });
        }
    }
}
