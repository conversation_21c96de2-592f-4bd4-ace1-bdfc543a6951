export interface CmTicketsAddOrUpdateDto {
	id?: number;
	createdById: number;
	ticket_Parent_Id: number;
	ticket_Number: string;
	ticket_Status: string;
	ticket_Subject: string;
	ticket_Priority: string;
	ticket_Category: string;
	ticket_First_Name: string;
	ticket_Last_Name: string;
	ticket_Customer: string;
	ticket_Customer_Email: string;
	ticket_Customer_Phone: string;
	ticket_From: string;
	ticket_Open_By: number;
	ticket_Close_By: number;
	assignToId?: number;
	assignToType: boolean;
	ticketAttachments: any[];
	ticketContentsInfo: any;
	assigToId: any[];
	executionType: string;
	activityId: string;
	trigger: string;
	taskId?: string;
	ticket_Open_Date: string;
	ticket_Close_Date: string;
	nodeId: string;
	ticket_Content: string;
	ticket_Content_Type: string;
	ticket_Project: string;
	userTrusteeId: number;
	UserTrusteeIds: any[];
	OnHold: boolean;
	ScheduledDate: string;
	parent_ticket_Number: string;
}
