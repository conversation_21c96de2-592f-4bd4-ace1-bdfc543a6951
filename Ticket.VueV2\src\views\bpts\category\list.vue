<template>
	<div class="list-page-layout">
		<el-container style="height: 100%">
			<el-header style="height: auto">
				<el-card class="list-search-card" shadow="never" style="width: 100%" :body-style="{ paddingTop: '10px' }">
					<div class="list-index-search">
						<el-form label-width="150px" @keyup.enter="onSearch">
							<el-row :gutter="35">
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="8" class="mb6">
									<el-form-item :label="$t('message.categoryFields.Category_Code')">
										<el-input v-model="state.tableData.params.category_Code" clearable class="w-20"></el-input>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="8" class="mb6">
									<el-form-item :label="$t('message.categoryFields.Category_Description')">
										<el-input v-model="state.tableData.params.category_Description" clearable class="w-20"></el-input>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="8" class="mb6">
									<el-form-item :label="$t('message.categoryFields.Category_Status')">
										<el-select
											:placeholder="$t('message.page.selectKeyPlaceholder')"
											v-model="state.tableData.params.category_Status"
											clearable
											class="w-20"
										>
											<el-option :label="$t('message.userFields.Active')" :value="0"></el-option>
											<el-option :label="$t('message.userFields.Inactive')" :value="1"></el-option>
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="8" class="mb6">
									<el-form-item :label="$t('message.categoryFields.Category_Company')">
										<el-tree-select
											ref="localCompanyTree"
											v-model="state.tableData.params.category_Company"
											:data="state.customerOpts"
											:render-after-expand="false"
											node-key="orgId"
											show-checkbox
											:check-strictly="true"
											check-on-click-node
											multiple
											collapse-tags
											:default-expand-all="true"
											class="w-20"
											:placeholder="$t('message.page.selectKeyPlaceholder')"
											:props="{ value: 'orgId', label: 'name' }"
											filterable 
											clearable
											@change="handleProjectsCheckChange">
											<template #header>
												<el-checkbox v-model="state.projectsCheckAll"
													@change="handleProjectsCheckAll">
													All
												</el-checkbox>
											</template>
										</el-tree-select>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="8" class="mb6">
									<el-form-item :label="$t('message.categoryFields.NewTicketAssignee1')">
										<el-tree-select
											ref="localCompanyTree"
											v-model="state.tableData.params.defaultAssignee"
											:data="state.assigneeOpts"
											:render-after-expand="false"
											node-key="value"
											show-checkbox
											:check-strictly="true"
											check-on-click-node
											multiple
											collapse-tags
											:default-expand-all="true"
											class="w-20"
											:placeholder="$t('message.page.selectKeyPlaceholder')"
											:props="{ value: 'value', label: 'label' }"
											filterable 
											clearable
											@change="handleAssigneeCheckChange">
											<template #header>
												<el-checkbox v-model="state.assigneeCheckAll"
													@change="handleAssigneeCheckAll">
													All
												</el-checkbox>
											</template>
										</el-tree-select>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="8" class="mb6">
									<el-form-item>
										<el-button size="small" type="primary" @click="onSearch"> {{ $t('message.page.buttonSearch') }} </el-button>
										<el-button size="small" type="danger" @click="onSearchReSet"> {{ $t('message.page.buttonReset') }} </el-button>
									</el-form-item>
								</el-col>
							</el-row>
						</el-form>
					</div>
				</el-card>
			</el-header>

			<el-header class="table_header mt5" style="display: flex">
				<div class="left-panel">
					<el-button v-auth="'ticketCategory.Create'" size="small" type="primary" class="ml10" @click="onAdd">
						{{ $t('message.categoryButtons.createCategory') }}
					</el-button>
				</div>
				<div class="right-panel">
					<el-dropdown>
						<el-button v-auth="'ticketCategory.Export'" type="primary" size="small" class="ml10">
							<el-icon>
								<ele-ArrowDownBold />
							</el-icon>
							{{ $t('message.page.buttonExport') }}
						</el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item @click="onExportAllRecord(0)">{{ $t('message.page.buttonExportEntireList') }}</el-dropdown-item>
								<el-dropdown-item @click="onExportAllRecord(1)">{{ $t('message.page.buttonExportSelectedRecords') }}</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</div>
			</el-header>

			<el-main style="flex: 1">
				<div class="scTable" style="height: 100%" ref="scTableMain">
					<div class="scTable-table">
						<el-table
							:data="state.tableData.data"
							v-loading="state.tableData.loading"
							height="calc(100%)"
							table-layout="fixed"
							lazy
							highlight-current-row
							scrollbar-always-on
							@selection-change="selectionChange"
							@sort-change="onSortChange"
							:row-style="{ height: '40px' }"
							:cell-style="{ padding: '0px' }"
							:default-sort="{ prop: 'category_Code', order: 'ascending' }" stripe border
						>
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')" :image-size="100"></el-empty>
							</template>
							<el-table-column type="selection" />

							<el-table-column
								sortable
								:label="$t('message.categoryFields.Category_Code')"
								prop="category_Code"
								show-overflow-tooltip
								:min-width="160"
								:sort-orders="['ascending', 'descending']"
							>
								<template #default="{ row }">
									{{ row.category_Code }}
								</template>
							</el-table-column>

							<el-table-column
								sortable
								:label="$t('message.categoryFields.Category_Description')"
								prop="category_Description"
								show-overflow-tooltip
								:min-width="220"
								:sort-orders="['ascending', 'descending']"
							>
								<template #default="{ row }">
									{{ row.category_Description }}
								</template>
							</el-table-column>

							<el-table-column
								sortable
								:label="$t('message.categoryFields.Category_Status')"
								prop="category_Status"
								show-overflow-tooltip
								:min-width="170"
								:sort-orders="['ascending', 'descending']"
							>
								<template #default="{ row }">
									<el-tag type="danger" v-if="row.category_Status == '1'">{{ $t('message.userFields.Inactive') }}</el-tag>
									<el-tag type="success" v-if="row.category_Status == '0'">{{ $t('message.userFields.Active') }}</el-tag>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.categoryFields.Category_Company')" prop="companyName" show-overflow-tooltip :min-width="150">
								<template #default="{ row }">
									{{ row.companyName }}
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.categoryFields.NewTicketAssignee')" prop="defaultAssignee" show-overflow-tooltip :min-width="260">
								<template #default="{ row }">
									{{ row.defaultAssignee }}
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.page.actions')" :min-width="120">
								<template #default="scope">
									<el-row>
										<el-col :xs="24" :sm="18" :md="12" :lg="9" :xl="9">
											<el-button v-auth="'ticketCategory.Edit'" size="mini" type="text" @click="onOpenEditMenu(scope.row, 'Edit')">{{
												$t('message.page.actionsEdit')
											}}</el-button>
										</el-col>
										<el-col :xs="24" :sm="18" :md="12" :lg="9" :xl="9">
											<el-button v-auth="'ticketCategory.Delete'" size="mini" type="text" style="color: #ff3a3a" @click="onTabelRowDel(scope.row)">{{
												$t('message.page.actionsDelete')
											}}</el-button>
										</el-col>
										<el-col :xs="24" :sm="18" :md="12" :lg="9" :xl="9">
											<el-button
												v-auth="'ticketCategory.AddCompany'"
												size="mini"
												type="text"
												style="color: #77e577"
												@click="onOpenEditMenu(scope.row, 'Add Other Company')"
												>{{ $t('message.categoryButtons.addOtherCompany') }}</el-button
											>
										</el-col>
									</el-row>
								</template>
							</el-table-column>
						</el-table>

						<div class="scTable-page">
							<el-pagination
								@size-change="onSizechange"
								@current-change="onCurrentChange"
								:pager-count="5"
								:page-sizes="[10, 20, 30]"
								v-model:current-page="state.tableData.params.pageIndex"
								background
								v-model:page-size="state.tableData.params.pageSize"
								layout="total, sizes, prev, pager, next, jumper"
								:total="state.tableData.total"
								small
							>
							</el-pagination>
						</div>
					</div>
				</div>
			</el-main>

			<EditCategory ref="editCategoryRef" @fetchData="onInit" />
		</el-container>
	</div>
</template>

<script lang="ts" setup name="ticketCategory">
import { nextTick, ref, toRefs, reactive, onMounted, defineAsyncComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';

import cmCategoryApi from '/@/api/cmCategory/index';
import organizationApi from '/@/api/organization/index';
import { formatDateTime } from '/@/utils/formatTime';
import userInfoApi from '/@/api/user/index';
const EditCategory = defineAsyncComponent(() => import('./component/createOrEdit.vue'));

const Auth = defineAsyncComponent(() => import('/@/components/auth/auth.vue'));

const localCompanyTree = ref();
const editCategoryRef = ref();

const { t } = useI18n();

const state = reactive({
	tableData: {
		data: [],
		total: 0,
		loading: false,
		selection: [],
		params: {
			pageIndex: 1,
			pageSize: 10,
			searchKey: '',
			order: 'CreatedAt',
			sort: 'desc',
			category_Code: '',
			category_Description: '',
			defaultAssignee:[],
			category_Status: '',
			category_Company: [],
			ids: [],
			ischeckType: 0,
		},
	},
	customerOpts: [],
	assigneeOpts:[],
	projectsCheckAll: false,
	assigneeCheckAll:false,
});

const onInit = (loadData: any = 'No') => {
	state.tableData.loading = true;
//console.log(state.tableData.params)
	cmCategoryApi
		.Query(state.tableData.params)
		.then((rs: any) => {
			state.tableData.data = [];
			state.tableData.data = rs.data;
			state.tableData.total = rs.totalCount;
		})
		.catch(() => {})
		.finally(() => {
			state.tableData.loading = false;

			if (loadData == 'Yes') {
				
				organizationApi.Tree({ IncludeProject: 1 }).then((rs) => {
					state.customerOpts = rs.data;
					//console.log("aaaaaaaaaaaaa:",rs.data);
				});
			}
		});

		userInfoApi.GetAllGroupUserListV5({}).then((rs) => {
			state.assigneeOpts = rs.data.groupList;
		});
};

const initFormData = () => {
	state.tableData.params = {
		pageIndex: 1,
		pageSize: 10,
		searchKey: '',
		order: 'CreatedAt',
		sort: 'desc',
		category_Code: '',
		category_Description: '',
		defaultAssignee:[],
		category_Status: '',
		category_Company: [],
	} as any;
	state.projectsCheckAll = false;
	state.assigneeCheckAll=false;
};

const onSearch = () => {
	onInit();
};

const onSearchReSet = () => {
	initFormData();
	onInit();
};

const onAdd = () => {
	editCategoryRef.value.openDialog({ action: 'Create' });
};

const onOpenEditMenu = (row: any, actionType: any) => {
	editCategoryRef.value.openDialog({
		action: actionType,
		id: row.id,
		companyId: row.category_Company,
		cmCategoryTalbeRelationId: row.cmCategoryTalbeRelationId,
	});
};

const onTabelRowDel = (row: any) => {
	ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
		confirmButtonText: t('message.page.confirm'),
		cancelButtonText: t('message.page.cannel'),
		type: 'warning',
		closeOnClickModal:false,
	})
		.then(() => {
			//弃用
			// cmCategoryApi.DeleteByKey(row.id).then((rs) => {
			// 	ElMessage.success(t('message.page.deleteSuccess'));
			// 	onInit();
			// });

			cmCategoryApi
				.DeleteCategoryByKey(row)
				.then((rs) => {
					ElMessage.success(t('message.page.deleteSuccess'));
					onInit();
				})
				.catch((rs) => {
					nextTick(() => {
						ElMessageBox.alert(t('message.page.existCanNotDelete'), t('message.page.dlgTip'), {
							type: 'warning',
						});
					});
				});
		})
		.catch(() => {});
};

const selectionChange = (selection: any) => {
	state.tableData.selection = selection;
};

const onSizechange = (val: number) => {
	state.tableData.params.pageSize = val;
	onInit();
};

const onCurrentChange = (val: number) => {
	state.tableData.params.pageIndex = val;
	onInit();
};

const onSortChange = (column: any) => {
	state.tableData.params.order = column.prop;
	state.tableData.params.sort = column.order;
	onInit();
};

// 通过check的回调里面获取节点id,再获取节点的node对象
const checkChange = (data: any) => {
	//根据key拿到Tree组件中的node节点
	const node = localCompanyTree.value.getNode(data.value);
	//调用节点处理方法
	setNode(node);
};

//递归设置子节点和父节点
const setNode = (node: any) => {
	if (node.checked) {
		if (node.level == 1) {
			setChildNode(node);
		} else if (node.level == 2) {
			setParentNode(node);
		}
	}
};

//递归设置父节点全部取消选中
const setParentNode = (node: any) => {
	if (node.parent) {
		for (const key in node) {
			if (key === 'parent') {
				node[key].checked = false;
				//递归调用相应父节点处理函数
				setParentNode(node[key]);
			}
		}
	}
};

//递归设置子节点全部取消选中
const setChildNode = (node: any) => {
	if (node.childNodes && node.childNodes.length) {
		node.childNodes.forEach((item: any) => {
			item.checked = false;

			//递归调用相应子节点处理函数
			setChildNode(item);
		});
	}
};

const onExportAllRecord = (selectAll: number) => {
	let currPageSize = state.tableData.params.pageSize;
	state.tableData.params.pageSize = 10000;

	var checkSelection = state.tableData.selection;
	let ids_arr = [] as any;

	checkSelection.forEach(function (item, index) {
		let idjson = {} as any;
		idjson = item.id;
		ids_arr.push(idjson);
	});
	state.tableData.params.ischeckType = selectAll;
	if (selectAll == 1 && ids_arr.length == 0) {
		ElMessage.error(t('message.userFields.SelectTheDataYouWantToExport'));
		return;
	}
	if (selectAll == 0) {
		state.tableData.params.ids = [];
	} else {
		state.tableData.params.ids = ids_arr;
	}

	cmCategoryApi
		.Export(state.tableData.params)
		.then((rs) => {
			downloadCallback(rs);
		})
		.catch((rs) => {})
		.finally(() => (state.tableData.loading = false));

	state.tableData.params.pageSize = currPageSize;
};

const downloadCallback = (rs) => {
	let data = rs;
	var newBlob = new Blob([data], { type: 'text/plain;charset=UTF-8' });
	var anchor = document.createElement('a');
	anchor.download = 'Category_' + formatDateTime() + '.xlsx';
	anchor.href = window.URL.createObjectURL(newBlob);
	anchor.click();
};
//全选开始--------------------------------------

//返回所有节点值
const GetTreeAllNode = (nodes, allNodesIds) => {
			nodes.forEach(item => {
				allNodesIds.push(item.orgId);
				if (item.children && item.children.length) {
					GetTreeAllNode(item.children, allNodesIds);
				}
			});
		};

		const handleProjectsCheckChange = (value: any) => {
			if (value.length == 0) {
				state.projectsCheckAll = false;
			}
		};

		const handleProjectsCheckAll = (value: boolean) => {
			if (value) {
				let allNodesIds = [] as any;
				GetTreeAllNode(state.customerOpts, allNodesIds);
				//console.log("allNodesIds:",allNodesIds);
				state.tableData.params.category_Company = allNodesIds;

			}
			else {
				state.tableData.params.category_Company = [];
			}
		};
		const GetTreeAssigneeAllNode = (nodes, allNodesIds) => {
			nodes.forEach(item => {
				allNodesIds.push(item.value);
				if (item.children && item.children.length) {
					GetTreeAssigneeAllNode(item.children, allNodesIds);
				}
			});
		};
		const handleAssigneeCheckChange = (value: any) => {
			if (value.length == 0) {
				state.assigneeCheckAll = false;
			}
		};

		const handleAssigneeCheckAll = (value: boolean) => {
			if (value) {
				let allNodesIds = [] as any;
				GetTreeAssigneeAllNode(state.assigneeOpts, allNodesIds);
				console.log("allNodesIds:",allNodesIds);
				state.tableData.params.defaultAssignee = allNodesIds;

			}
			else {
				state.tableData.params.defaultAssignee = [];
			}
		};
		
//全选结束---------------------------------------
onMounted(() => {
	onInit('Yes');
});
</script>
