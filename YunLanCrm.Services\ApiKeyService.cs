using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using YunLanCrm.IServices;
using YunLanCrm.Model.Models;
using YunLanCrm.IRepository.Base;

namespace YunLanCrm.Services
{
    /// <summary>
    /// API Key服务实现
    /// </summary>
    public class ApiKeyService : BaseService<ApiKeyInfo>, IApiKeyService
    {
        private readonly ILogger<ApiKeyService> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public ApiKeyService(ILogger<ApiKeyService> logger, IHttpContextAccessor httpContextAccessor, IBaseRepository<ApiKeyInfo> baseRepository)
        {
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            base.BaseDal = baseRepository;
        }

        /// <summary>
        /// 验证API Key
        /// </summary>
        public async Task<ApiKeyInfo> ValidateApiKeyAsync(string apiKey)
        {
            if (string.IsNullOrWhiteSpace(apiKey))
                return null;

            try
            {
                // 对提供的API Key进行哈希处理
                var hashedApiKey = HashApiKey(apiKey);

                // 从数据库查找匹配的API Key
                var apiKeyInfo = await BaseDal.Db.Queryable<ApiKeyInfo>()
                    .Where(x => x.KeyValue == hashedApiKey && x.IsActive)
                    .FirstAsync();

                if (apiKeyInfo == null)
                    return null;

                // 检查IP限制
                if (!string.IsNullOrEmpty(apiKeyInfo.AllowedIPs))
                {
                    var allowedIPs = JsonConvert.DeserializeObject<List<string>>(apiKeyInfo.AllowedIPs);
                    var clientIP = GetClientIP();
                    
                    if (allowedIPs?.Any() == true && !allowedIPs.Contains(clientIP))
                    {
                        _logger.LogWarning($"API Key {apiKeyInfo.Name} access denied from IP {clientIP}");
                        return null;
                    }
                }

                // 检查端点限制
                if (!string.IsNullOrEmpty(apiKeyInfo.AllowedEndpoints))
                {
                    var allowedEndpoints = JsonConvert.DeserializeObject<List<string>>(apiKeyInfo.AllowedEndpoints);
                    var currentPath = _httpContextAccessor.HttpContext?.Request.Path.Value;
                    
                    if (allowedEndpoints?.Any() == true && !allowedEndpoints.Any(endpoint => 
                        currentPath?.StartsWith(endpoint, StringComparison.OrdinalIgnoreCase) == true))
                    {
                        _logger.LogWarning($"API Key {apiKeyInfo.Name} access denied for endpoint {currentPath}");
                        return null;
                    }
                }

                return apiKeyInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating API Key");
                return null;
            }
        }

        /// <summary>
        /// 创建新的API Key
        /// </summary>
        public async Task<(ApiKeyInfo apiKeyInfo, string plainTextKey)> CreateApiKeyAsync(
            string name,
            string externalSystemName,
            string description = null,
            DateTime? expiryDate = null,
            List<string> allowedIPs = null,
            List<string> allowedEndpoints = null,
            long createdByUserId = 1)
        {
            // 生成随机API Key
            var plainTextKey = GenerateApiKey();
            var hashedKey = HashApiKey(plainTextKey);

            var apiKeyInfo = new ApiKeyInfo
            {
                Name = name,
                KeyValue = hashedKey,
                ExternalSystemName = externalSystemName,
                Description = description,
                ExpiryDate = expiryDate,
                AllowedIPs = allowedIPs?.Any() == true ? JsonConvert.SerializeObject(allowedIPs) : null,
                AllowedEndpoints = allowedEndpoints?.Any() == true ? JsonConvert.SerializeObject(allowedEndpoints) : null,
                CreatedByUserId = createdByUserId,
                CreateTime = DateTime.Now,
                IsActive = true
            };

            await BaseDal.Db.Insertable(apiKeyInfo).ExecuteReturnEntityAsync();

            _logger.LogInformation($"Created new API Key: {name} for system: {externalSystemName}");

            return (apiKeyInfo, plainTextKey);
        }

        /// <summary>
        /// 禁用API Key
        /// </summary>
        public async Task<bool> DisableApiKeyAsync(long apiKeyId)
        {
            var result = await BaseDal.Db.Updateable<ApiKeyInfo>()
                .SetColumns(x => x.IsActive == false)
                .Where(x => x.Id == apiKeyId)
                .ExecuteCommandAsync();

            return result > 0;
        }

        /// <summary>
        /// 启用API Key
        /// </summary>
        public async Task<bool> EnableApiKeyAsync(long apiKeyId)
        {
            var result = await BaseDal.Db.Updateable<ApiKeyInfo>()
                .SetColumns(x => x.IsActive == true)
                .Where(x => x.Id == apiKeyId)
                .ExecuteCommandAsync();

            return result > 0;
        }

        /// <summary>
        /// 记录API Key使用情况
        /// </summary>
        public async Task LogApiKeyUsageAsync(
            long apiKeyId,
            string requestPath,
            string httpMethod,
            string clientIP = null,
            string userAgent = null,
            int? responseStatusCode = null,
            long? processingTimeMs = null)
        {
            try
            {
                var usageLog = new ApiKeyUsageLog
                {
                    ApiKeyId = apiKeyId,
                    RequestPath = requestPath,
                    HttpMethod = httpMethod,
                    ClientIP = clientIP ?? GetClientIP(),
                    UserAgent = userAgent ?? GetUserAgent(),
                    ResponseStatusCode = responseStatusCode,
                    ProcessingTimeMs = processingTimeMs,
                    RequestTime = DateTime.Now
                };

                await BaseDal.Db.Insertable(usageLog).ExecuteCommandAsync();

                // 更新API Key的最后使用时间和使用次数
                await BaseDal.Db.Updateable<ApiKeyInfo>()
                    .SetColumns(x => new ApiKeyInfo
                    {
                        LastUsedTime = DateTime.Now,
                        UsageCount = x.UsageCount + 1
                    })
                    .Where(x => x.Id == apiKeyId)
                    .ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error logging API Key usage for key ID: {apiKeyId}");
            }
        }

        /// <summary>
        /// 获取API Key列表
        /// </summary>
        public async Task<List<ApiKeyInfo>> GetApiKeysAsync()
        {
            return await BaseDal.Db.Queryable<ApiKeyInfo>()
                .OrderBy(x => x.CreateTime, SqlSugar.OrderByType.Desc)
                .ToListAsync();
        }

        /// <summary>
        /// 获取API Key使用统计
        /// </summary>
        public async Task<List<ApiKeyUsageLog>> GetApiKeyUsageStatsAsync(long apiKeyId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = BaseDal.Db.Queryable<ApiKeyUsageLog>()
                .Where(x => x.ApiKeyId == apiKeyId);

            if (startDate.HasValue)
                query = query.Where(x => x.RequestTime >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(x => x.RequestTime <= endDate.Value);

            return await query.OrderBy(x => x.RequestTime, SqlSugar.OrderByType.Desc)
                .ToListAsync();
        }

        /// <summary>
        /// 删除API Key
        /// </summary>
        public async Task<bool> DeleteApiKeyAsync(long apiKeyId)
        {
            // 先删除使用日志
            await BaseDal.Db.Deleteable<ApiKeyUsageLog>()
                .Where(x => x.ApiKeyId == apiKeyId)
                .ExecuteCommandAsync();

            // 再删除API Key
            var result = await BaseDal.Db.Deleteable<ApiKeyInfo>()
                .Where(x => x.Id == apiKeyId)
                .ExecuteCommandAsync();

            return result > 0;
        }

        #region 私有方法

        /// <summary>
        /// 生成API Key
        /// </summary>
        private string GenerateApiKey()
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            var result = new StringBuilder(64);

            for (int i = 0; i < 64; i++)
            {
                result.Append(chars[random.Next(chars.Length)]);
            }

            return result.ToString();
        }

        /// <summary>
        /// 对API Key进行哈希处理
        /// </summary>
        private string HashApiKey(string apiKey)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(apiKey));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        /// <summary>
        /// 获取客户端IP地址
        /// </summary>
        private string GetClientIP()
        {
            var context = _httpContextAccessor.HttpContext;
            if (context == null) return "Unknown";

            var ip = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ip))
                ip = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (string.IsNullOrEmpty(ip))
                ip = context.Connection.RemoteIpAddress?.ToString();

            return ip ?? "Unknown";
        }

        /// <summary>
        /// 获取用户代理
        /// </summary>
        private string GetUserAgent()
        {
            var context = _httpContextAccessor.HttpContext;
            return context?.Request.Headers["User-Agent"].FirstOrDefault() ?? "Unknown";
        }

        #endregion
    }
}
