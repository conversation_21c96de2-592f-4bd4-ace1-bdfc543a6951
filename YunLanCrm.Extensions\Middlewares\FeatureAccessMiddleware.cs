﻿using System;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using YunLanCrm.IServices;
using YunLanCrm.Model;
using YunLanCrm.Services;
using Microsoft.AspNetCore.Authorization;

namespace YunLanCrm.Extensions.Middlewares
{
    public class FeatureAccessMiddleware
    {
        private readonly ILogger<FeatureAccessMiddleware> _logger;
        private readonly RequestDelegate _next;
        private readonly IRedisBasketRepository _redisRepository;
        private readonly IConfigService _configService;

        public FeatureAccessMiddleware(RequestDelegate next, IRedisBasketRepository redisRepository, ILogger<FeatureAccessMiddleware> logger, IConfigService configService)
        {
            _logger = logger;
            _next = next;
            _redisRepository = redisRepository;
            _configService = configService;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                var sysConfig = await _configService.GetAppConfig();
                var togglesConfig = sysConfig?.Toggles;

                if (togglesConfig != null && togglesConfig.SecurityValidation)
                {
                    var path = context.Request.Path;

                    var lowerPath = path.ToString().ToLowerInvariant();

                    if (!string.IsNullOrWhiteSpace(lowerPath) && (
                           lowerPath == "/v1/workflow-definitions"
                        || lowerPath == "/api/index.html"
                        || lowerPath == "/swagger/default/swagger.json"
                        || lowerPath == "/api/token/ssocallback"
                        || lowerPath == "/.well-known/appspecific/com.chrome.devtools.json"
                        ))
                    {
                        await _next(context);
                        return;
                    }

                    // 检查当前endpoint是否使用ApiKeyPolicy
                    var endpoint = context.GetEndpoint();
                    var authorizeData = endpoint?.Metadata?.GetOrderedMetadata<IAuthorizeData>();
                    var hasApiKeyPolicy = authorizeData?.Any(a => a.Policy == "ApiKeyPolicy") == true;

                    if (hasApiKeyPolicy)
                    {
                        // 如果是API Key策略的接口，跳过Auth-Token验证
                        await _next(context);
                        return;
                    }

                    await ValidateRequestAsync(context);
                }

                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogToFile(LogLevel.Error, $"{ex.Message}", "Security.log");
                context.Response.StatusCode = 403;
                await context.Response.WriteAsync("Forbidden");
            }
        }

        private bool IsSwaggerRequest(PathString path)
        {
            string[] swaggerPaths = new[] { $"/{Consts.SwaggerRoutePrefix}", $"/{Consts.SwaggerRoutePrefix}/index.html" };
            return swaggerPaths.Any(p => path.Value?.Contains(p, StringComparison.OrdinalIgnoreCase) == true);
        }

        private async Task ValidateRequestAsync(HttpContext context)
        {
            if (!context.Request.Headers.TryGetValue("Auth-Token", out var authTokenHeader))
            {
                throw new UnauthorizedAccessException("Invalid request.");
            }

            string decryptedData = DecryptData(authTokenHeader.ToString());
            var parts = decryptedData.Split(':');

            if (parts.Length != 2)
            {
                throw new UnauthorizedAccessException("Invalid request.");
            }

            string nonce = parts[0];
            if (!long.TryParse(parts[1], out long timestamp))
            {
                throw new UnauthorizedAccessException("Invalid request.");
            }

            long currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            const int TimeWindowInSeconds = 120;

            if (Math.Abs(currentTime - timestamp) > TimeWindowInSeconds)
            {
                throw new UnauthorizedAccessException("Invalid request.");
            }

            string redisKey = $"Nonce:{nonce}";

            if (!string.IsNullOrEmpty(await _redisRepository.GetValue(redisKey)))
            {
                throw new UnauthorizedAccessException("Invalid request.");
            }

            await _redisRepository.Set(redisKey, "used", TimeSpan.FromMinutes(5));
        }

        private string DecryptData(string authToken)
        {
            try
            {
                var privateKeyPem = GetPrivateKey();
                using var rsa = RSA.Create();
                rsa.ImportFromPem(privateKeyPem);
                var decryptedBytes = rsa.Decrypt(Convert.FromBase64String(authToken), RSAEncryptionPadding.OaepSHA256);
                return Encoding.UTF8.GetString(decryptedBytes);
            }
            catch (Exception ex)
            {
                throw new UnauthorizedAccessException("Decryption failed.", ex);
            }
        }

        private string GetPrivateKey()
        {
            var assembly = Assembly.GetExecutingAssembly();
            var resourceName = "YunLanCrm.Extensions.Properties.crm_private_key.pem";

            using var stream = assembly.GetManifestResourceStream(resourceName);
            if (stream == null)
            {
                throw new InvalidOperationException("Private key resource not found.");
            }

            using var reader = new StreamReader(stream);
            return reader.ReadToEnd();
        }
    }
}