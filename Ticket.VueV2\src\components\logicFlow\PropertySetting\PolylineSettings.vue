<template>
	<div>
		<el-form label-position="top" :model="formData">
			<el-form-item label="类型">{{ type }}</el-form-item>
			<el-form-item label="名称">
				<el-input v-model="formData.name"></el-input>
			</el-form-item>
			<el-form-item label="显示名称">
				<el-input v-model="text"></el-input>
			</el-form-item>
			<el-form-item label="触发方式" class="form-lable">
				<el-select v-model="formData.triggerMode" class="form-select" placeholder="Select" size="large" clearable>
					<el-option label="SignalReceived" value="SignalReceived" />
				</el-select>
			</el-form-item>
			<el-form-item label="触发器名称" class="form-lable">
				<el-input v-model="formData.triggerName" placeholder="Please input node name"></el-input>
			</el-form-item>
			<el-form-item>
				<el-button size="small" @click="onClosed">Cancel</el-button>
				<el-button type="primary" @click="onSubmit">Save</el-button>
			</el-form-item>
		</el-form>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

export default defineComponent({
	name: 'approvalSetting',
	props: {
		nodeData: Object,
		lf: Object || String,
	},
	components: {},
	setup(props, context) {
		const state = reactive({
			type: '',
			text: '',
			formData: {
				triggerMode: '',
				triggerName: '',
				name: '',
				displayName: '',
			},
		});

		const onSubmit = () => {
			const properties = state.formData;
			properties.displayName = state.text;
			if (properties.triggerMode && !properties.triggerName) {
			}

			const { id } = props.nodeData;
			props.lf.updateText(id, state.formData.displayName);
			props.lf.setProperties(id, {
				...state.formData,
			});
			context.emit('onClose');
		};

		const onClosed = () => {
			context.emit('onClose');
		};

		onMounted(() => {
			var graphData = props.lf.getGraphData();
			const { properties, text, type } = props.nodeData;
			console.log('props.nodeData', props.nodeData);
			if (properties) {
				state.formData = Object.assign({}, state.formData, properties);
			}
			if (text && text.value) {
				state.text = text.value;
			}
			state.type = type;
		});

		return {
			onSubmit,
			onClosed,
			...toRefs(state),
		};
	},
});
</script>
<style scoped></style>
