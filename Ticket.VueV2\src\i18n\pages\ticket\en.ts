export default {
	ticketSearch: {
		//查询区域
		searchKeyPlaceholder: 'Please input',
		searchDataUnvalid: 'End date cannot be earlier than start date',
	},
	ticketButtons: {
		//非按钮
		submitTicketSuccess: 'Submit Ticket Succeed!',
		submitTicket: 'Submit Ticket',
		more: 'More',
		workflowRecords: 'Workflow Records',
		attachments: 'Attachments',
		clickToUpload: 'Click to upload',
		detail: 'detail',
		viewComments: 'View Comments',

		//按钮
		newTicket: 'New Ticket',
		newScheduleTicket: 'New Schedule Ticket',

		uploadFilesLessThan: 'upload files with a size less than 20MB.',
		dragUploadFilesLessThan: 'drag or click upload files with a size less than 20MB.',
		dropFilesHere: 'Drag and drop files here',
		addComment: 'Add Comment',
	},
	ticketLabels: {
		status: 'Status',
		scheduledDate: 'Scheduled Date',
		priority: 'Priority',
		category: 'Category',
		customers: 'Customer',
		projects: 'Projects',
		caseNumber: 'Ticket Number',
		ticketSubject: 'Ticket Subject',
		createDate: 'Create Date',
		modifiedAt: 'Modified Date',
		to: 'To',
		createSubTicket: 'Create Sub Ticket',
		assignedtoMe: 'Assigned to Me',
		contentPlaceholder:
			'Please enter any additional information, relevant details or updates, or share your thoughts and feedback about the ticket in the note field.',
		uploadTips: 'upload files with a size less than 20MB.',
		preview: 'Preview',
		ticketContentType: 'Ticket Type',
	},
	ticketFields: {
		//table 列名
		status: 'Status',
		caseNumber: 'Ticket Number',
		assignTo: 'Assigned To',
		delegateTo: 'Delegate To',
		priority: 'Priority',
		category: 'Category',
		company: 'Company',
		project: 'Project',
		attachments: 'Attachments',
		ticketType: 'Ticket Type',
		openedBy: 'Opened By',
		solvedBy: 'Solved By',
		createdDate: 'Created Date',
		createdBy: 'Created By',
		modifiedDate: 'Modified Date',
		modifiedBy: 'Modified By',
		ticketFrom: 'Ticket From',
		submitTicketError: "The category you selected hasn't have an Assigned to,  please contact the system administrator for any questions",
		submitTicketOtherError: 'There is a problem submitting ticket, please contact the administrator',
		commentSavedSuccessfully: 'Comment saved successfully',
		openDate: 'Open Date',
		closeDate: 'Close Date',
		information: 'TICKET INFORMATION',
		internal: 'Internal',
		assignee: 'Assigned To',
		telephone: 'Phone',
		customerEmail: 'Customer Email',
		customer: 'Customer',
		lastName: 'Last Name',
		firstName: 'First Name',
		ticketSubject: 'Ticket Subject',
		subject: 'Subject',
		attachment: 'Attachment',
		content: 'Notes',
		subjectType: 'Subject Type',
		comment: 'Comment',
		public: 'Public',
		scheduledDate: 'Scheduled Date',
		operatorName: 'Operator',
		parentTicket: 'Linked Ticket',
		grantAccess: 'Grant Access to',
		privateTicket: 'Private Ticket',
		Emails: 'Emailed',
		BusinessUnit: 'Business Unit',
	},
	ticketValidates: {
		required: 'Required',
	},
};
