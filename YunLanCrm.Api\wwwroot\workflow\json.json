{"$id": "1", "activities": [{"$id": "2", "activityId": "ee442a35-6872-415f-9278-255f93243e80", "type": "StartActivity", "name": "node1", "displayName": "", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "3", "name": "Config", "expressions": {"$id": "4", "literal": "{\"name\":\"node1\",\"displayName\":\"\",\"activityType\":\"\",\"approvalType\":1,\"approvalData\":[],\"approvalMethod\":\"1\",\"returnType\":\"1\",\"approvalAction\":false,\"returnAction\":false,\"assignToAction\":false,\"rejectAction\":false,\"approveNotice\":\"\",\"returnNotice\":\"\",\"assignToNotice\":\"\",\"rejectNotice\":\"\",\"timedoutNotice\":\"\",\"Execute\":{},\"CC\":{\"Users\":[],\"Roles\":[],\"Departments\":[],\"Organizations\":[]},\"permissions\":[{\"PermissionType\":\"Approved\",\"Enable\":true,\"PermissionText\":\"同意\"},{\"PermissionType\":\"AssignTo\",\"Enable\":true,\"PermissionText\":\"转办\"},{\"PermissionType\":\"Revoked\",\"Enable\":true,\"PermissionText\":\"撤回\"},{\"PermissionType\":\"Returned\",\"Enable\":true,\"PermissionText\":\"退回\"},{\"PermissionType\":\"Resubmit\",\"Enable\":true,\"PermissionText\":\"提交\"},{\"PermissionType\":\"Escalate\",\"Enable\":true,\"PermissionText\":\"催办\"},{\"PermissionType\":\"Cancelled\",\"Enable\":true,\"PermissionText\":\"取消\"}]}"}}], "propertyStorageProviders": {}}, {"$id": "5", "activityId": "2a209726-4e92-48ed-82c8-662d78454603", "type": "If", "name": "Start_Resubmit_IFElse", "displayName": "重新提交？", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "6", "name": "Condition", "syntax": "JavaScript", "expressions": {"$id": "7", "javaScript": "getVariable('IsResubmit')===true"}}], "propertyStorageProviders": {}}, {"$id": "8", "activityId": "bb94c2bf-9807-4e15-b9ad-00a5c198a128", "type": "SignalReceived", "name": "Start_Resubmit_SignalReceived", "displayName": "等待提条", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "9", "name": "Signal", "expressions": {"$id": "10", "literal": "Start_Resubmit_SignalReceived"}}], "propertyStorageProviders": {}}, {"$id": "11", "activityId": "2355fc9b-30db-4508-bf16-0ed985e5090b", "type": "Fork", "name": "node1_Fork", "displayName": "Fork", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "12", "name": "Branches", "expressions": {"$id": "13", "json": "[\"node1_Fork_Approved\",\"node1_Fork_Revoked\",\"node1_Fork_Returned\"]"}}], "propertyStorageProviders": {}}, {"$id": "14", "activityId": "c76c1b01-d2b3-4ac0-8acf-c8d1e89fa3a2", "type": "SignalReceived", "name": "node1_Fork_Approved", "displayName": "node1_Fork_Approved", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "15", "name": "Signal", "expressions": {"$id": "16", "literal": "node1_Fork_Approved"}}], "propertyStorageProviders": {}}, {"$id": "17", "activityId": "44abe461-c924-46c4-9440-ccc653e8b773", "type": "SignalReceived", "name": "node1_Fork_Revoked", "displayName": "node1_Fork_Revoked", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "18", "name": "Signal", "expressions": {"$id": "19", "literal": "node1_Fork_Revoked"}}], "propertyStorageProviders": {}}, {"$id": "20", "activityId": "38b176d6-6d83-41da-a0f0-29be0821241e", "type": "SignalReceived", "name": "node1_Fork_Returned", "displayName": "node1_Fork_Returned", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "21", "name": "Signal", "expressions": {"$id": "22", "literal": "node1_Fork_Returned"}}], "propertyStorageProviders": {}}, {"$id": "23", "activityId": "855ead3c-1c4e-44f7-b405-1daca0a42a59", "type": "Approved", "name": "node2", "displayName": "主管审批", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "24", "name": "Config", "expressions": {"$id": "25", "literal": "{\"name\":\"node2\",\"displayName\":\"主管审批\",\"activityType\":\"\",\"approvalType\":1,\"approvalData\":[{\"ItemId\":\"1\",\"ItemName\":\"admin\"}],\"approvalMethod\":\"1\",\"returnType\":\"1\",\"approvalAction\":false,\"returnAction\":false,\"assignToAction\":false,\"rejectAction\":false,\"approveNotice\":\"\",\"returnNotice\":\"\",\"assignToNotice\":\"\",\"rejectNotice\":\"\",\"timedoutNotice\":\"\",\"Execute\":{},\"CC\":{\"Users\":[],\"Roles\":[],\"Departments\":[],\"Organizations\":[]},\"permissions\":[{\"PermissionType\":\"Approved\",\"Enable\":true,\"PermissionText\":\"同意\"},{\"PermissionType\":\"AssignTo\",\"Enable\":true,\"PermissionText\":\"转办\"},{\"PermissionType\":\"Revoked\",\"Enable\":true,\"PermissionText\":\"撤回\"},{\"PermissionType\":\"Returned\",\"Enable\":true,\"PermissionText\":\"退回\"},{\"PermissionType\":\"Resubmit\",\"Enable\":true,\"PermissionText\":\"提交\"},{\"PermissionType\":\"Escalate\",\"Enable\":true,\"PermissionText\":\"催办\"},{\"PermissionType\":\"Cancelled\",\"Enable\":true,\"PermissionText\":\"取消\"}]}"}}], "propertyStorageProviders": {}}, {"$id": "26", "activityId": "855ead3c-1c4e-44f7-b405-1daca0a42a59", "type": "Approved", "name": "node2", "displayName": "主管审批", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "27", "name": "Config", "expressions": {"$id": "28", "literal": "{\"name\":\"node2\",\"displayName\":\"主管审批\",\"activityType\":\"Approved\",\"approvalType\":1,\"approvalData\":[{\"ItemId\":\"1\",\"ItemName\":\"admin\"}],\"approvalMethod\":\"1\",\"returnType\":\"1\",\"approvalAction\":false,\"returnAction\":false,\"assignToAction\":false,\"rejectAction\":false,\"approveNotice\":\"\",\"returnNotice\":\"\",\"assignToNotice\":\"\",\"rejectNotice\":\"\",\"timedoutNotice\":\"\",\"Execute\":{},\"CC\":{\"Users\":[],\"Roles\":[],\"Departments\":[],\"Organizations\":[]},\"permissions\":[{\"PermissionType\":\"Approved\",\"Enable\":true,\"PermissionText\":\"同意\"},{\"PermissionType\":\"AssignTo\",\"Enable\":true,\"PermissionText\":\"转办\"},{\"PermissionType\":\"Revoked\",\"Enable\":true,\"PermissionText\":\"撤回\"},{\"PermissionType\":\"Returned\",\"Enable\":true,\"PermissionText\":\"退回\"},{\"PermissionType\":\"Resubmit\",\"Enable\":true,\"PermissionText\":\"提交\"},{\"PermissionType\":\"Escalate\",\"Enable\":true,\"PermissionText\":\"催办\"},{\"PermissionType\":\"Cancelled\",\"Enable\":true,\"PermissionText\":\"取消\"}]}"}}], "propertyStorageProviders": {}}, {"$id": "29", "activityId": "1f388f5e-dd96-4681-bbb3-d0b23c7bffe2", "type": "Finish", "name": "Finish", "displayName": "结束", "persistWorkflow": false, "loadWorkflowContext": false, "saveWorkflowContext": false, "properties": [{"$id": "30", "name": "Content", "expressions": {"$id": "31", "literal": "Finish"}}], "propertyStorageProviders": {}}], "connections": [{"$id": "32", "sourceActivityId": "ee442a35-6872-415f-9278-255f93243e80", "targetActivityId": "2a209726-4e92-48ed-82c8-662d78454603", "outcome": "Done"}, {"$id": "33", "sourceActivityId": "2a209726-4e92-48ed-82c8-662d78454603", "targetActivityId": "bb94c2bf-9807-4e15-b9ad-00a5c198a128", "outcome": "True"}, {"$id": "34", "sourceActivityId": "bb94c2bf-9807-4e15-b9ad-00a5c198a128", "targetActivityId": "ee442a35-6872-415f-9278-255f93243e80", "outcome": "Done"}, {"$id": "35", "sourceActivityId": "2a209726-4e92-48ed-82c8-662d78454603", "targetActivityId": "2355fc9b-30db-4508-bf16-0ed985e5090b", "outcome": "False"}, {"$id": "36", "sourceActivityId": "2355fc9b-30db-4508-bf16-0ed985e5090b", "targetActivityId": "c76c1b01-d2b3-4ac0-8acf-c8d1e89fa3a2", "outcome": "node1_Fork_Approved"}, {"$id": "37", "sourceActivityId": "2355fc9b-30db-4508-bf16-0ed985e5090b", "targetActivityId": "44abe461-c924-46c4-9440-ccc653e8b773", "outcome": "node1_Fork_Revoked"}, {"$id": "38", "sourceActivityId": "44abe461-c924-46c4-9440-ccc653e8b773", "targetActivityId": "ee442a35-6872-415f-9278-255f93243e80", "outcome": "Done"}, {"$id": "39", "sourceActivityId": "2355fc9b-30db-4508-bf16-0ed985e5090b", "targetActivityId": "38b176d6-6d83-41da-a0f0-29be0821241e", "outcome": "node1_Fork_Returned"}, {"$id": "40", "sourceActivityId": "38b176d6-6d83-41da-a0f0-29be0821241e", "targetActivityId": "ee442a35-6872-415f-9278-255f93243e80", "outcome": "Done"}, {"$id": "41", "sourceActivityId": "c76c1b01-d2b3-4ac0-8acf-c8d1e89fa3a2", "targetActivityId": "855ead3c-1c4e-44f7-b405-1daca0a42a59", "outcome": "Done"}, {"$id": "42", "sourceActivityId": "855ead3c-1c4e-44f7-b405-1daca0a42a59", "targetActivityId": "1f388f5e-dd96-4681-bbb3-d0b23c7bffe2", "outcome": "Done"}], "variables": {"$id": "43", "data": {}}, "contextOptions": {"$id": "44", "contextType": "System.Object, mscorlib", "contextFidelity": "<PERSON><PERSON><PERSON>"}, "customAttributes": {"$id": "45", "data": {}}, "channel": "Channel42af812b-bd63-4ece-ae95-f2f6f58caaa2"}