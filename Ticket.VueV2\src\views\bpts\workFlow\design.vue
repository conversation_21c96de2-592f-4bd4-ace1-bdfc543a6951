<template>
	<el-container class="workflowApproval-index-container tablelist" style="height: 100%">
		<el-header>
			<div class="do">
				<Control class="demo-control" v-if="lf" :lf="lf" @catData="catData" @saveTemplate="saveTemplate"></Control>
			</div>
		</el-header>
		<el-main class="nopadding">
			<NodePanel v-if="lf" :lf="lf" :nodeList="nodeList"></NodePanel>
			<div id="LF-view" ref="container" class="container"></div>
			<!-- 属性面板 -->
			<el-drawer title="设置节点属性" v-model="dialogVisible" direction="rtl" size="600px" :before-close="closeDialog">
				<PropertyDialog v-if="dialogVisible" :nodeData="clickNode" :lf="lf" @setPropertiesFinish="closeDialog"></PropertyDialog>
			</el-drawer>
		</el-main>
	</el-container>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, defineComponent, ref } from 'vue';
import LogicFlow from '@logicflow/core';
import '@logicflow/core/dist/style/index.css';
import '@logicflow/extension/lib/style/index.css';
import { Menu, Snapshot, MiniMap } from '@logicflow/extension';
import Control from '/@/components/logicFlow/LFComponents/Control.vue';
import NodePanel from '/@/components/logicFlow/LFComponents/NodePanel.vue';
import PropertyDialog from '/@/components/logicFlow/PropertySetting/PropertyDialog.vue';
import { nodeList } from '/@/components/logicFlow/config.js';
import StartNode from '/@/components/logicFlow/CustomNode/StartNode';
import JugementNode from '/@/components/logicFlow/CustomNode/JugementNode';
import EndNode from '/@/components/logicFlow/CustomNode/EndNode';
import ApprovalNode from '/@/components/logicFlow/CustomNode/ApprovalNode';
import ForkNode from '/@/components/logicFlow/CustomNode/ForkNode';
import JoinNode from '/@/components/logicFlow/CustomNode/JoinNode';
import UserTask from '/@/components/logicFlow/CustomNode/UserTask';

import workflowCrmApi from '/@/api/workflowCrm/index';
import { ElMessage } from 'element-plus';

export default defineComponent({
	name: 'workflowApprovalIndex',
	components: { Control, NodePanel, PropertyDialog },
	setup() {
		const state = reactive({
			showAddPanel: false,
			addPanelStyle: {
				top: 0,
				left: 0,
			},
			nodeData: null,
			addClickNode: null,
			clickNode: null,
			dialogVisible: false,
			graphData: null,
			dataVisible: false,
			config: {
				background: {
					backgroundColor: '#f7f9ff',
				},
				grid: {
					size: 10,
					visible: false,
				},
				keyboard: {
					enabled: true,
				},
				edgeTextDraggable: true,
				hoverOutline: false,
			},
			moveData: {},
			nodeList,
		});

		// 声明容器的对应ref对象和LF对象
		const container = ref();

		const lf = ref<LogicFlow>();

		const exportJson = () => {};

		const catData = () => {
			var graphData = (state.graphData = lf.value.getGraphData());
			console.log('sgraphData', graphData);
		};

		const saveTemplate = () => {
			var req = {};
			var obj = lf.value.getGraphData();
			var firstNode = obj.nodes.find((a) => a.type === 'StartNode');
			if (firstNode && firstNode.properties) {
				//req.DesignId=firstNode.id;
				req.WorkflowName = firstNode.properties.workflowName;
				req.WorkflowCode = firstNode.properties.workflowCode;
				req.Sorting = firstNode.properties.sorting;
				req.Description = firstNode.properties.description;
				req.WorkflowDefinitionId=firstNode.properties.workflowDefinitionId;
			}
			req.Data = JSON.stringify(obj);
			console.log('obj', obj);
			return;
			workflowCrmApi.SaveWorkflowTemplate(req).then((rs) => {
				ElMessage.success('Succeed');
			});
		};

		const addEvent = () => {
			lf.value.on('node:dnd-add', ({ data }) => {
				console.log('node:dnd-add', data);
			});

			lf.value.on('edge:add', ({ data }) => {
				data.properties.triggerMode = 'SignalReceived';
				//data.properties.triggerName = getNextEdgeName();
				data.properties.name = getNextNodeName();
				lf.value.setProperties(data.id, {
					...data.properties,
				});
				console.log('edge:dnd-add', data);
			});

			lf.value.on('node:click', ({ data }) => {
				console.log('node:click', data);
				if (data.text && data.text.value && !data.properties.displayName) {
					data.properties.displayName = data.text.value;
				}
				state.clickNode = data;
				state.dialogVisible = true;
			});

			lf.value.on('edge:click', ({ data }) => {
				console.log('edge:click', data);
				if (data.text && data.text.value && !data.properties.displayName) {
					data.properties.displayName = data.text.value;
				}
				state.clickNode = data;
				state.dialogVisible = true;
			});
		};

		const getNextNodeName = () => {
			const nodes = lf.value.getGraphData().nodes;
			const names = nodes.map((node) => node.properties.name).sort(); // 将所有name从小到大排序

			const lastNodeName = names[names.length - 1]; // 取出最后一个name
			console.log('lastNodeName', lastNodeName);
			const lastNodeNumber = lastNodeName ? parseInt(lastNodeName.replace(/^\D+/g, '')) : 0; // 提取数字部分并转换为数字
			console.log('lastNodeNumber', lastNodeNumber);
			const nextNodeNumber = lastNodeNumber + 1; // 计算下一个数字
			const nextNodeName = `node${nextNodeNumber}`; // 拼接下一个name
			return nextNodeName;
		};

		const getNextEdgeName = () => {
			const nodes = lf.value.getGraphData().edges;
			const names = nodes.map((node) => node.properties.triggerName).sort(); // 将所有name从小到大排序
			const lastNodeName = names.pop(); // 取出最后一个name
			const lastNodeNumber = lastNodeName ? parseInt(lastNodeName.replace(/^\D+/g, '')) : 0; // 提取数字部分并转换为数字
			const nextNodeNumber = lastNodeNumber + 1; // 计算下一个数字
			const nextNodeName = `SignalReceived${nextNodeNumber}`; // 拼接下一个name
			return nextNodeName;
		};

		const closeDialog = () => {
			state.dialogVisible = false;
		};

		// 页面加载时
		onMounted(() => {
			lf.value = new LogicFlow({
				// 通过选项指定了渲染的容器和需要显示网格
				container: container.value,
				grid: true,
				plugins: [Menu, MiniMap, Snapshot],
			});
			// 设置主题
			lf.value.setTheme({
				circle: {
					stroke: '#000000',
					strokeWidth: 1,
					outlineColor: '#88f',
				},
				rect: {
					outlineColor: '#88f',
					strokeWidth: 1,
				},
				polygon: {
					strokeWidth: 1,
				},
				polyline: {
					stroke: '#000000',
					hoverStroke: '#000000',
					selectedStroke: '#000000',
					outlineColor: '#88f',
					strokeWidth: 1,
				},
				nodeText: {
					color: '#000000',
				},
				edgeText: {
					color: '#000000',
					background: {
						fill: '#f7f9ff',
					},
				},
			});
			// 在执行render前进行注册
			lf.value.register(StartNode);
			lf.value.register(JugementNode);
			lf.value.register(EndNode);
			lf.value.register(ApprovalNode);
			lf.value.register(ForkNode);
			lf.value.register(JoinNode);
			lf.value.register(UserTask);
			lf.value.render();

			addEvent();
		});
		return {
			exportJson,
			saveTemplate,
			container,
			lf,
			closeDialog,
			catData,
			...toRefs(state),
		};
	},
});
</script>

<style scoped>
.el-main {
	--el-main-padding: 20px;
	display: block;
	flex: 1;
	flex-basis: auto;
	overflow: auto;
	box-sizing: border-box;
	padding: var(--el-main-padding);
}
.container {
	width: 100%;
	height: 100%;
}
</style>

<style>
.logic-flow-view {
	height: 100vh;
	position: relative;
}
.demo-title {
	text-align: center;
	margin: 20px;
}
.demo-control {
	position: absolute;
	top: 30px;
	left: 50px;
	z-index: 2;
}
#LF-view {
	width: calc(100% - 100px);
	height: 80%;
	outline: none;
	margin-left: 50px;
}
.time-plus {
	cursor: pointer;
}
.add-panel {
	position: absolute;
	z-index: 11;
	background-color: white;
	padding: 10px 5px;
}
.el-drawer__body {
	height: 80%;
	overflow: auto;
	margin-top: -30px;
	z-index: 3;
}

.node-panel {
	width: 90px;
	margin-top: 40px;
}

@keyframes lf_animate_dash {
	to {
		stroke-dashoffset: 0;
	}
}
</style>