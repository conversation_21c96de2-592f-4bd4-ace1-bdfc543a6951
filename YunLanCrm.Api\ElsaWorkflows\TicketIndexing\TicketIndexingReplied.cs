﻿using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Persistence;
using Elsa.Serialization;
using Elsa.Services.Models;
using Mapster;
using YunLanCrm.Api.ElsaWorkflows.Activities;
using YunLanCrm.Common.HttpContextUser;
using YunLanCrm.Dto.CmTickets;
using YunLanCrm.IRepositories;
using YunLanCrm.IServices;
using YunLanCrm.Model.Views;
using YunLanCrm.Services;

namespace YunLanCrm.Api.ElsaWorkflows.TicketIndexing
{
    [Action(Category = "工单提交", DisplayName = "Replied", Description = "Replied", Outcomes = new[] { OutcomeNames.Done })]
    public class TicketIndexingReplied : Approved
    {
        private readonly ICmTicketsService _cmTicketsService;
        private readonly IUser CurrentUser;
        private readonly ILogger<TicketIndexingReplied> _logger;

        public TicketIndexingReplied(IContentSerializer serializer, IUser user, IEmailsService emailsService,
            IWorkflowCrmService workflowCrmService, IWorkflowCrmRepository workflowCrmRepository, IWorkflowDefinitionStore workflowDefinitionStore, ICmTicketsService cmTicketsService, IUser currentUser, ILogger<TicketIndexingReplied> logger) : base(serializer, user, emailsService, workflowCrmService, workflowCrmRepository, workflowDefinitionStore)
        {
            _cmTicketsService = cmTicketsService;
            CurrentUser = currentUser;
            _logger = logger;
        }


        //这个事件 一般是第一次执行的时候 触发，比如 处理添加审批节点的 待办人、更新当前节点
        // 这个事件不需要的话 可以不用重写
        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            var obj = context.GetVariable("FormData");
            CmTicketsAddOrUpdateDto cmTicketsAddOrUpdateDto;
            CmTicketsView oldTicketInfo = null;

            try
            {
                if (obj != null)
                {
                    cmTicketsAddOrUpdateDto = obj.Adapt<CmTicketsAddOrUpdateDto>();

                    oldTicketInfo = await _cmTicketsService.GetTicketInfo(cmTicketsAddOrUpdateDto.Id.ObjToLong());
                    cmTicketsAddOrUpdateDto.FromStatus = oldTicketInfo.Ticket_Status;
                    var assignToNode = cmTicketsAddOrUpdateDto.assigToId;

                    if (assignToNode.Count > 0 && assignToNode[0].NodeName != null)
                    {
                        var obj1 = _cmTicketsService.UpdateTicket(cmTicketsAddOrUpdateDto).Result;
                    }
                    else
                    {
                        CurrentUser.UserId = assignToNode.Count > 0 ? Convert.ToInt32(assignToNode[0].NodeId) : 0;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogToFile(LogLevel.Critical, ex.Message, nameof(TicketIndexingReplied) + "/SubmitTicket/Fatal.log");
            }

            //先执行基类 Approved OnExecuteAsync 的逻辑
            var result = await base.OnExecuteAsync(context);

            try
            {
                if (obj != null)
                {
                    cmTicketsAddOrUpdateDto = obj.Adapt<CmTicketsAddOrUpdateDto>();

                    if (cmTicketsAddOrUpdateDto.Id > 0)
                    {
                        await _cmTicketsService.RecordTicketUpdateLog(cmTicketsAddOrUpdateDto, oldTicketInfo);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogToFile(LogLevel.Critical, ex.Message, nameof(TicketIndexingReplied) + "/SubmitTicket/Fatal.log");
            }

            return result;
        }
    }
}