<template>
	<div>
		<h4>{{ nodeName }}</h4>
		<p></p>
		<el-table :data="tableData.data" v-loading="tableData.loading" class="approved-table">
			<el-table-column label="Reviewer" show-overflow-tooltip>
				<template #default="{ row }">
					<span>{{ row.operatorName }}</span>
				</template>
			</el-table-column>

			<el-table-column label="Review Status" width="150" show-overflow-tooltip>
				<template #default="{ row }">
					<el-tag v-if="row.status == 'Pending'">Pending Review</el-tag>
					<el-tag type="success" v-if="row.status === 'Completed'">Reviewed</el-tag>
					<el-tag type="danger" v-if="row.status === 'Cancelled'">Cancelled</el-tag>
				</template>
			</el-table-column>

			<template v-if="showOperationTypeColumn">
				<el-table-column label="Review Result">
					<template #default="{ row }">
						<el-tag v-if="row.operationResult === WorkflowActions.APPROVE" type="success">Approved</el-tag>
						<el-tag v-if="row.operationResult === WorkflowActions.RETURN" type="danger">退回</el-tag>
						<el-tag v-if="row.operationResult === WorkflowActions.ASSIGNTO" type="info">转办</el-tag>
					</template>
				</el-table-column>
			</template>
			<el-table-column label="Review Date" show-overflow-tooltip>
				<template #default="{ row }">
					<span>{{ formatStrDate(row.operationDate) }}</span>
				</template>
			</el-table-column>
			<el-table-column label="审批备注" show-overflow-tooltip v-if="false">
				<template #default="{ row }">
					<span>{{ row.operationMsg }}</span>
				</template>
			</el-table-column>

			<template v-slot:append>
				<div v-if="nodeStatus === 'Pending' && taskMode === 'WaitAll'">
					<!-- <el-alert title="当前审批流程正在等待其他审批人的审批结果" type="info" :closable="false" show-icon /> -->
					<el-alert
						title="The current approval process is waiting for the review results from other approvers."
						type="info" :closable="false" show-icon />
				</div>
				<div v-if="taskMode === 'WaitAny'">
					<!-- <el-alert title="当前审批设置为【或签】，其中一个审批人通过即可。" type="info" :closable="false" show-icon /> -->
					<el-alert
						title="The current approval setting is set to [jointly signed], where the approval of any one approver is sufficient."
						type="info" :closable="false" show-icon />
				</div>
			</template>
		</el-table>

		<br />
		<p v-if="item.transferMsg">Remark：</p>
		<span v-html="item.transferMsg"></span>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, onMounted } from 'vue';
import { formatStrDate } from '/@/utils/formatTime';
import { WorkflowActions } from '/@/types/workflow';
export default defineComponent({
	name: 'workflowOperationsApproved',
	props: {
		nodeData: Object,
	},
	setup(props) {
		const state = reactive({
			nodeStatus: '',
			nodeName: '',
			taskMode: '',
			item: {},
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
			},
			showStatusColumn: false,
			showOperationTypeColumn: false,
		});

		onMounted(() => {
			const { nodeName, operationData, taskMode, nodeStatus } = props.nodeData;
			state.nodeName = nodeName;
			state.nodeStatus = nodeStatus;
			state.taskMode = taskMode;
			state.tableData.data = operationData;

			state.item = operationData[0];

			state.showStatusColumn = state.tableData.data.some((row) => row.status === 'Pending');
			state.showOperationTypeColumn = state.tableData.data.some((row) => row.status === 'Completed' || row.status === 'Canceled');
		});

		return {
			formatStrDate,
			WorkflowActions,
			...toRefs(state),
		};
	},
});
</script>
