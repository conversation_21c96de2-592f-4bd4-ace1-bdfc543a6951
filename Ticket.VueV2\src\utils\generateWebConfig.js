// versionUpdatePlugin.js
const fs = require('fs');
const path = require('path');

const writeVersion = (versionFile, content) => {
	// 写入文件
	fs.writeFile(versionFile, content, (err) => {
		if (err) throw err;
	});
};

export default (options) => {
	let config;

	return {
		name: 'generate-webconfig',

		configResolved(resolvedConfig) {
			// 存储最终解析的配置
			config = resolvedConfig;
		},

		buildStart() {
			const file = config.publicDir + path.sep + 'web.config';
			const content = options.version;

			if (fs.existsSync(config.publicDir)) {
				writeVersion(file, content);
			} else {
				fs.mkdir(config.publicDir, (err) => {
					if (err) throw err;
					writeVersion(file, content);
				});
			}
		},
	};
};
