﻿<template>
	<div class="user-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="769px" :close-on-click-modal="false" @close="onCancel"
			draggable>
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="140px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.userFields.userName')" prop="userName">
							<el-input v-model="ruleForm.userName" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.userFields.realName')" prop="realName">
							<el-input v-model="ruleForm.realName" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.userFields.mobile')" prop="mobile">
							<el-input v-model="ruleForm.mobile" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.userFields.lastName')" prop="lastName">
							<el-input v-model="ruleForm.lastName" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.userFields.email')" prop="email">
							<el-input v-model="ruleForm.email" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.userFields.masterUser')"
							v-show="ruleForm.userType == 'End User' ? true : false">
							<el-tree-select ref="localMasterUserTypeTree" v-model="ruleForm.masterUserTypeArr"
								:data="masterUserTypeOrgData" :render-after-expand="false" node-key="orgId"
								show-checkbox :check-strictly="true" check-on-click-node style="width: 100%" multiple
								@check-change="checkMasterUserChange"
								:default-expanded-keys="ruleForm.masterUserTypeArr" :auto-expand-parent="true"
								collapse-tags :props="{ value: 'orgId', label: 'name' }" @node-expand="ClickExpandNode1"
								filterable />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.userFields.orgName')" prop="orgArr">
							<el-tree-select v-model="ruleForm.orgArr"
								:placeholder="$t('message.page.selectKeyPlaceholder')" :data="orgData" node-key="orgId"
								:default-expand-all="true" checkStrictly style="width: 100%" clearable
								:props="{ value: 'orgId', label: 'name' }" filterable @clear="clearCompany">
								<template #default="{ data }">
									<SvgIcon name="fa fa-sitemap ti" :size="12"></SvgIcon>
									<span>{{ data.name }}</span>
								</template>
							</el-tree-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6" v-if="isShowPrivateTicket">
						<el-form-item :label="$t('message.userFields.privateTicket')" prop="privateTicket">
							<el-switch v-model="ruleForm.privateTicket" class="ml-2" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.userFields.roleAssign')" prop="roleArr">
							<el-select v-model="ruleForm.roleArr" multiple
								:placeholder="$t('message.page.selectKeyPlaceholder')" style="width: 100%"
								:disabled="disableAssignRole" @change="ChangeRole">
								<el-option v-for="item in roleData" :key="item.id" :label="item.name"
									:value="item.id" />
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.userFields.customerAssign')" prop="customerArr">
							<el-tree-select ref="localTree" v-model="ruleForm.customerArr" :data="customerData"
								:render-after-expand="false" node-key="orgId" show-checkbox :check-strictly="false"
								check-on-click-node style="width: 85%" multiple @check-change="checkChange"
								:default-expanded-keys="ruleForm.customerArr_Init" :auto-expand-parent="true"
								:props="{ value: 'orgId', label: 'name' }" @node-expand="ClickExpandNode"
								:max-collapse-tags="5" collapse-tags collapse-tags-tooltip filterable
								@current-change="checkChangeCustomer" @check="checkChangeCustomer" />

							&nbsp;&nbsp;

							<el-checkbox v-model="checked1" class="check-all-button" @change="selectAll"
								:indeterminate="isIndeterminate">
								Check All
							</el-checkbox>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6" v-if="false">
						<el-form-item :label="$t('message.userFields.sex')" prop="sex">
							<el-radio-group v-model="ruleForm.sex" size="small">
								<el-radio-button :label="0">{{ $t('message.userFields.sexSecret') }}</el-radio-button>
								<el-radio-button :label="1">{{ $t('message.userFields.sexMale') }}</el-radio-button>
								<el-radio-button :label="2">{{ $t('message.userFields.sexWomen') }}</el-radio-button>
							</el-radio-group>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6" v-if="false">
						<el-form-item :label="$t('message.userFields.birth')" prop="birth">
							<MyDate v-model:input="ruleForm.birth"
								:placeholder="$t('message.page.selectKeyPlaceholder')" class="w100" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.userFields.status')" prop="status">
							<el-select v-model="ruleForm.status" :placeholder="$t('message.page.selectKeyPlaceholder')"
								clearable class="w100">
								<el-option :label="$t('message.userFields.Active')" :value="0"></el-option>
								<el-option :label="$t('message.userFields.Inactive')" :value="1"></el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.userFields.userType')" prop="userType">
							<el-select v-model="ruleForm.userType"
								:placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100"
								disabled="disabled">
								<el-option v-for="item in userTypeData" :label="item.itemName" :value="item.itemName"
									:key="item.itemId"></el-option></el-select>
						</el-form-item>
					</el-col>



					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.userFields.escalation')" prop="escalation">
							<el-select v-model="ruleForm.escalation" clearable filterable
								:placeholder="$t('message.page.selectKeyPlaceholder')" class="w100">
								<el-option v-for="item in userBptsData" :key="parseInt(item.nodeId)" :label="item.label"
									:value="parseInt(item.nodeId)" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.userFields.jobTitle')" prop="jobTitle">
							<el-input v-model="ruleForm.jobTitle" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.userFields.timeZone')" prop="timeZone">
							<el-select v-model="ruleForm.timeZone"
								:placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100">
								<el-option v-for="item in timeZoneData" :label="item.itemName" :value="item.itemName"
									:key="item.itemId"></el-option></el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.userFields.location')" prop="location">
							<el-input v-model="ruleForm.location" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6" v-if="false">
						<el-form-item :label="$t('message.userFields.address')" prop="address">
							<el-input v-model="ruleForm.address" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6" v-if="false">
						<el-form-item :label="$t('message.userFields.remark')" prop="remark">
							<el-input v-model="ruleForm.remark" />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>

			<el-dialog v-model="changePwdRef" :title="$t('message.page.changePwd')" :width="500"
				:close-on-click-modal="false" @close="handleClose(changePwdFormRef)">
				<el-form ref="changePwdFormRef" :rules="changeRules" :model="form" label-position="left"
					label-width="140px" style="height: 100px">
					<el-row>
						<el-col>
							<el-form-item :label="$t('message.page.resetPwd')" prop="pwd">
								<el-input v-model="form.pwd" :type="isShowPwd ? 'text' : 'password'"
									:placeholder="$t('message.page.searchKeyPlaceholder')" autocomplete="off"
									style="width: 300px">
									<template #suffix>
										<el-icon @click="isShowPwd = !isShowPwd">
											<template v-if="isShowPwd">
												<img src="/img/show.svg" />
											</template>
											<template v-else>
												<img src="/img/hide.svg" />
											</template>
										</el-icon>
									</template>
								</el-input>
							</el-form-item>
						</el-col>
						<el-col style="margin-top: 20px">
							<el-form-item :label="$t('message.page.confirmPwd')" prop="confirmPwd">
								<el-input v-model="form.confirmPwd" :type="isShowConfirmPwd ? 'text' : 'password'"
									:placeholder="$t('message.page.searchKeyPlaceholder')" style="width: 300px">
									<template #suffix>
										<el-icon @click="isShowConfirmPwd = !isShowConfirmPwd">
											<template v-if="isShowConfirmPwd">
												<img src="/img/show.svg" />
											</template>
											<template v-else>
												<img src="/img/hide.svg" />
											</template>
										</el-icon>
									</template>
								</el-input>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
				<div style="color: red; size: 12; margin-top: 20px; text-align: left;">
					<p>Must be a minimum of 8 characters in length and contain a combination of at least two of the
						following:
					</p>
					<p>1. A mix of uppercase and lowercase letters</p>
					<p>2. Special character (~ ! @ # $ % ^ & * ( ) _ +)</p>
					<p>3. Number (numeric value)</p>
				</div>
				<template #footer>
					<span class="dialog-footer">
						<el-button @click="handleClose(changePwdFormRef)">{{ $t('message.page.buttonCancel')
							}}</el-button>
						<el-button type="primary" @click="onSubmitChangePwd">
							{{ $t('message.page.changePwd') }}
						</el-button>
					</span>
				</template>
			</el-dialog>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">
						{{ $t('message.page.buttonClose') }}
					</el-button>
					<el-button v-if="dialogParams.action === 'Edit'" type="info" size="small" @click="onChangePwd">
						{{ $t('message.page.changePwd') }}
					</el-button>
					<el-button v-if="dialogParams.action === 'Create'" @click="onInitForm" size="small" type="danger">
						{{ $t('message.page.buttonReset') }}
					</el-button>
					<Auth :value="dialogParams.action === 'Edit' ? 'systemUser.Delete' : ''">
						<el-button v-if="dialogParams.action === 'Edit'" @click="onDelete" type="danger" size="small">
							{{ $t('message.page.buttonDelete') }}
						</el-button>
					</Auth>
					<Auth :value="dialogParams.action === 'Edit' ? 'systemUser.UnLock' : ''">
						<el-button v-if="dialogParams.action === 'Edit' && ruleForm.hasLock" @click="onUnLock"
							type="warning" size="small">
							{{ $t('message.limits.UnLock') }}
						</el-button>
					</Auth>
					<el-button :loading="saveLoading" type="primary" @click="onSubmit" size="small">{{
						$t('message.page.buttonSave') }}</el-button>
				</span>
			</template>
		</el-dialog>

		<SelectUser ref="selectUserRef" @fetchData="setSelectItem" />
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent, ref, watch, computed, nextTick } from 'vue';
import { getElcascaderMultiple } from '/@/utils';
import { ElMessageBox, ElMessage, FormInstance } from 'element-plus';

import userApi from '/@/api/user';
import organizationApi from '/@/api/organization';
import roleApi from '/@/api/role';
import SelectUser from '/@/views/bpts/user/components/SelectUser.vue';

import dictItemApi from '/@/api/dictItem/index';
import { checkEmail } from '/@/utils/toolsValidate';
import { useI18n } from 'vue-i18n';
import MyDate from '/@/components/ticket/ticketDate.vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import Auth from '/@/components/auth/auth.vue';
import userInfoApi from '/@/api/user/index';
interface DialogParams {
	action: string;
	id: number;
}

export default defineComponent({
	name: 'userCreateOrEdit',
	components: { SelectUser, MyDate, Auth },
	setup(props, context) {
		const { t } = useI18n();
		const { proxy } = getCurrentInstance() as any;
		const selectUserRef = ref();
		const userRef = ref();
		const localTree = ref();
		const localMasterUserTypeTree = ref();
		const changePwdRef = ref(false);

		const checked1 = ref(false)

		const state = reactive({
			title: 'New User',
			isShowPwd: false,
			isShowConfirmPwd: false,
			isShowDialog: false,
			isShowPrivateTicket: false,
			saveLoading: false,
			deleteLoading: false,
			orgData: [],
			masterUserTypeOrgData: [],
			positionData: [],
			roleData: [] as any,
			oldRoleData: [] as any,
			customerData: [],
			userDataSelect: [] as any,
			userData: [],
			userBptsData: [],
			userTypeData: [] as any,
			timeZoneData: [] as any,
			dialogParams: {
				action: '',
				id: -1,
			},
			ruleForm: {
				roleArr: [] as any, // 当前选中的值
				lastRoleArr: [] as any, // 上一次选中的值
				customerArr: [] as any,
				customerArr_Init: [],
				orgArr: [],
				masterUserTypeArr: [],
				id: 0,
				mobile: '',
				email: '',
				userName: '',
				realName: '',
				lastName: '',
				status: 0,
				userType: '',
				departmentId: 0,
				remark: '',
				errorCount: 0,
				name: '',
				sex: 0,
				age: 0,
				birth: '',
				address: '',
				positionId: 0,
				lastLoginIp: '',
				emailValidation: false,
				phoneValidation: false,
				avatar: '',
				namePinYin: '',
				master_User: false,
				location: '',
				timeZone: '',
				escalation:0,
				jobTitle: '',
				hasLock: false,
				privateTicket: false,
			} as any,
			rules: {
				userName: [{ required: true, message: 'Please input', trigger: 'blur' }],
				realName: [{ required: true, message: 'Please input', trigger: 'blur' }],
				lastName: [{ required: true, message: 'Please input', trigger: 'blur' }],
				mobile: [
					{ required: false, message: 'Please input', trigger: 'blur' },
					{
						validator: (rule, value, callback) => {
							if (/^(?:[0-9]\d*)$/.test(value) == false && isNotEmptyOrNull(value)) {
								callback(new Error('Phone must be number'));
							} else {
								callback();
							}
						},
						trigger: 'blur',
					},
				],
				email: [
					{ required: true, message: 'Please input', trigger: 'blur' },
					{
						validator: checkEmail,
						min: 9,
						max: 18,
						message: t('message.formats.email'),
						trigger: 'blur',
					},
				],
				orgArr: [{ required: true, message: 'Please input', trigger: 'change' }],
				roleArr: [{ required: true, message: 'Please input', trigger: 'change' }],
				customerArr: [{ required: true, message: 'Please input', trigger: 'change' }],
			},
		});

		const changeRules = {
			pwd: [
				{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' },
				{
					pattern:
						/^(?=.*[a-z])(?=.*[A-Z])(?=.*(\d|[~!@#$%^&*()_+,]))[A-Za-z\d~!@#$%^&*()_+,]{7,20}$|^(?=.*\d)(?=.*[~!@#$%^&*()_+,])[A-Za-z\d~!@#$%^&*()_+,]{7,20}$/,
					message: 'Password is Invalid.',
					trigger: ['blur'],
				},
			],
			confirmPwd: [
				{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' },
				{
					pattern:
						/^(?=.*[a-z])(?=.*[A-Z])(?=.*(\d|[~!@#$%^&*()_+,]))[A-Za-z\d~!@#$%^&*()_+,]{7,20}$|^(?=.*\d)(?=.*[~!@#$%^&*()_+,])[A-Za-z\d~!@#$%^&*()_+,]{7,20}$/,
					message: 'Password is Invalid.',
					trigger: ['blur'],
				},
			],
		};

		const form = reactive({
			userId: '' as any,
			pwd: '',
			confirmPwd: '',
		});

		const changePwdFormRef = ref<FormInstance>();

		const openDialog = (parmas: DialogParams) => {
			checked1.value = false
			state.dialogParams = parmas;

			onInitForm();

			userInfoApi.GetStaffMemberList().then((rs) => {
				state.userBptsData = rs.data;

			});

			if (parmas.action == 'Create') {
				state.title = 'New User';
				if (state.ruleForm.escalation==0)
				{	
					state.ruleForm.escalation="";
				}
				organizationApi.Tree().then((rs) => {
					state.orgData = rs.data;
				});

				roleApi.GetList().then((rs) => {
					state.roleData = rs.data;
					state.oldRoleData = [...rs.data];
				});

				var dictArr = ['UserType'];
				dictItemApi.Many(dictArr).then((rs: any) => {
					state.userTypeData = rs.data?.find((a: any) => {
						return a.dictValue === 'UserType';
					})?.items;
				});
				var dictArr = ['Time Zone'];
				dictItemApi.Many(dictArr).then((rs: any) => {
					state.timeZoneData = rs.data?.find((a: any) => {
						return a.dictValue === 'Time Zone';
					})?.items;
				});
			} else if (parmas.action == 'Edit') {
				state.title = 'Edit User';
				getData(parmas.id);
			} else {
				ElMessage.error('Parameter action cannot be empty.');
			}
		};

		const openSelectUser = () => {
			selectUserRef.value.openDialog();
		};

		const closeDialog = () => {
			localTree.value.setCheckedKeys([]);
			localMasterUserTypeTree.value.setCheckedKeys([]);
			onInitForm();
			state.isShowDialog = false;
		};

		const onCancel = () => {
			closeDialog();
		};

		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}

				var obj = state.ruleForm;

				if (obj.birth == '') {
					delete obj.birth;
				}

				obj.roles = state.ruleForm.roleArr;

				obj.customers = state.ruleForm.customerArr;

				obj.masterUserTypes = state.ruleForm.masterUserTypeArr;

				obj.orgs = [state.ruleForm.orgArr];

				obj.Check_All_Projects = checked1.value ? true : false
				if (state.ruleForm.escalation=="")
				{	
					state.ruleForm.escalation=0;
				}
				state.saveLoading = true;

				userApi
					.Save(obj)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						proxy.$message(rs.resultMsg || rs.toString(), 'error');
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};

		//根据id获取完整的信息
		const getData = (id: any) => {
			userApi.Detail(id).then(async (rs) => {
				state.ruleForm = Object.assign({}, rs.data);
				state.ruleForm.orgArr = state.ruleForm.orgs[0];
				if (state.ruleForm.escalation=="0")
				{	
					state.ruleForm.escalation="";
				}
				state.orgData = rs.data.additionalProperties.CompanyList;
				state.customerData = rs.data.additionalProperties.ProjectList;

				await roleApi.GetList().then((rs) => {
					state.roleData = rs.data;
					state.oldRoleData = [...rs.data];

					state.ruleForm.roleArr = [];
					state.ruleForm.lastRoleArr = [];
					if (state.ruleForm.userType == 'End User') {
						state.roleData = [...state.oldRoleData];		
						if(state.ruleForm.roles && state.ruleForm.roles.length>0){
							state.ruleForm.roleArr = state.ruleForm.roles;
							state.ruleForm.lastRoleArr = state.ruleForm.roles;
						}else{
							const roles = [9] as any;
							state.ruleForm.roleArr = roles;
							state.ruleForm.lastRoleArr = roles;
						}
						state.isShowPrivateTicket = false;
						state.roleData = state.oldRoleData.filter((item) => {
							return item.id != 1 && item.id != 4 && item.id != 8;
						});
					} else {
						state.isShowPrivateTicket = true;
						state.roleData = state.oldRoleData.filter((item) => {
							return state.ruleForm.id == 1 ? item.id != 9 : item.id != 9 && item.id != 4;
						});

						state.ruleForm.roleArr = state.ruleForm.roles;
						state.ruleForm.lastRoleArr = state.ruleForm.roles;
					}
				});

				state.ruleForm.customerArr = state.ruleForm.projects;

				if (state.ruleForm.userType != 'Staff Member') {
					state.ruleForm.masterUserTypeArr = state.ruleForm.masterUserTypes;

					bindMasterUserType(state.ruleForm.orgArr);
				}

				state.ruleForm.customerArr_Init = state.ruleForm.customerArr;

				var dictArr = ['UserType', 'Time Zone'];
				dictItemApi.Many(dictArr).then((rs: any) => {
					state.userTypeData = rs.data?.find((a: any) => {
						return a.dictValue === 'UserType';
					})?.items;

					state.timeZoneData = rs.data?.find((a: any) => {
						return a.dictValue === 'Time Zone';
					})?.items;
				});
			});
		};

		//删除纪录
		const onDelete = () => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal: false,
			}).then(() => {
				state.deleteLoading = true;
				userApi
					.DeleteByKey(state.ruleForm.id)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.deleteLoading = false;
					});
			});
		};

		const onUnLock = () => {
			userApi
				.UnLock(state.ruleForm.id)
				.then((rs: any) => {
					ElMessage.success('Succeed');
					context.emit('fetchData');
					closeDialog();
				})
				.catch((rs) => {
					ElMessage.error(rs.resultMsg || rs.toString());
				})
				.finally(() => {
					state.deleteLoading = false;
				});
		};

		const onInitForm = () => {
			state.isShowDialog = true;

			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}

			state.ruleForm = {
				id: 0,
				mobile: '',
				email: '',
				userName: '',
				realName: '',
				status: 0,
				departmentId: 0,
				remark: '',
				errorCount: 0,
				name: '',
				sex: 0,
				age: 0,
				birth: '',
				address: '',
				positionId: 0,
				lastLoginIp: '',
				emailValidation: false,
				phoneValidation: false,
				avatar: '',
				namePinYin: '',
				master_User: false,
				escalation:0,
			};

			state.orgData = [];
			state.masterUserTypeOrgData = [];

			state.ruleForm.masterUserTypeArr = [];
			state.positionData = [];
			state.roleData = [];
			state.customerData = [];
			state.userDataSelect = [] as any;
			state.userData = [];
			state.userBptsData = [];
			state.ruleForm.roleArr = [];
			state.ruleForm.customerArr = [];
		};

		const selectAll = (val: any) => {
			if (checked1.value) {
				state.ruleForm.customerArr = state.customerData.map((item: any) => {
					return item.orgId
				});
			} else {
				state.ruleForm.customerArr = []
			}
		}

		const onVisible = () => {
			userRef.value.blur();
			selectUserRef.value.openDialog({ multiple: false });
		};

		const setSelectItem = (items: any) => {
			const newItem = [...items];
			state.userData = items;
			state.userDataSelect = newItem.map((a) => {
				return a.itemId;
			});
		};

		const getTreeName = (treeList: any, id: any) => {
			for (let i = 0; i < treeList.length; i++) {
				let treeItem = treeList[i];
				if (treeItem.orgId === id) {
					return treeItem;
				} else {
					if (treeItem.children && treeItem.children.length > 0) {
						let res: any = getTreeName(treeItem.children, id);
						if (res) {
							return res;
						}
					}
				}
			}
		};

		const isIndeterminate = ref(true)

		watch(
			() => state.ruleForm.customerArr,
			(newValue, oldValue) => {
				var allData = state.customerData.map((item: any) => {
					return item.orgId
				});

				if (newValue != undefined && allData.length > 0) {
					// 检查 newValue 是否包含所有的 allData 元素
					const allSelected = allData.every(item => newValue.includes(item));

					const checkedCount = newValue.length

					checked1.value = allSelected;

					// 当部分选中时，isIndeterminate 为 true
					isIndeterminate.value = checkedCount > 0 && !allSelected;
				} else {
					checked1.value = false;
					isIndeterminate.value = false;
				}
			}
		);

		watch(
			() => state.ruleForm.orgArr,
			(newValue, oldValue) => {
				if (newValue == undefined || newValue != oldValue) {
					state.roleData.length = 0;


					if (state.ruleForm.masterUserTypeArr != undefined) {
						state.ruleForm.masterUserTypeArr.length = 0;
						state.ruleForm.masterUserTypeArr = [];
					}

					if (state.ruleForm.roleArr != undefined) {
						state.ruleForm.roleArr.length = 0;
						state.ruleForm.roleArr = [];						
						state.ruleForm.lastRoleArr = [];
					}

					if (state.ruleForm.customerArr != undefined) {
						state.ruleForm.customerArr.length = 0;
						state.ruleForm.customerArr = []
					}

					state.ruleForm.userType = ''
				}

				// if (parseThanZero(oldValue) || !state.dialogParams.id) {			
				let matchOrg = getTreeName(state.orgData, newValue);

				if (matchOrg != undefined) {
					state.ruleForm.userType = matchOrg.organizationType;

					if (state.ruleForm.userType == 'End User') {
						state.roleData = [...state.oldRoleData];
						const roles = [9] as any;
						state.ruleForm.roleArr = roles;
						state.ruleForm.lastRoleArr = roles;
						state.isShowPrivateTicket = false;
						state.roleData = state.oldRoleData.filter((item) => {
							return item.id != 1 && item.id != 4 && item.id != 8;
						});
					} else {
						state.isShowPrivateTicket = true;
						state.roleData = state.oldRoleData.filter((item: any) => {
							return state.ruleForm.id == 1 ? item.id != 9 : item.id != 9 && item.id != 4;
						});
					}

					if (state.ruleForm.userType == 'Staff Member') {
						organizationApi.Tree({ IncludeProject: 1 }).then((rs) => {
							state.customerData = rs.data;
						});
					} else {
						organizationApi.Tree({ IncludeProject: 1, IncludeOrgId: newValue }).then((rs) => {
							state.customerData = rs.data;
						});

						bindMasterUserType(newValue);
					}
				}
				// }
			}
		);

		const bindMasterUserType = (orgId: any) => {
			let parentOrgId = 0;

			for (let i = 0; i < state.orgData.length; i++) {
				let orgItem = state.orgData[i];
				let orgItemArr = [orgItem];

				let matchOrgItem = getTreeName(orgItemArr, orgId);

				if (matchOrgItem != undefined) {
					parentOrgId = orgItem.orgId;
					break;
				}
			}

			if (orgId != undefined) {
				if (parseThanZero(parentOrgId)) {
					organizationApi.Tree({ IncludeOrgId: parentOrgId }).then((rs) => {
						state.masterUserTypeOrgData = rs.data;
					});
				}
			}
		};

		const disableAssignRole = computed(() => {
			let disableResult = false;
			// if (state.ruleForm.userType == 'End User' || state.ruleForm.id == 1) {
			// 	disableResult = true;
			// }
			return disableResult;
		});

		const ClickExpandNode = (data: any) => {
			const node = localTree.value.getNode(data.orgId);
			setChildNodeExpand(node);
		};

		const ClickExpandNode1 = (data: any) => {
			const node = localMasterUserTypeTree.value.getNode(data.orgId);
			setChildNodeExpand(node);
		};

		// 通过check的回调里面获取节点id,再获取节点的node对象
		const checkChange = (data: any) => {
			const node = localTree.value.getNode(data.orgId);
			// setNode(node);
		};

		const checkMasterUserChange = (data: any) => {
			const node = localMasterUserTypeTree.value.getNode(data.orgId);
			setNode(node);
		};

		//递归设置子节点和父节点
		const setNode = (node: any) => {
			if (node.checked) {
				setChildNode(node);
				setParentNode(node);
			}
		};

		//递归设置父节点全部取消选中
		const setParentNode = (node: any) => {
			if (node.parent) {
				for (const key in node) {
					if (key === 'parent') {
						node[key].checked = false;

						setParentNode(node[key]);
					}
				}
			}
		};

		//递归设置子节点全部取消选中
		const setChildNode = (node: any) => {
			if (node.childNodes && node.childNodes.length) {
				node.childNodes.forEach((item: any) => {
					item.checked = false;
					item.expanded = true;

					setChildNode(item);
				});
			}
		};

		//递归设置子节点全部展开
		const setChildNodeExpand = (node: any) => {
			if (node.childNodes && node.childNodes.length) {
				node.childNodes.forEach((item: any) => {
					item.expanded = true;

					setChildNodeExpand(item);
				});
			}
		};

		const onChangePwd = () => {
			form.userId = '';
			form.pwd = '';
			form.confirmPwd = '';

			form.userId = state.ruleForm.id;
			changePwdRef.value = true;
		};

		const onSubmitChangePwd = () => {
			if (form.pwd != form.confirmPwd) {
				ElMessage.error(t('message.page.pwdMatch'));
				return;
			}

			proxy.$refs.changePwdFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}

				var obj = { userId: form.userId, pwd: form.confirmPwd };
				state.saveLoading = true;
				userApi
					.UserResetPassword(obj)
					.then(() => {
						ElMessage.success('Succeed');
						changePwdRef.value = false;
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};

		const handleClose = (formEl: { resetFields: () => void; }) => {
			formEl.resetFields();

			changePwdRef.value = false;
		};

		const ChangeRole = (value: any) => {
			//暂时撤销这个修改

			// if (state.ruleForm.id == 0) {
			// 	if (value.includes(8)) {
			// 		state.ruleForm.customerArr = state.customerData.map((item: any) => {
			// 			return item.orgId
			// 		});
			// 	} else {
			// 		state.ruleForm.customerArr = []
			// 	}
			// }
			//启动修改
			if (state.ruleForm.userType == 'End User') {
				if(!value.includes(9)) {
					// 恢复到上一次的值
					state.ruleForm.roleArr = state.ruleForm.lastRoleArr;
				}else{
					// 更新上一次的值
					state.ruleForm.lastRoleArr = [...value];
				}
			}
		}

		const checkChangeCustomer = (data: any) => {
			if (data.disabled) {
				return;
			}

			const checkKey = localTree.value.getCheckedKeys();
			const customerData = state.customerData;

			// 递归查找节点在树中的父节点
			const findParentNode = (tree: any[], targetId: number): number | null => {
				for (const node of tree) {
					// 检查当前节点的子节点
					if (node.children?.some((child: any) => child.orgId === targetId)) {
						return node.orgId;
					}
					// 如果当前节点有子节点，递归查找
					if (node.children) {
						const found = findParentNode(node.children, targetId);
						if (found !== null) {
							return found;
						}
					}
				}
				return null;
			};

			// 找出所有被选中的节点中的顶级节点（没有被选中的父节点的节点）
			const filteredKeys = checkKey.filter(nodeId => {
				// 查找该节点的父节点
				const parentId = findParentNode(customerData, nodeId);
				// 如果没有父节点，或者父节点没有被选中，就保留这个节点
				return parentId === null || !checkKey.includes(parentId);
			});

			state.ruleForm.customerArr = filteredKeys;
		};

		const clearCompany = () => {
			state.customerData.length = 0;
		};

		return {
			openDialog,
			onVisible,
			setSelectItem,
			userRef,
			selectUserRef,
			closeDialog,
			openSelectUser,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			...toRefs(state),
			disableAssignRole,
			checkChange,
			localTree,
			onChangePwd,
			onSubmitChangePwd,
			handleClose,
			changePwdRef,
			form,
			changePwdFormRef,
			changeRules,
			checkMasterUserChange,
			localMasterUserTypeTree,
			ClickExpandNode,
			ClickExpandNode1,
			onUnLock,
			selectAll,
			checked1,
			isIndeterminate,
			ChangeRole,
			checkChangeCustomer,
			clearCompany
		};
	},
});
</script>

<style scoped lang="scss">
.el-icon img {
	height: 1em;
	width: 1em;
	cursor: pointer;
}
</style>