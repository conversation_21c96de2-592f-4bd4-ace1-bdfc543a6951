<template>
	<el-dialog :title="title" v-model="isShowDialog" :close-on-click-modal="false" @close="closeDialog"
		:destroy-on-close="true" width="800">
		<div class="ticket-info_layout" :style="{ height: 'calc(90vh - 250px)' }">
			<el-container style="height: 100%">
				<el-container class="tk-container">
					<el-header class="tk-header" v-if="ticketId > 0">
						<el-form label-width="100px" size="default">
							<el-row :gutter="35">
								<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb10">
									<el-form-item :label="$t('message.ticketFields.subject')">
										<el-input v-model="formData.ticket_Subject" disabled />
									</el-form-item>
								</el-col>
							</el-row>
						</el-form>
					</el-header>
					<el-main class="tk-main new-tk-main" v-if="ticketId > 0">
						<!--  <el-scrollbar>
                        </el-scrollbar> -->
						<el-timeline>
							<el-timeline-item :timestamp="item.createdAt" placement="top"
								v-for="item in formData.ticketContents" :key="item.ticketContentId">
								<div class="tl-item">
									<div class="timeline-avatar">
										<el-avatar :size="50"
											src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" />
									</div>
									<div class="timeline-content">
										<div class="timeline-conte_name">
											<h5>{{ item.createdByName }}</h5>
											<el-tag v-if="item.ticketContentType === 'Internal'"
												class="timeline-conte_type" size="small" type="warning">{{
													$t('message.ticketFields.internal')
												}}</el-tag>
											<el-tag v-if="item.ticketContentType === 'Comment'"
												class="timeline-conte_type" size="small">{{
													$t('message.ticketFields.comment')
												}}</el-tag>
											<el-tag v-if="item.ticketContentType === 'Public'"
												class="timeline-conte_type" size="small" type="success">{{
													$t('message.ticketFields.public')
												}}</el-tag>
											<el-tag v-if="item.ticketContentType === 'Emails'"
												class="timeline-conte_type" size="small" type="success">{{
													$t('message.ticketFields.Emails')
												}}</el-tag>
										</div>
										<div v-if="isNotEmptyOrNull(item.assginToName)">
											<label class="timeline-content_to">To</label><span>：</span>{{
												item.assginToName }}
										</div>
										<div v-if="item.isAttachment === 'Y'" style="position: relative">
											<el-icon class="ele-icon">
												<ele-Link />
											</el-icon>
											<span>：</span>
											<el-popover :visible="item.popoverShow"
												@update:visible="(visible: boolean) => UpdateVisible(item, visible)"
												placement="bottom-start" :width="400" trigger="click"
												:popper-options="{ modifiers: [{ name: 'offset', options: { offset: [80, 0] } }] }">
												<!-- <el-popover placement="right" 
												:width="400" trigger="click"> -->
												<template #reference>
													<el-link type="primary">{{ $t('message.ticketButtons.attachments')
													}} ({{ item.ticketAttachments.length }})</el-link>
												</template>
												<el-table :data="item.ticketAttachments">
													<el-table-column property="attachment_Original_File_Name"
														label="Name">
														<template #default="{ row }">
															<el-icon class="ele-icon"><ele-Download /></el-icon>
															<el-link class="link_text" @click="downloadFile(row)">
																{{ row.attachment_Original_File_Name }}
															</el-link>
															<el-tag v-if="previewFile(row.attachment_File_Ext)"
																class="ml-2 ml10" type="warning"
																style="cursor: pointer;"
																@click="handleFilePreview(item, row)">{{
																	$t('message.ticketLabels.preview') }}</el-tag>
														</template>
													</el-table-column>
												</el-table>
												<el-image-viewer v-if="previewVisibleRef" :url-list="previewSrcList"
													:initial-index="previewIndexRef" hide-on-click-modal
													@close="imagePreviewVisible" />
											</el-popover>
										</div>
										<el-image-viewer v-if="showPreview" :url-list="srcList" hide-on-click-modal
											:initial-index="0" @close="showPreview = false" />
										<p v-html="item.ticketContent" @click="handleImageClick"
											:class="{ 'internal-content': item.ticketContentType === 'Internal' }"></p>

										<el-divider />
									</div>
								</div>
							</el-timeline-item>
						</el-timeline>
					</el-main>
					<el-footer class="tk-footer new-footer-default" :class="footerClass">
						<el-collapse @change="commentBoxCollapse">
							<el-collapse-item :name="commentCollapseItemName">
								<template #title>
									<span class="comment-footer">{{ $t('message.ticketButtons.addComment') }}</span>
								</template>
								<el-card shadow="never" class="card-footer" @dragenter="handleDragEnter"
									@dragleave="handleDragLeave" @drop="handleDrop" @dragover.prevent>
									<el-form ref="ruleFormRef" :model="formData" :rules="rules" label-width="100px"
										size="default">
										<el-row :gutter="5">
											<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb10">
												<el-form-item prop="assignToUserId">
													<!-- :headers="headers" -->
													<el-upload v-model:file-list="fileList" class="upload-demo" drag
														:action="actionStr" accept="Image/jpg"
														:before-upload="beforeImageUpload" :limit="10" multiple
														@change="handleUploadChange" :http-request="customUpload">
														<el-row
															style="display: flex; justify-content: center; align-items: center;">
															<el-icon class="uploader-icon">
																<ele-Paperclip />
															</el-icon>
															{{ isDropFileHere ?
																$t('message.ticketButtons.dropFilesHere')
																: $t('message.ticketButtons.dragUploadFilesLessThan') }}
														</el-row>

													</el-upload>
												</el-form-item>
											</el-col>
											<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb10">
												<el-form-item :label="$t('message.ticketFields.content')">
													<!-- <el-input v-model="formData.ticket_Content" type="textarea"
														:placeholder="contentPlaceholder"
														:autosize="{ minRows: 4, maxRows: 13 }"></el-input> -->
													<EditorNote v-model:modelValue="editorNote.htmlVal"
														v-model:get-text="editorNote.textVal"
														:disable="editorNote.disable" :height="editorNote.height"
														:mode="editorNote.mode" />
												</el-form-item>
											</el-col>
										</el-row>
									</el-form>
								</el-card>
							</el-collapse-item>
						</el-collapse>
					</el-footer>
				</el-container>
			</el-container>
		</div>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="onCancel" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
				<el-button :loading="loading" type="primary" @click="onSubmit" size="small">{{
					$t('message.page.buttonSave') }}</el-button>
			</span>
		</template>
	</el-dialog>
	<FilePreview ref="ticketFileDialogRef"></FilePreview>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, computed, getCurrentInstance, ref, defineAsyncComponent, provide } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';

import { CmTicketsAddOrUpdateDto } from '/@/types/ticketDtos';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import { isNotEmptyOrNull } from '/@/utils';
import { formatPast } from '/@/utils/formatTime';
import cmTicketsApi from '/@/api/cmTickets/index';
import { Local, Session } from '/@/utils/storage';
import request from '/@/utils/request';
import FilePreview from './filePreview.vue';
const EditorNote = defineAsyncComponent(() => import('/@/components/editor/noteV5.vue'));


export default defineComponent({
	name: 'TicketCommentView',
	components: { EditorNote, FilePreview },
	props: {
		showCommentView: Boolean,
		showCommentID: String,
	},
	emits: ['closedComment'],
	setup(props, context) {


		const { proxy } = getCurrentInstance() as any;
		const { t } = useI18n();
		const stores = useUserInfo();
		const { userInfos } = storeToRefs(stores);
		// 获取用户信息 vuex
		const currentUser = computed(() => {
			return userInfos.value;
		});
		const BASE_URL = computed(() => {
			const appSettings = proxy?.$appSettings;
			return appSettings?.BASE_URL || import.meta.env.VITE_API_URL;
		});

		const showPreview = ref(false)
		let srcList = ref([]) as any;

		const isUploading = ref(false);

		const previewVisibleRef = ref(false);
		const previewIndexRef = ref(0);
		const previewSrcList = ref();
		const ticketFileDialogRef = ref();
		const fileData = ref({});
		const isDropFileHere = ref(false);
		const dragCounter = ref(0);

		const contentPlaceholder =
			'Please enter any additional information, relevant details or updates, or share your thoughts and feedback about the ticket in the note field.';
		const actionStr = `${BASE_URL.value}Api/Files/UpLoadV2`;
		let footerClass = ref(['']);
		const commentCollapseItemName = 'commentBox';

		const downloadFile = (row: any) => {
			request({
				url: `${BASE_URL.value}api/files/ticketFile/${row.id}`,
				method: 'get',
				responseType: 'blob'
			})
				.then((response: any) => {
					// 创建 Blob URL
					const blob = new Blob([response], { type: response.type || 'application/octet-stream' });
					const url = window.URL.createObjectURL(blob);

					// 创建下载链接
					const link = document.createElement('a');
					link.style.display = 'none';
					link.href = url;
					link.download = row.attachment_Original_File_Name; // 可以根据需要设置文件名

					// 触发下载
					document.body.appendChild(link);
					link.click();

					// 清理
					document.body.removeChild(link);
					window.URL.revokeObjectURL(url);
				})
				.catch((error: any) => {
					console.error('Download failed:', error);
					// 可以添加用户友好的错误提示
					ElMessage.error('Download failed');
				});
		};

		const handleFilePreview = (item: any, row: any) => {
			if (compareImg(row.attachment_File_Ext)) {
				// 过滤出图片类型的附件
				previewSrcList.value = item.ticketAttachments.filter((item: any) => compareImg(item.attachment_File_Ext)).map((item: any) => `${BASE_URL.value}` + item.attachment_File_Name)
				// 设置预览图片的索引
				previewIndexRef.value = previewSrcList.value.indexOf(`${BASE_URL.value}` + row.attachment_File_Name);
				// 显示图片预览
				previewVisibleRef.value = true;
			} else {
				fileData.value = row;
				// 显示文件对话框
				ticketFileDialogRef.value.openDialog(row);
				// window.open(`${BASE_URL.value}`+row.attachment_File_Name,'_blank');
			}
		};
		const previewFile = (type: string) => {
			// 判断是否为图片类型的附件
			const fileTypes = ['bmp', 'jpg', 'png', 'gif', 'svg', 'webp', 'ico', 'jpeg', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'mp3', 'mp4'];
			return fileTypes.includes(type.toLowerCase());
		};
		//判断类型为图片的方法
		const compareImg = (type: string) => {
			const imageTypes = ['bmp', 'jpg', 'png', 'gif', 'svg', 'webp', 'ico', 'jpeg'];
			return imageTypes.includes(type.toLowerCase());
		};
		const imagePreviewVisible = () => {
			previewVisibleRef.value = false;
			state.formData.ticketContents.forEach((item: any) => item.popoverShow = false);
		}
		const onInitForm = () => {
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.fileList = [];
			footerClass.value = [];
			state.editorNote.htmlVal = '';

		};

		const closeDialog = () => {
			onInitForm();
			context.emit('closedComment');


		};

		const requestData = async (ticketId: number) => {
			if (ticketId <= 0) return;

			cmTicketsApi.Detail(ticketId).then((rs) => {
				const obj = rs.data;
				state.formData = obj;
				// state.formData.ticketContents.forEach((item:any)=>item.popoverShow=false);
			});
		};

		const ticketId = computed(() => {
			const showCommentIDProps = parseInt(props.showCommentID || '0', 10);
			requestData(showCommentIDProps);
			return showCommentIDProps;
		});

		const isShowDialog = computed(() => {
			return props.showCommentView;
		});

		const onCancel = () => {
			closeDialog();
		};
		const handleClick = (event) => {
			const src = event.src;
			srcList.value = [];
			srcList.value.push(src);
			showPreview.value = true;
		}
		const handleImageClick = (event) => {
			if (event.target.tagName === 'IMG') {
				const alt = event.target.alt;
				if (alt != "noView") {
					handleClick(event.target);
				}
			}
		};

		//获取表单信息
		const getFormData = () => {
			state.formData.id = ticketId.value;
			// 使用展开运算符 ... 创建新对象
			let req: any = { ...state.formData };
			req.assigToId = [{ nodeIdGuid: req.nodeId, nodeType: req.assigToType, nodeName: req.assigToName }];
			req.FromStatus = req.ticketStatusFlag;
			req.ticketContentsInfo = {
				//ticket_Content: state.formData.ticket_Content.replace(/\n/g, '<br/>'),
				ticket_Content: state.editorNote.htmlVal,
				ticket_Content_Type: 'Comment',
			};

			//附件
			req.ticketAttachments = state.fileList.map((item) => ({
				attachment_File_Id: item.id,
				attachment_File_Name: item.raw.response.data[0].fileUrl,
				attachment_Original_File_Name: item.name,
				attachment_File_Ext: item.name.substring(item.name.lastIndexOf('.') + 1),
			}));

			//删除多余的属性
			delete req.ticketContents;
			return req;
		};

		const onSubmit = async () => {
			try {
				await Promise.all([proxy.$refs.ruleFormRef.validate()]);

				if (isUploading.value) {
					ElMessage.error('Please wait, file is uploading');
					return;
				}

				if (state.editorNote.textVal == "") {
					ElMessage.error('Please enter the content.');
					return;
				}

				state.loading = true;

				const newformData = getFormData();
				//console.log("newformData:",newformData)


				await cmTicketsApi
					.SaveComment(newformData)
					.then((rs) => {
						ElMessage.success(t('message.page.saveSuccess'));
						state.fileList = [];
						proxy.$refs.ruleFormRef.resetFields();

						requestData(ticketId.value);
						closeDialog();
					})
					.finally(() => {
						state.loading = false;
					});
			} catch (err: any) {
				// 如果有任何一个表单未通过验证，处理错误
				console.log(err);
				if (err.message) {
					ElMessage.error(err.message);
				}
				return false;
			} finally {
				state.loading = false;

			}
		};

		const state = reactive({
			formData: {
				ticket_Content_Type: 'Public',
			} as CmTicketsAddOrUpdateDto,
			rules: {
				ticket_Category: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'change' }],
				ticket_Subject: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' }],
				ticket_Priority: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'change' }],
				ticket_First_Name: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' }],
				ticket_Last_Name: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' }],
				ticket_Customer: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'change' }],
				ticket_Customer_Email: [
					,
					{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' },
					{ pattern: /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, message: 'Invalid email', trigger: ['blur', 'change'] },
				],
				ticket_Customer_Phone: [{ required: false, message: t('message.ticketValidates.required'), trigger: 'blur' }],
				nodeId: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'change' }],
				ticket_Content: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' }],
				ticketContentType: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'change' }],
			},
			contentPlaceholder: contentPlaceholder,
			currentUser: currentUser,
			fileList: [],
			contentTypeOpts: [],
			// 设置上传的请求头部
			headers: {
				Authorization: `Bearer ${Local.get('token')}`,
			},
			actionStr: actionStr,
			title: t('message.ticketButtons.viewComments'),
			loading: false,
			footerClass: footerClass,
			commentCollapseItemName: commentCollapseItemName,
			editorNote: {
				placeholder: '',
				htmlVal: '',
				textVal: '',
				disable: false,
				height: '150px',
				mode: 'simple', // 'default' or 'simple'
			},
		});

		state.formData.ticketContentType = 'Public';

		const commentBoxCollapse = (val: string[]) => {
			footerClass.value = [];
			if (val.indexOf(commentCollapseItemName) > -1) {
				footerClass.value = ['new-footer-expand'];
			}
		};

		const beforeImageUpload = (file: any) => {
			//监控文件上传文本提示
			dragCounter.value = 0;
			isDropFileHere.value = false;
			const maxSize = file.size / 1024 / 1024 <= 20;
			if (!maxSize) {
				ElMessage.warning(t('message.page.uploadLimit'));
				return false;
			}
		};

		//上传成功时触发（目前没有用到）
		//直接获取 state.fileList 可以得到 上传的文件列表了
		const onUploadSuccess = (result: any, file: any) => {
			if (result == undefined) {
				return;
			}

			if (result.resultCode !== 200) {
				return;
			}

			if (state.formData.ticketAttachments != undefined) {
				state.formData.ticketAttachments.some((a) => a.attachment_File_Id === file.id);
			}
		};

		const handleUploadChange = (file: any, fileList: any) => {
			if (file.status === 'ready') {
				isUploading.value = true;
			} else if (fileList.every((f: { status: string }) => ['success', 'fail'].includes(f.status))) {
				isUploading.value = false;
			}
		};

		const customUpload = async (options: any) => {
			const { file, onSuccess, onError, onProgress } = options;

			const formData = new FormData();
			formData.append('file', file);

			try {
				const token = Local.get('token');

				const response = await request.post(state.actionStr, formData, {
					headers: {
						Authorization: `Bearer ${token}`,
						'Content-Type': 'multipart/form-data',
					},
					onUploadProgress: (event: any) => {
						if (event.total > 0) {
							onProgress({ percent: (event.loaded / event.total) * 100 }, file);
						}
					},
				});

				file.response = response

				onSuccess(response, file, state.fileList);
			} catch (error: any) {
				onError(error, file, state.fileList);
			}
		};

		const handleDragEnter = (event: any) => {
			event.preventDefault();
			dragCounter.value++;
			if (dragCounter.value === 1) {
				isDropFileHere.value = true;
			}
		};
		const handleDragLeave = (event: any) => {
			event.preventDefault();
			dragCounter.value--;
			if (dragCounter.value === 0) {
				isDropFileHere.value = false;
			}
		};
		const handleDrop = (event: any) => {
			event.preventDefault();
			//监控文件上传文本提示
			dragCounter.value = 0;
			isDropFileHere.value = false;
		};
		const UpdateVisible = (item: any, val: boolean) => {
			if (item.popoverShow !== val) {
				console.log('UpdateVisible', item);
				console.log('UpdateVisible', val);
				item.popoverShow = val;
			}
		};
		// provide('UpdateVisible', UpdateVisible);

		return {
			...toRefs(state),
			ticketId,
			isShowDialog,
			isNotEmptyOrNull,
			downloadFile,
			formatPast,
			beforeImageUpload,
			onUploadSuccess,
			closeDialog,
			onCancel,
			onSubmit,
			commentBoxCollapse,
			handleUploadChange,
			customUpload,
			handleFilePreview,
			previewFile,
			handleDragEnter,
			handleDragLeave,
			handleDrop,
			UpdateVisible,
			imagePreviewVisible,
			previewVisibleRef,
			previewIndexRef,
			previewSrcList,
			ticketFileDialogRef,
			fileData,
			isDropFileHere,
			showPreview,
			srcList,
			handleImageClick,
		};
	},
});
</script>

<style>
.list-page-layout .el-main,
.tk-main,
.new-tk-main {
	text-align: left;
}

.uploader-icon {
	width: 24px;
	height: 24px;
	font-size: 20px;
}

.card-footer {
	height: 200px;
	width: 100%;
	overflow-y: auto;
}

.el-collapse-item__header .comment-footer {
	font-weight: 700;
	color: #303133;
}

.ticket-info_layout .tk-footer.new-footer-default {
	height: 50px;
}

.ticket-info_layout .tk-footer.new-footer-default.new-footer-expand {
	height: 250px;
}

.upload-demo {
	width: 100%;
	/* color: #fff; */
}

.el-upload {
	--el-upload-dragger-padding-horizontal: 5px;
	--el-upload-dragger-padding-vertical: 0px;
	box-shadow: none;
	outline: none;
}

.el-upload-dragger {
	background-color: lightgray;
	border: 0px;
}
</style>
<style scoped lang="scss">
::v-deep .el-image-viewer__close {
	background-color: #313233;
}

::v-deep .el-image-viewer__mask {
	opacity: 0.01 !important;
	/* 设置遮罩层透明度为50% */
}
</style>