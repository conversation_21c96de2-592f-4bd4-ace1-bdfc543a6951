import { defineStore } from 'pinia';
import Cookies from 'js-cookie';
import { Session, Local } from '/@/utils/storage';
import loginApi from '/@/api/login';

/**
 * 用户信息
 * @methods setUserInfos 设置用户信息
 */
export const useUserInfo = defineStore('userInfo', {
	state: (): UserInfosState => ({
		userInfos: {} as UserInfos,
	}),
	actions: {
		async setUserInfos() {
			// 存储用户信息到浏览器缓存
			if (Local.get('userInfo')) {
				this.userInfos = Local.get('userInfo');
			} else {
				const userInfos = <UserInfos>await this.getApiUserInfo();
				this.userInfos = userInfos;
			}
		},
		async getUserInfos() {
			await this.setUserInfos();
			return this.userInfos;
		},
		// 模拟接口数据
		// https://gitee.com/lyt-top/vue-next-admin/issues/I5F1HP
		async getApiUserInfo(): Promise<UserInfos> {
			const token = Local.get('token');

			if (!token) throw 'Token Missing';

			const rs = await loginApi.GetMyInfo();
			this.userInfos = {
				...this.createDefaultUserInfos(),
				userId: rs.data.id,
				userName: rs.data.name,
				// mobile: rs.data.mobile,
				// email: rs.data.email,
				photo: rs.data.photo,
				lastLoginTime: rs.data.lastLoginTime,
				firstName: rs.data.firstName,
				lastLoginIp: rs.data.lastLoginIp,
				roles: rs.data.roleNames,
				authBtnList: rs.data.permissions,
				userType: rs.data.userType,
				language: rs.data.language ?? 'en',
				projects: rs.data.projects,
				privateTicket: rs.data.privateTicket,
				nodeId: rs.data.nodeId,
				systemUrl: rs.data.systemUrl,
				needToChangePwd: rs.data.needToChangePwd,
				time: Date.now(),
			};
			Local.set('userInfo', this.userInfos);
			return this.userInfos;
		},
		createDefaultUserInfos(): UserInfos {
			return {
				userId: '',
				userName: '',
				mobile: '',
				email: '',
				photo: '',
				lastLoginTime: 0,
				firstName: '',
				lastLoginIp: '',
				roles: [],
				authBtnList: [],
				userType: '',
				language: '',
				projects: [],
				privateTicket: '',
				nodeId: '',
				systemUrl: '',
				time: 0,
				needToChangePwd: false,
			};
		},
	},
});
